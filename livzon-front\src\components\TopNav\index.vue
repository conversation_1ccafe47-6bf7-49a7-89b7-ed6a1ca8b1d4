<template>
	<div class="top-nav">
		<!-- PC端菜单 -->
		<el-menu
			:default-active="activeMenu"
			background-color="#fff"
			:text-color="variables.menuColor"
			:active-text-color="theme"
			mode="horizontal"
			:ellipsis="false"
			class="pc-menu"
		>
			<sidebar-item
				v-for="(route, index) in sidebarRouters"
				:key="route.path + index"
				:item="route"
				:base-path="route.path"
			/>
		</el-menu>
	</div>
</template>

<script setup>
import { computed } from "vue";
import { useRoute } from "vue-router";
import SidebarItem from "@/layout/components/Sidebar/SidebarItem.vue";
import variables from "@/assets/styles/variables.module.scss";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";

const route = useRoute();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();

const sidebarRouters = computed(() => permissionStore.sidebarRouters);
const theme = computed(() => settingsStore.theme);

const activeMenu = computed(() => {
	const { meta, path } = route;
	if (meta.activeMenu) {
		return meta.activeMenu;
	}
	return path;
});
</script>

<style lang="scss">
/**
 * 顶部导航栏组件
 * @module components/TopNav
 * @description 展示主导航菜单，支持主题色切换，带有菜单项间分隔竖线
 */
@import "@/assets/styles/variables.module.scss";

/**
 * @description 允许菜单栏在移动端横向滚动，便于查看全部菜单项
 */
.top-nav {
	overflow-x: auto;
	overflow-y: hidden;
	-webkit-overflow-scrolling: touch;
	white-space: nowrap;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	background-color: #fff !important;

	:deep(.el-menu) {
		border: none;
		height: 100%;
		display: flex;
		align-items: center;
	}

	:deep(.el-menu-item),
	:deep(.el-sub-menu__title) {
		height: 50px;
		line-height: 50px;
		border-right: 1px solid #dcdfe6;
		margin-right: -1px;
		position: relative;

		&:last-child {
			border-right: none;
		}
	}
}

/* 确保菜单项样式正确 */
:deep(.el-menu--horizontal) {
	display: flex;
	align-items: center;
	background: #fff;
	border-bottom: none;
	min-width: max-content;

	> .el-menu-item,
	> .el-sub-menu > .el-sub-menu__title {
		height: 50px;
		line-height: 50px;
		position: relative;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1px;
			height: 20px;
			background-color: #dcdfe6;
			z-index: 1;
		}
		
		&:first-child::before {
			display: none;
		}
	}
}

:deep(.el-menu--horizontal > .el-menu-item),
:deep(.el-menu--horizontal > .el-sub-menu > .el-sub-menu__title) {
	padding: 0 32px !important;
	width: auto !important;
	color: #222 !important;
	font-weight: 500 !important;
	background: #fff !important;
	opacity: 1 !important;
	text-align: center;
}

/**
 * @description 顶部菜单项之间的分隔线
 */
:deep(.el-menu.el-menu--horizontal) {
	.el-menu-item,
	.el-sub-menu .el-sub-menu__title {
		&::after {
			content: "";
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1px;
			height: 20px;
			background: #dcdfe6;
		}

		&:last-child::after {
			display: none;
		}
	}
}

/**
 * @description 顶部菜单项分隔竖线
 */
.el-menu--horizontal {
	.el-menu-item:not(:last-child),
	.el-sub-menu:not(:last-child) .el-sub-menu__title {
		border-right: 1px solid #dcdfe6;
	}
}

:deep(.el-menu--horizontal > .el-menu-item.is-active),
:deep(.el-menu--horizontal > .el-menu-item:hover),
:deep(.el-menu--horizontal > .el-sub-menu > .el-sub-menu__title:hover),
:deep(.el-menu--horizontal > .el-sub-menu.is-active > .el-sub-menu__title) {
	color: #1765d5 !important;
	font-weight: 600 !important;
	background: #f5f7fa !important;
	opacity: 1 !important;
}

/**
 * @description 顶部导航下拉子菜单极简美观风格
 */
:deep(.el-menu--popup) {
	min-width: 180px !important;
	background: #fff !important;
	border-radius: 8px !important;
	box-shadow: 0 6px 24px 0 rgba(0,0,0,0.08) !important;
	padding: 4px 0 !important;
	border: none !important;
}

:deep(.el-menu--popup .el-menu-item) {
	padding: 0 20px !important;
	height: 38px !important;
	line-height: 38px !important;
	font-size: 15px !important;
	color: #222 !important;
	border-radius: 6px !important;
	margin: 2px 6px !important;
	font-family: 'PingFang SC', 'Microsoft YaHei', Arial, 'Helvetica Neue', sans-serif !important;
	font-weight: 500 !important;
	letter-spacing: 0.2px;
	transition: background 0.18s, color 0.18s;
	display: flex;
	align-items: center;
	background: #fff !important;
	box-shadow: none !important;
}

:deep(.el-menu--popup .el-menu-item:hover),
:deep(.el-menu--popup .el-menu-item.is-active) {
	background: #f5f7fa !important;
	color: #1765d5 !important;
	font-weight: 600 !important;
}

:deep(.el-menu--popup .el-menu-item.is-disabled) {
	color: #c0c4cc !important;
	background: #fff !important;
	cursor: not-allowed !important;
	opacity: 1 !important;
}

/**
 * @description 统一主菜单、菜单项、子菜单标题的背景色为白色，保证风格一致
 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
	background-color: #fff !important;
}
:deep(.el-menu-item.is-active),
:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
	background-color: #f5f7fa !important;
}

/**
 * @description 主菜单悬停和激活时字体风格与子菜单保持一致
 */
:deep(.el-menu-item:hover),
:deep(.el-menu-item.is-active) {
	background: #f5f7fa !important;
	color: #1765d5 !important;
	font-weight: 600 !important;
}

/**
 * @description 调整主菜单下拉图标与文字的间距，避免重叠
 */
:deep(.el-sub-menu__title) {
	padding-right: 36px !important;
	position: relative;
}

:deep(.el-sub-menu__icon-arrow) {
	right: 16px !important;
	position: absolute !important;
}

/**
 * @description 顶部导航主菜单和子菜单颜色、宽度统一
 * <AUTHOR>
 */
:deep(.el-menu--horizontal > .el-menu-item),
:deep(.el-menu--horizontal > .el-sub-menu > .el-sub-menu__title) {
	position: relative !important;
	min-width: 80px !important;
	max-width: 180px !important;
	width: auto !important;
	text-align: center;
	color: #222 !important;
	font-weight: 500 !important;
	background: #fff !important;
	opacity: 1 !important;
	padding: 0 24px !important;
	box-sizing: border-box;
	font-family: 'PingFang SC', 'Microsoft YaHei', Arial, 'Helvetica Neue', sans-serif !important;
	font-size: 15px !important;
}

:deep(.el-menu--horizontal > .el-menu-item.is-active),
:deep(.el-menu--horizontal > .el-menu-item:hover),
:deep(.el-menu--horizontal > .el-sub-menu > .el-sub-menu__title:hover),
:deep(.el-menu--horizontal > .el-sub-menu.is-active > .el-sub-menu__title) {
	color: #1765d5 !important;
	font-weight: 600 !important;
	background: #f5f7fa !important;
	opacity: 1 !important;
}

:deep(.el-menu--horizontal > .el-menu-item:first-child) {
	min-width: 80px !important;
	max-width: 180px !important;
	width: auto !important;
	padding: 0 24px !important;
	text-align: center;
}

/**
 * @description 顶部菜单项图标和文字之间的分隔线
 */
:deep(.menu-flex) {
	position: relative;
	
	.menu-left-icon {
		position: relative;
		
		&::after {
			content: "";
			position: absolute;
			right: -8px;
			top: 50%;
			transform: translateY(-50%);
			width: 1px;
			height: 16px;
			background-color: #dcdfe6;
		}
	}
}
</style>
