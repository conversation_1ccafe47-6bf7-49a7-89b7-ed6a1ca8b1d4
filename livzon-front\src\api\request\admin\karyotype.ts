import request from "@/utils/request";

/**
 * 查询核型分类下拉树结构
 *
 * @returns
 */
export const karyotypeTree = async (query?: any) => {
	return await request({
		url: "/karyotype/tree",
		method: "get",
		params: query,
	});
};

/**
 * 新增
 *
 * @returns
 */
export const add = async (data: any) => {
	return await request({
		url: "/karyotype/add",
		method: "post",
		data: data,
	});
};


/**
 * 详情
 *
 * @returns
 */
export const details = async (karyotypeId: any) => {
	return await request({
		url: "/karyotype/details/" + karyotypeId,
		method: "get",
	});
};

/**
 * 编辑
 *
 * @returns
 */
export const edit = async (data: any) => {
	return await request({
		url: "/karyotype/edit",
		method: "put",
		data: data,
	});
};

/**
 * 删除
 *
 * @returns
 */
export const del = async (karyotypeId: any) => {
	return await request({
		url: "/karyotype/del/" + karyotypeId,
		method: "delete",
	});
};




