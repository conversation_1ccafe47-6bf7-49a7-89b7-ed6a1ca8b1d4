<template>
	<div class="register-container" :class="{ 'mobile-view': isMobile }">
		<!-- 预加载图片 -->
		<img v-show="false" src="../assets/images/login-background.jpg" @load="onImageLoad" />
		
		<!-- 背景层 -->
		<div class="register-bg" :class="{ 'bg-loaded': isBgLoaded }" :style="{ backgroundImage: `url(${backgroundImageUrl})` }">
			<!-- 背景遮罩 -->
			<div class="bg-overlay"></div>
		</div>
		
		<!-- 主要内容区域 -->
		<div class="register-content">
			<!-- 左侧欢迎区域 -->
			<div class="welcome-section">
				<div class="welcome-content">

				</div>
			</div>
			
			<!-- 右侧注册表单 -->
			<div class="form-section">
				<div class="register-form-wrapper">
					<el-form 
						ref="registerRef" 
						:model="registerForm" 
						:rules="registerRules" 
						class="register-form"
						@submit.prevent="handleRegister"
					>
						<div class="form-header">
							<h2 class="form-title">用户注册</h2>
						</div>
						
						<div class="form-content">
							<el-form-item prop="username" class="form-item">
								<el-input 
									v-model="registerForm.username" 
									type="text" 
									auto-complete="off" 
									placeholder="账号"
									class="modern-input"
									:class="{ 'mobile-input': isMobile }"
									maxlength="20"
									@focus="onInputFocus"
									@blur="onInputBlur"
								>
									<template #prefix>
										<svg-icon icon-class="user" class="input-icon" />
										<div class="input-divider"></div>
									</template>
								</el-input>
							</el-form-item>
							
							<el-form-item prop="password" class="form-item">
								<el-input 
									v-model="registerForm.password" 
									type="password" 
									auto-complete="off" 
									placeholder="密码"
									class="modern-input"
									:class="{ 'mobile-input': isMobile }"
									maxlength="20"
									@keyup.enter="handleRegister"
									@focus="onInputFocus"
									@blur="onInputBlur"
								>
									<template #prefix>
										<svg-icon icon-class="password" class="input-icon" />
										<div class="input-divider"></div>
									</template>
								</el-input>
							</el-form-item>
							
							<el-form-item prop="confirmPassword" class="form-item">
								<el-input 
									v-model="registerForm.confirmPassword" 
									type="password" 
									auto-complete="off" 
									placeholder="确认密码"
									class="modern-input"
									:class="{ 'mobile-input': isMobile }"
									maxlength="20"
									@keyup.enter="handleRegister"
									@focus="onInputFocus"
									@blur="onInputBlur"
								>
									<template #prefix>
										<svg-icon icon-class="password" class="input-icon" />
										<div class="input-divider"></div>
									</template>
								</el-input>
							</el-form-item>
							
							<el-form-item prop="code" v-if="captchaEnabled" class="form-item captcha-item">
								<div class="captcha-wrapper">
									<el-input 
										v-model="registerForm.code" 
										auto-complete="off" 
										placeholder="验证码"
										class="modern-input captcha-input"
										:class="{ 'mobile-input': isMobile }"
										maxlength="10"
										@keyup.enter="handleRegister"
										@focus="onInputFocus"
										@blur="onInputBlur"
									>
										<template #prefix>
											<svg-icon icon-class="validCode" class="input-icon" />
											<div class="input-divider"></div>
										</template>
									</el-input>
									<div class="captcha-code" @click="getCode" @touchstart="onCaptchaTouchStart">
										<img :src="codeUrl" @click="getCode" class="captcha-img" title="获取验证码" alt="验证码" />
									</div>
								</div>
							</el-form-item>
							
							<el-form-item class="form-item register-btn-item">
								<el-button 
									:loading="loading" 
									type="primary" 
									size="large" 
									class="register-btn"
									:class="{ 'mobile-btn': isMobile }"
									@click.prevent="handleRegister"
									@touchstart="onButtonTouchStart"
								>
									<span v-if="!loading">注 册</span>
									<span v-else>注 册 中...</span>
								</el-button>
							</el-form-item>
							
							<!-- 登录链接 -->
							<div class="login-wrapper">
								<router-link 
									class="login-link" 
									:to="'/login'"
									@touchstart="onLinkTouchStart"
								>
									使用已有账户登录
								</router-link>
							</div>
							
							<div class="form-footer">
								<span>荧枢⊙FluoroCore</span>
							</div>
						</div>
					</el-form>
				</div>
			</div>
		</div>
		
	</div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { ref, onMounted, computed, nextTick } from "vue";
import { ElForm, ElMessageBox } from "element-plus";
import { getCodeImg, register } from "@/api/login";
import preload from '@/utils/preload';

const router = useRouter();
const registerRef = ref<InstanceType<typeof ElForm>>();
const registerForm = ref({
	username: "",
	password: "",
	confirmPassword: "",
	code: "",
	uuid: "",
});

// prettier-ignore
const equalToPassword = (rule: any, value: string, callback: Function) => {
	if (registerForm.value.password !== value) {
		callback(new Error("两次输入的密码不一致"));
	} else {
		callback();
	}
};

// prettier-ignore
const registerRules = {
	username: [
		{ required: true, trigger: "blur", message: "请输入您的账号" },
		{ min: 2, max: 20, message: "用户账号长度必须介于 2 和 20 之间", trigger: "blur" }
	],
	password: [
		{ required: true, trigger: "blur", message: "请输入您的密码" },
		{ min: 5, max: 20, message: "用户密码长度必须介于 5 和 20 之间", trigger: "blur" }
	],
	confirmPassword: [
		{ required: true, trigger: "blur", message: "请再次输入您的密码" },
		{ required: true, validator: equalToPassword, trigger: "blur" }
	],
	code: [{ required: true, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref<string>("");
const loading = ref<boolean>(false);
const captchaEnabled = ref(true);

// 添加背景图加载状态
const isBgLoaded = ref(false);

// 移动端检测
const isMobile = computed(() => {
	if (typeof window === 'undefined') return false;
	const userAgent = navigator.userAgent.toLowerCase();
	const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'blackberry', 'webos'];
	return mobileKeywords.some(keyword => userAgent.includes(keyword)) || window.innerWidth <= 768;
});

// 动态导入背景图片
const backgroundImageUrl = new URL('../assets/images/login-background.jpg', import.meta.url).href;

// 图片加载完成回调
const onImageLoad = () => {
	isBgLoaded.value = true;
};

// 移动端触摸事件处理
const onInputFocus = () => {
	// 移动端输入框获得焦点时的处理
	if (isMobile.value) {
		nextTick(() => {
			// 延迟滚动以确保软键盘已经弹出
			setTimeout(() => {
				const activeElement = document.activeElement as HTMLElement;
				if (activeElement) {
					activeElement.scrollIntoView({ 
						behavior: 'smooth', 
						block: 'center' 
					});
				}
			}, 300);
		});
	}
};

const onInputBlur = () => {
	// 移动端输入框失去焦点时的处理
	if (isMobile.value) {
		// 可以在这里添加一些移动端特定的处理逻辑
	}
};

const onCaptchaTouchStart = () => {
	// 验证码触摸开始时的处理
	if (isMobile.value) {
		// 添加触摸反馈
	}
};

const onButtonTouchStart = () => {
	// 按钮触摸开始时的处理
	if (isMobile.value) {
		// 添加触摸反馈
	}
};

const onLinkTouchStart = () => {
	// 链接触摸开始时的处理
	if (isMobile.value) {
		// 添加触摸反馈
	}
};

// 在组件挂载时预加载背景图
onMounted(async () => {
	await preload.preloadImage(backgroundImageUrl);
	isBgLoaded.value = true;
	
	// 移动端优化：防止页面缩放
	if (isMobile.value) {
		const viewport = document.querySelector('meta[name="viewport"]');
		if (viewport) {
			viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover');
		}
		
		// 防止双击缩放
		document.addEventListener('gesturestart', (e) => {
			e.preventDefault();
		});
		
		// 防止页面滚动到顶部（iOS Safari）
		document.addEventListener('touchstart', () => {}, { passive: true });
	}
});

const handleRegister = () => {
	registerRef.value?.validate((valid: boolean) => {
		if (valid) {
			loading.value = true;
			// prettier-ignore
			register(registerForm.value).then((res: any) => {
				if (res.code === 200) {
					const username = registerForm.value.username;
					// prettier-ignore
					ElMessageBox.alert("<font color='red'>恭喜你，您的账号 " + username + " 注册成功！</font>", "系统提示", {
						dangerouslyUseHTMLString: true,
						type: "success",
					}).then(() => {
						router.push("/login");
					}).catch(() => { });
				}
			})
				.catch(() => {
					loading.value = false;
					if (captchaEnabled) {
						getCode();
					}
				});
		}
	});
};

const getCode = () => {
	getCodeImg().then((res: any) => {
		if (res.code === 200) {
			// prettier-ignore
			captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
			if (captchaEnabled.value) {
				codeUrl.value = "data:image/gif;base64," + res.data.img;
				registerForm.value.uuid = res.uuid;
			}
		}
	});
};

getCode();
</script>

<style lang="scss" scoped>
/**
 * 现代化注册页面样式
 * 与登录页面保持一致的设计风格
 */

// 颜色变量定义
$primary-color: #1890ff;
$primary-hover: #40a9ff;
$primary-active: #096dd9;
$bg-color: #f0f2f5;
$white: #ffffff;
$text-primary: #262626;
$text-secondary: #8c8c8c;
$text-disabled: #bfbfbf;
$border-color: #d9d9d9;
$border-hover: #40a9ff;
$shadow-1: 0 2px 8px rgba(0, 0, 0, 0.15);
$shadow-2: 0 4px 16px rgba(0, 0, 0, 0.15);
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

// 主容器
.register-container {
	display: flex;
	min-height: 100vh;
	position: relative;
	overflow: hidden;
}

// 背景层
.register-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: $gradient-primary;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	opacity: 0;
	transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
	z-index: 1;

	&.bg-loaded {
		opacity: 1;
	}
}

// 背景遮罩
.bg-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(1px);
}

// 主要内容区域
.register-content {
	display: flex;
	width: 100%;
	position: relative;
	z-index: 2;
	animation: slideInUp 1s ease-out;
}

// 左侧欢迎区域
.welcome-section {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 80px 60px;
	color: $white;
	
	.welcome-content {
		max-width: 480px;
		
		.welcome-title {
			font-size: 48px;
			font-weight: 700;
			line-height: 1.2;
			margin-bottom: 24px;
			background: linear-gradient(45deg, #fff, #e6f7ff);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			animation: fadeInLeft 1s ease-out 0.3s both;
		}
		
		.welcome-description {
			font-size: 18px;
			line-height: 1.6;
			margin-bottom: 48px;
			opacity: 0.9;
			animation: fadeInLeft 1s ease-out 0.9s both;
		}
	}
}

// 右侧表单区域
.form-section {
	width: 520px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.register-form-wrapper {
	width: 100%;
	max-width: 400px;
	padding: 60px 40px;
	animation: slideInRight 1s ease-out 0.5s both;
}

.register-form {
	width: 100%;
	
	.form-header {
		text-align: center;
		margin-bottom: 48px;
		
		.form-title {
			font-size: 32px;
			font-weight: 700;
			color: #051634 !important;
			margin-bottom: 8px;
			
			// 简化样式，确保始终显示深蓝色
			background: none !important;
			-webkit-background-clip: unset !important;
			-webkit-text-fill-color: #051634 !important;
		}
	}
	
	.form-content {
		.form-item {
			margin-bottom: 28px;
			
			&.captcha-item {
				margin-bottom: 24px;
			}
			
			&.register-btn-item {
				margin-bottom: 4px;
			}
		}
	}
}

// 现代化输入框样式
.modern-input {
	height: 48px !important;
	
	:deep(.el-input__wrapper) {
		border-radius: 12px;
		border: 2px solid #e0e0e0;
		background: #fafafa;
		box-shadow: none;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		
		&:hover {
			background: #f5f5f5;
			border-color: rgba(24, 144, 255, 0.5);
		}
		
		&.is-focus {
			background: $white;
			border-color: $primary-color;
			box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
		}
	}
	
	:deep(.el-input__inner) {
		height: 44px;
		line-height: 44px;
		font-size: 15px;
		color: #000000;
		font-weight: 500;
		
		&::placeholder {
			color: $text-disabled;
			font-size: 14px;
		}
	}
}

// 输入框图标
.input-icon {
	color: $text-secondary;
	font-size: 16px;
	transition: color 0.3s ease;
}

// 输入框内部竖线样式
.input-divider {
	width: 1px;
	height: 20px;
	background-color: #333333;
	margin: 0 12px 0 8px;
	align-self: center;
}

// 验证码部分
.captcha-wrapper {
	display: flex;
	gap: 12px;
	
	.captcha-input {
		flex: 1;
	}
	
	.captcha-code {
		width: 130px; // 桌面端宽度
		height: 48px;
		border-radius: 12px;
		background: #fafafa;
		overflow: hidden;
		cursor: pointer;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		box-sizing: border-box;
		border: 2px solid #e0e0e0;
		// 去掉选中时的灰色框
		outline: none;
		-webkit-tap-highlight-color: transparent;
		user-select: none;
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		
		&:hover {
			background: #f5f5f5;
			border-color: rgba(24, 144, 255, 0.3);
			transform: scale(1.02);
		}
		
		&:focus {
			outline: none;
			border-color: rgba(24, 144, 255, 0.5);
		}
		
		&:active {
			outline: none;
		}
		
		.captcha-img {
			width: 100%;
			height: 100%;
			display: block;
			border: none;
			border-radius: 10px;

		}
	}
}

// 注册按钮
.register-btn {
	width: 100%;
	height: 48px;
	border-radius: 12px;
	font-size: 16px;
	font-weight: 600;
	background: #0066cb !important;
	border: none;
	box-shadow: $shadow-1;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	
	&:hover {
		background: #0052a3 !important;
		transform: translateY(-2px);
		box-shadow: $shadow-2;
	}
	
	&:active {
		background: #004182 !important;
		transform: translateY(0);
	}
	
	&.is-loading {
		opacity: 0.8;
	}
}

// 登录链接容器
.login-wrapper {
	text-align: left;
	margin-top: 0px;
	margin-bottom: 20px;
	
	.login-link {
		color: $primary-color;
		font-size: 14px;
		font-weight: 500;
		text-decoration: none;
		transition: color 0.3s ease;
		// 移动端优化点击区域
		display: inline-block;
		padding: 8px 0;
		-webkit-tap-highlight-color: transparent;
		
		&:hover {
			color: $primary-hover;
		}
		
		&:active {
			color: $primary-active;
		}
	}
}

// 表单底部
.form-footer {
	text-align: center;
	margin-top: 20px;
	
	span {
		color: #061632;
		font-size: 12px;
		font-weight: 400;
	}
}

// 动画定义
@keyframes slideInUp {
	from {
		transform: translateY(100px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideInRight {
	from {
		transform: translateX(50px);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes fadeInLeft {
	from {
		transform: translateX(-30px);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

// 响应式设计
@media (max-width: 1200px) {
	.welcome-section {
		padding: 60px 40px;
		
		.welcome-content .welcome-title {
			font-size: 40px;
		}
	}
	
	.form-section {
		width: 480px;
	}
}

@media (max-width: 992px) {
	.register-content {
		flex-direction: column;
	}
	
	.welcome-section {
		padding: 40px 20px 60px;
		
		.welcome-content {
			text-align: center;
			
			.welcome-title {
				font-size: 36px;
			}
			
			.welcome-description {
				font-size: 16px;
			}
		}
	}
	
	.form-section {
		width: 100%;
		min-height: auto;
	}
	
	.register-form-wrapper {
		padding: 40px 20px 60px;
	}
}

@media (max-width: 768px) {
	.register-container {
		// 移动端优化容器设置
		min-height: 100vh;
		min-height: 100dvh; // 动态视口高度，更好地处理移动浏览器地址栏
	}
	
	.welcome-section {
		padding: 30px 20px 40px;
		min-height: 40vh; // 给欢迎区域一个最小高度
		
		.welcome-content .welcome-title {
			font-size: 28px;
			margin-bottom: 16px;
		}
		
		.welcome-content .welcome-description {
			font-size: 15px;
			margin-bottom: 32px;
		}
	}
	
	.form-section {
		// 移动端表单区域优化
		background: rgba(255, 255, 255, 0.98);
		border-left: none;
		border-top: 1px solid rgba(255, 255, 255, 0.3);
		border-radius: 20px 20px 0 0;
	}
	
	.register-form-wrapper {
		max-width: 100%;
		padding: 30px 20px 40px;
	}
	
	.register-form {
		.form-header .form-title {
			font-size: 26px;
			margin-bottom: 32px;
		}
		
		.form-content .form-item {
			margin-bottom: 24px;
			
			&.captcha-item {
				margin-bottom: 20px;
			}
		}
	}
	
	// 移动端输入框优化
	.modern-input {
		height: 52px !important; // 移动端稍大一些，便于触摸
		
		:deep(.el-input__wrapper) {
			border-radius: 14px;
			// 移动端焦点状态优化
			&.is-focus {
				border-color: $primary-color;
				box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.15);
				transform: scale(1.02);
			}
		}
		
		:deep(.el-input__inner) {
			height: 48px;
			line-height: 48px;
			font-size: 16px; // 防止iOS缩放
			
			&::placeholder {
				font-size: 15px;
			}
		}
	}
	
	.captcha-wrapper {
		flex-direction: column;
		gap: 16px;
		
		.captcha-code {
			width: 100%;
			height: 52px;
			border: 2px solid #e0e0e0;
			border-radius: 14px;
			background: #fafafa;
			// 移动端也去掉选中时的灰色框
			outline: none;
			-webkit-tap-highlight-color: transparent;
			user-select: none;
			-webkit-user-select: none;
			-moz-user-select: none;
			-ms-user-select: none;
			
			&:active {
				transform: scale(0.98);
				border-color: rgba(24, 144, 255, 0.5);
				outline: none;
			}
			
			&:focus {
				outline: none;
			}
			
			.captcha-img {
				width: 100%;
				height: 100%;
				object-fit: contain;
				display: block;
				border: none;
				border-radius: 12px;
				// 移动端图片也去掉选中效果
				outline: none;
				user-select: none;
				-webkit-user-select: none;
				-moz-user-select: none;
				-ms-user-select: none;
				-webkit-touch-callout: none;
				-webkit-tap-highlight-color: transparent;
			}
		}
	}
	
	// 移动端按钮优化
	.register-btn {
		height: 52px;
		font-size: 17px;
		// 添加触摸反馈
		&:active {
			transform: scale(0.98);
		}
	}
	

}

@media (max-width: 480px) {
	.register-container {
		padding: 0;
	}
	
	.welcome-section {
		padding: 20px 16px 30px;
		min-height: 35vh;
	}
	
	.register-form-wrapper {
		padding: 24px 16px 32px;
	}
	
	.register-form .form-header .form-title {
		font-size: 24px;
		margin-bottom: 24px;
	}
	

	
	.form-footer {
		margin-top: 16px;
		
		span {
			font-size: 11px;
		}
	}
}

// 超小屏幕优化（iPhone SE等）
@media (max-width: 375px) {
	.welcome-section {
		padding: 16px 12px 24px;
		min-height: 30vh;
		
		.welcome-content .welcome-title {
			font-size: 24px;
		}
	}
	
	.register-form-wrapper {
		padding: 20px 12px 28px;
	}
	
	.register-form .form-header .form-title {
		font-size: 22px;
	}
	
	.form-content .form-item {
		margin-bottom: 20px;
	}
	

}

// 横屏移动设备优化
@media (max-width: 768px) and (orientation: landscape) {
	.register-content {
		flex-direction: row;
	}
	
	.welcome-section {
		flex: 0.6;
		padding: 20px;
		min-height: auto;
		
		.welcome-content .welcome-title {
			font-size: 24px;
			margin-bottom: 12px;
		}
		
		.welcome-content .welcome-description {
			font-size: 14px;
			margin-bottom: 20px;
		}
	}
	
	.form-section {
		flex: 0.4;
		border-radius: 0 20px 20px 0;
		border-left: 1px solid rgba(255, 255, 255, 0.3);
		border-top: none;
	}
	
	.register-form-wrapper {
		padding: 20px 16px;
	}
	
	.register-form .form-header .form-title {
		font-size: 20px;
		margin-bottom: 20px;
	}
	

}

// 触摸设备通用优化
@media (hover: none) and (pointer: coarse) {
	// 移除hover效果，优化触摸体验
	.modern-input:deep(.el-input__wrapper):hover {
		background: #fafafa;
		border-color: #e0e0e0;
		transform: none;
	}
	
	.captcha-code:hover {
		background: #fafafa;
		border-color: #e0e0e0;
		transform: none;
	}
	
	.register-btn:hover {
		background: #0066cb !important;
		transform: none;
		box-shadow: $shadow-1;
	}
	
	.login-link:hover {
		color: $primary-color;
	}
	
	// 添加触摸反馈
	.modern-input:deep(.el-input__wrapper):active {
		transform: scale(1.01);
	}
	
	.captcha-code:active {
		transform: scale(0.98);
		background: #f0f0f0;
		border-color: rgba(24, 144, 255, 0.5);
	}
	

}

// 高分辨率移动设备优化
@media (-webkit-min-device-pixel-ratio: 2) and (max-width: 768px), 
       (min-resolution: 192dpi) and (max-width: 768px) {
	.register-bg {
		background-size: cover;
		-webkit-background-size: cover;
		background-attachment: scroll; // 移动端不使用fixed
	}
}

// 移动端特定类样式
.mobile-view {
	.register-content {
		min-height: 100vh;
		min-height: 100dvh;
	}
}

.mobile-input {
	// iOS特定优化
	-webkit-appearance: none;
	border-radius: 14px !important;
	
	:deep(.el-input__wrapper) {
		// 防止iOS输入框样式被覆盖
		-webkit-appearance: none;
		-webkit-border-radius: 14px;
		
		// 优化触摸区域
		min-height: 52px;
		padding: 0 16px;
		
		// 移动端焦点状态
		&.is-focus {
			// 添加一个轻微的升起效果
			box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.15), 
			           0 2px 8px rgba(0, 0, 0, 0.1);
			transform: translateY(-1px);
		}
	}
	
	:deep(.el-input__inner) {
		// 防止iOS缩放
		font-size: 16px !important;
		-webkit-text-size-adjust: 100%;
		
		// 优化占位符在移动端的显示
		&::placeholder {
			opacity: 0.7;
			color: #999;
		}
	}
}

.mobile-btn {
	// 移动端按钮优化
	min-height: 52px;
	font-weight: 600;
	letter-spacing: 2px;
	
	// 添加触摸反馈
	&:active {
		transform: scale(0.98) translateY(1px);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
	}
	
	// 加载状态优化
	&.is-loading {
		&:active {
			transform: none;
		}
	}
}



// 移动端安全区域适配（iPhone X系列等）
@supports (padding: max(0px)) {
	.register-container {
		padding-left: max(0px, env(safe-area-inset-left));
		padding-right: max(0px, env(safe-area-inset-right));
	}
	
	.form-section {
		padding-bottom: max(20px, env(safe-area-inset-bottom));
	}
}

// 移动端键盘弹起时的优化
@media screen and (max-height: 600px) and (max-width: 768px) {
	.welcome-section {
		display: none; // 键盘弹起时隐藏欢迎区域
	}
	
	.form-section {
		width: 100%;
		border-radius: 0;
		border: none;
	}
	
	.register-form-wrapper {
		padding: 20px 16px 16px;
	}
	
	.register-form .form-header .form-title {
		font-size: 22px;
		margin-bottom: 20px;
	}
	
	.form-content .form-item {
		margin-bottom: 16px;
	}
	

}

// PWA支持 - 全屏显示优化
@media all and (display-mode: standalone) {
	.register-container {
		padding-top: max(20px, env(safe-area-inset-top));
	}
}

// 深色模式适配（可选）
@media (prefers-color-scheme: dark) {
	.mobile-view {
		.form-section {
			background: rgba(30, 30, 30, 0.95);
		}
		
		.modern-input:deep(.el-input__wrapper) {
			background: #2a2a2a;
			border-color: #404040;
			
			&.is-focus {
				background: #333;
				border-color: $primary-color;
			}
		}
		
		.modern-input:deep(.el-input__inner) {
			color: #fff;
			
			&::placeholder {
				color: #888;
			}
		}
		
		.form-title {
			color: #fff !important;
		}
		
		.form-footer span {
			color: #ccc;
		}
		

	}
}

// 无障碍优化
@media (prefers-reduced-motion: reduce) {
	.register-container,
	.register-content,
	.welcome-section,
	.form-section,
	.modern-input,
	.register-btn,
	.captcha-code {
		animation: none !important;
		transition: none !important;
	}
	
	.register-bg {
		transition: opacity 0.3s ease !important;
	}
}

// 高对比度模式适配
@media (prefers-contrast: high) {
	.modern-input:deep(.el-input__wrapper) {
		border-width: 2px;
		border-color: #000;
		
		&.is-focus {
			border-color: $primary-color;
			box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.5);
		}
	}
	
	.register-btn {
		border: 2px solid #000;
	}
	
	.login-link {
		text-decoration: underline;
		font-weight: bold;
	}
}

// Element Plus 样式覆盖
:deep(.el-form-item__error) {
	font-size: 13px;
	color: #ff4d4f;
	line-height: 1.4;
}

:deep(.el-loading-mask) {
	border-radius: 12px;
}
</style>
