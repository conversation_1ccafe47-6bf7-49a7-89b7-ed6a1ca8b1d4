<template>
    <div class="editor" ref="editor" :style="styles"></div>
</template>

<script>
import Quill from "quill";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";

export default {
  name: "Editor",
  props: {
    /* 编辑器的内容 */
    modelValue: {
      type: String,
      default: "",
    },
    /* 高度 */
    height: {
      type: Number,
      default: null,
    },
    /* 最小高度 */
    minHeight: {
      type: Number,
      default: null,
    },
    /* 只读 */
    readOnly: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      Quill: null,
      currentValue: "",
      options: {
        theme: "snow",
        bounds: document.body,
        debug: "warn",
        modules: {
          // 工具栏配置
          toolbar: [
            ["bold", "italic", "underline", "strike"],       // 加粗 斜体 下划线 删除线
            ["blockquote", "code-block"],                    // 引用  代码块
            [{ list: "ordered" }, { list: "bullet" }],       // 有序、无序列表
            [{ indent: "-1" }, { indent: "+1" }],            // 缩进
            [{ size: ["small", false, "large", "huge"] }],   // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题
            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色
            [{ align: [] }],                                 // 对齐方式
            ["clean"],                                       // 清除文本格式
            ["link", "image", "video"]                       // 链接、图片、视频
          ],
        },
        placeholder: "请输入内容",
        readOnly: this.readOnly,
      },
    };
  },
  computed: {
    styles() {
      let style = {};
      if (this.minHeight) {
        style.minHeight = `${this.minHeight}px`;
      }
      if (this.height) {
        style.height = `${this.height}px`;
      }
      return style;
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val !== this.currentValue) {
          this.currentValue = val === null ? "" : val;
          this.setContent(this.currentValue);
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    if (this.Quill) {
      this.Quill.off("text-change", this.handleTextChange);
      this.Quill.off("selection-change", this.handleSelectionChange);
      this.Quill.off("editor-change", this.handleEditorChange);
      this.Quill = null;
    }
  },
  methods: {
    init() {
      const editor = this.$refs.editor;
      if (!editor) {
        console.warn('Editor element not found');
        return;
      }

      // 确保在DOM更新后初始化
      this.$nextTick(() => {
        try {
          // 创建编辑器实例
          this.Quill = new Quill(editor, this.options);
          
          // 初始化内容
          this.setContent(this.currentValue);

          // 监听内容变化
          this.Quill.on("text-change", this.handleTextChange);

          // 监听选区变化
          this.Quill.on("selection-change", this.handleSelectionChange);

          // 设置只读状态
          this.Quill.enable(!this.readOnly);

          // 添加编辑器变化事件
          this.Quill.on("editor-change", this.handleEditorChange);
        } catch (error) {
          console.error('Editor initialization error:', error);
        }
      });
    },

    /**
     * 安全地设置编辑器内容
     * @param {string} content - 要设置的内容
     */
    setContent(content) {
      if (!this.Quill) return;
      
      try {
        const html = content || '';
        this.Quill.root.innerHTML = html;
        // 重置选区到文档开始
        this.Quill.setSelection(0, 0);
      } catch (error) {
        console.warn('Set content error:', error);
      }
    },

    /**
     * 处理文本变化
     * @param {Delta} delta - 变化的内容
     * @param {Delta} oldDelta - 旧的内容
     * @param {string} source - 变化来源
     */
    handleTextChange(delta, oldDelta, source) {
      if (source === 'user') {
        try {
          const html = this.$refs.editor.querySelector('.ql-editor').innerHTML;
          const text = this.Quill.getText();
          this.currentValue = html;
          this.$emit("update:modelValue", html);
          this.$emit("on-change", { html, text, quill: this.Quill });
        } catch (error) {
          console.warn('Text change error:', error);
        }
      }
    },

    /**
     * 处理选区变化
     * @param {Range} range - 新的选区
     * @param {Range} oldRange - 旧的选区
     * @param {string} source - 变化来源
     */
    handleSelectionChange(range, oldRange, source) {
      try {
        if (range) {
          const length = this.Quill.getLength();
          // 确保选区在有效范围内
          if (range.index >= 0 && range.index < length) {
            const safeRange = {
              index: range.index,
              length: Math.min(range.length, length - range.index)
            };
            this.$emit("on-selection-change", safeRange, oldRange, source);
          }
        }
      } catch (error) {
        console.warn('Selection change error:', error);
      }
    },

    /**
     * 处理编辑器变化
     * @param {string} eventName - 事件名称
     * @param {...any} args - 其他参数
     */
    handleEditorChange(eventName, ...args) {
      try {
        this.$emit("on-editor-change", eventName, ...args);
      } catch (error) {
        console.warn('Editor change error:', error);
      }
    },

    /**
     * 安全地设置选区
     * @param {number} index - 选区起始位置
     * @param {number} length - 选区长度
     */
    setSelection(index, length = 0) {
      if (!this.Quill) return;
      
      try {
        const editorLength = this.Quill.getLength();
        const safeIndex = Math.min(Math.max(0, index), editorLength - 1);
        const safeLength = Math.min(length, editorLength - safeIndex);
        this.Quill.setSelection(safeIndex, safeLength);
      } catch (error) {
        console.warn('Set selection error:', error);
      }
    }
  },
};
</script>

<style>
.editor, .ql-toolbar {
  white-space: pre-wrap!important;
  line-height: normal !important;
}
.quill-img {
  display: none;
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "标准字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
  content: "衬线字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
  content: "等宽字体";
}
</style>
