<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- <title>Vite + Vue + TS</title> -->
    <title>荧枢⊙ FluoroCore</title>
    <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
    <style>
        html,
        body,
        #app {
            height: 100%;
            margin: 0px;
            padding: 0px;
        }

        .chromeframe {
            margin: 0.2em 0;
            background: #ccc;
            color: #000;
            padding: 0.2em 0;
        }

        #loader-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 999999;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            backdrop-filter: blur(10px);
        }

        #loader {
            display: block;
            position: relative;
            left: 50%;
            top: 50%;
            width: 120px;
            height: 120px;
            margin: -60px 0 0 -60px;
            perspective: 1000px;
            z-index: 1001;
        }

        #loader .cube {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
            animation: cubeRotate 3s infinite linear;
        }

        #loader .cube-face {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.5);
            backdrop-filter: blur(5px);
        }

        #loader .cube-face:nth-child(1) { transform: rotateY(0deg) translateZ(60px); }
        #loader .cube-face:nth-child(2) { transform: rotateY(90deg) translateZ(60px); }
        #loader .cube-face:nth-child(3) { transform: rotateY(180deg) translateZ(60px); }
        #loader .cube-face:nth-child(4) { transform: rotateY(-90deg) translateZ(60px); }
        #loader .cube-face:nth-child(5) { transform: rotateX(90deg) translateZ(60px); }
        #loader .cube-face:nth-child(6) { transform: rotateX(-90deg) translateZ(60px); }

        @keyframes cubeRotate {
            0% {
                transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
            }
            100% {
                transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
            }
        }

        #loader-wrapper .load_title {
            font-family: 'Open Sans';
            color: #333;
            font-size: 20px;
            width: 100%;
            text-align: center;
            z-index: 9999999999999;
            position: absolute;
            top: 65%;
            opacity: 1;
            line-height: 30px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            animation: fadeInOut 2s ease-in-out infinite;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        @keyframes fadeInOut {
            0%, 100% {
                opacity: 0.6;
                transform: scale(0.95);
            }
            50% {
                opacity: 1;
                transform: scale(1);
            }
        }

        #loader-wrapper .load_title span {
            font-weight: normal;
            font-style: italic;
            font-size: 14px;
            color: #666;
            opacity: 0.8;
            display: block;
            margin-top: 8px;
            -webkit-text-fill-color: #666;
        }

        #loader-wrapper .loader-section {
            position: fixed;
            top: 0;
            width: 51%;
            height: 100%;
            background: #e6f3ff;
            z-index: 1000;
            -webkit-transform: translateX(0);
            -ms-transform: translateX(0);
            transform: translateX(0);
        }

        #loader-wrapper .loader-section.section-left {
            left: 0;
        }

        #loader-wrapper .loader-section.section-right {
            right: 0;
        }


        .loaded #loader-wrapper .loader-section.section-left {
            -webkit-transform: translateX(-100%);
            -ms-transform: translateX(-100%);
            transform: translateX(-100%);
            -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
            transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
        }

        .loaded #loader-wrapper .loader-section.section-right {
            -webkit-transform: translateX(100%);
            -ms-transform: translateX(100%);
            transform: translateX(100%);
            -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
            transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
        }

        .loaded #loader {
            opacity: 0;
            -webkit-transition: all 0.3s ease-out;
            transition: all 0.3s ease-out;
        }

        .loaded #loader-wrapper {
            visibility: hidden;
            -webkit-transform: translateY(-100%);
            -ms-transform: translateY(-100%);
            transform: translateY(-100%);
            -webkit-transition: all 0.3s 1s ease-out;
            transition: all 0.3s 1s ease-out;
        }

        .no-js #loader-wrapper {
            display: none;
        }

        .no-js h1 {
            color: #222222;
        }
    </style>
</head>

<body>
    <div id="app">
        <div id="loader-wrapper">
            <div id="loader">
                <div class="cube">
                    <div class="cube-face"></div>
                    <div class="cube-face"></div>
                    <div class="cube-face"></div>
                    <div class="cube-face"></div>
                    <div class="cube-face"></div>
                    <div class="cube-face"></div>
                </div>
            </div>
            <div class="loader-section section-left"></div>
            <div class="loader-section section-right"></div>
            <div class="load_title">正在加载系统资源，请耐心等待</div>
        </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
</body>

</html>