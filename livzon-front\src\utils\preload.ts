/**
 * 预加载工具类
 */
class Preload {
  private static instance: Preload;
  private loadedImages: Map<string, boolean> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): Preload {
    if (!Preload.instance) {
      Preload.instance = new Preload();
    }
    return Preload.instance;
  }

  /**
   * 预加载图片
   * @param url 图片URL
   * @returns Promise<boolean>
   */
  public async preloadImage(url: string): Promise<boolean> {
    if (this.loadedImages.has(url)) {
      return true;
    }

    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        this.loadedImages.set(url, true);
        resolve(true);
      };
      img.onerror = () => {
        console.error(`Failed to load image: ${url}`);
        resolve(false);
      };
      img.src = url;
    });
  }

  /**
   * 检查图片是否已加载
   * @param url 图片URL
   * @returns boolean
   */
  public isImageLoaded(url: string): boolean {
    return this.loadedImages.get(url) || false;
  }
}

export default Preload.getInstance(); 