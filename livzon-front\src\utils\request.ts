import axios, { AxiosResponse } from "axios";
// prettier-ignore
import { ElNotification, ElMessageBox, ElLoading, ElMessage } from "element-plus";
import useUserStore from "@/store/modules/user";
import { getToken } from "@/utils/auth";
import { tansParams, blobValidate } from "@/utils/livzon";
import errorCode from "@/utils/errorCode";
import cache from "@/plugins/cache";
import { saveAs } from "file-saver";
import { nanoid } from "nanoid";
const userAgent = navigator.userAgent;
// prettier-ignore
var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1;
console.log("是否是IE11", isIE11);

// prettier-ignore
var isLessIE11 = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1;
console.log("是否是小于IE11", isLessIE11);

// prettier-ignore
axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";

// 创建axios实例
export const service = axios.create({
	baseURL: import.meta.env.VITE_APP_BASE_API,
	// 超时
	timeout: 3600000,
});
// 是否显示重新登录
export let isRelogin = { show: false };
// request拦截器
service.interceptors.request.use(
	(config: any) => {
		// 是否需要设置 token
		// prettier-ignore
		const isToken = (config.headers || {}).isToken === false;
		// 是否需要防止数据重复提交
		// prettier-ignore
		const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
		const requestId = nanoid();
		
		// 判断是否是分享页面的请求
		const isShareRequest = config.url.includes('/share');
		
		if (getToken() && !isToken && !isShareRequest) {
			// 请求加上随机ID
			config.headers["RequestId"] = requestId;
			// prettier-ignore
			config.headers["Authorization"] = "Bearer " + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
		}
		// get请求映射params参数
		if (config.method === "get" && config.params) {
			let url = config.url + "?" + tansParams(config.params);
			url = url.slice(0, -1);
			config.params = {};
			config.url = url;
		}
		// prettier-ignore
		if (!isRepeatSubmit && (config.method === "post" || config.method === "put")) {
			// prettier-ignore
			const requestObj = {
				url: config.url,
				data: typeof config.data === "object" ? JSON.stringify(config.data) : config.data,
				time: new Date().getTime(),
				withCredentials: config.withCredentials
			};
			// prettier-ignore
			const sessionObj = cache.session.getJSON("sessionObj");
			// prettier-ignore
			if (sessionObj === undefined || sessionObj === null || sessionObj === "") {
				cache.session.setJSON("sessionObj", requestObj);
			} else {
				const s_url = sessionObj.url; // 请求地址
				const s_data = sessionObj.data; // 请求数据
				const s_time = sessionObj.time; // 请求时间
				const interval = 1000; // 间隔时间(ms)，小于此时间视为重复提交
				const with_credentials = sessionObj.withCredentials; // 凭据
				// prettier-ignore
				if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url && !with_credentials) {
					const message = "数据正在处理，请勿重复提交";
					console.warn(`[${s_url}]: ` + message);
					return Promise.reject(new Error(message));
				} else {
					cache.session.setJSON("sessionObj", requestObj);
				}
			}
		}
		return config;
	},
	(error) => {
		console.log(error);
		Promise.reject(error);
	}
);

// 响应拦截器
service.interceptors.response.use((res: AxiosResponse) => {
	// 未设置状态码则默认成功状态
	const code = res.data.code || 200;
	// 获取错误信息
	// prettier-ignore
	const msg = errorCode[code] || res.data.msg || errorCode["default"];
	// 二进制数据则直接返回
	// prettier-ignore
	if (res.request.responseType === "blob" || res.request.responseType === "arraybuffer") {
		return res.data;
	}

	// Check the current browser window's path
	const currentPath = window.location.pathname;
	// Use startsWith to cover potential paths like /share/abc or /poster/123
	const isShareOrPosterPage = currentPath.startsWith('/share') || currentPath.startsWith('/poster');

	if (code === 401) {
		// If it's NOT a share/poster page AND the relogin prompt isn't already shown
		if (!isShareOrPosterPage && !isRelogin.show) {
			isRelogin.show = true;
			// prettier-ignore
			ElMessageBox.confirm(
				"登录状态已过期，您可以继续留在该页面，或者重新登录",
				"系统提示",
				{
					confirmButtonText: "重新登录",
					cancelButtonText: "取消",
					type: "warning"
				}
			)
				.then(() => {
					isRelogin.show = false;
					useUserStore().logOut().then(() => {
						// 如果当前就在登录页，避免重复跳转
						if (window.location.pathname !== '/login') {
							location.href = "/index"; // 或者跳转到登录页 '/login'
						}
					});
				})
				.catch(() => {
					isRelogin.show = false;
				});
			// Return rejection *after* starting the prompt logic for non-share pages
			// prettier-ignore
			return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
		} else if (isShareOrPosterPage) {
			// If it IS a share/poster page, just reject without showing the prompt.
			// Log the error for debugging purposes.
			console.error(`Ignored 401 on share/poster page (${currentPath}): ${msg}`);
			// prettier-ignore
			return Promise.reject(msg || "Authentication Error on Public Page"); // Reject with specific error
		} else {
			// If it's not share/poster, but isRelogin.show is true (prompt already showing), just reject
			// prettier-ignore
			return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
		}
	} else if (code === 500) {
		ElMessage({
			message: msg,
			type: "error"
		});
		return Promise.reject(new Error(msg));
	} else if (code !== 200) {
		ElNotification.error({
			title: msg
		});
		return Promise.reject("error");
	} else {
		return res.data;
	}
},
	error => {
		console.log("err", error);
		let { message } = error;
		if (message == "Network Error") {
			message = "后端接口连接异常";
		} else if (message.includes("timeout")) {
			message = "系统接口请求超时";
		} else if (message.includes("Request failed with status code")) {
			message = "系统接口" + message.substr(message.length - 3) + "异常";
		}
		ElMessage({
			message: message,
			type: "error",
			duration: 5 * 1000
		});
		return Promise.reject(error);
	}
);

/**
 * 下载文件
 * 
 * @param {string} url 下载地址
 * @param {object|Array} params 请求参数
 * @param {string} filename 文件名
 * @returns {Promise<void>}
 */
export const download = async (url: string, params: any, filename: string) => {
	// prettier-ignore
	const downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", spinner: "loading", background: "rgba(0, 0, 0, 0.7)", });

	let req = null;
	if (params) {
		req = service.post(url, params, {
			transformRequest: [
				(params) => {
					if (Array.isArray(params)) {
						// 如果是数组，将每个元素转换为查询字符串
						return params.map(item => tansParams(item)).join('&');
					}
					return tansParams(params);
				},
			],
			headers: { "Content-Type": "application/x-www-form-urlencoded" },
			responseType: "blob",
		});
	} else {
		req = service.get(url, {
			headers: { "Content-Type": "application/x-www-form-urlencoded" },
			responseType: "blob",
		});
	}
	return await req
		.then(async (resp: any) => {
			const isLogin = await blobValidate(resp);
			if (isLogin) {
				const blob = new Blob([resp]);
				// saveAs(blob, filename, {
				// 	type: "application/octet-stream;charset=utf-8"
				// });
				saveAs(blob, filename);
				//var blob2 = new Blob([resp], {type: "text/plain;charset=utf-8"});
				//saveAs(blob, resp.msg + ".xlsx");
				console.log("%s ====>>>导出成功", filename);
			} else {
				const resText = resp.text();
				const rspObj = JSON.parse(resText);
				// prettier-ignore
				const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
				ElMessage.error(errMsg);
			}
			downloadLoadingInstance.close();
		})
		.catch((r) => {
			console.error(r);
			ElMessage.error("下载文件出现错误，请联系管理员！");
			downloadLoadingInstance.close();
		});
};

export default service;