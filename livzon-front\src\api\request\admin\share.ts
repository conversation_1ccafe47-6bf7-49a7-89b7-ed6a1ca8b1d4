import request from "@/utils/request";

/**
 * 查询
 *
 * @returns
 */
export const query = async (query?: any) => {
	return await request({
		url: "/share",
		method: "get",
		params: query,
	});
};

/**
 * 查询
 *
 * @returns
 */
export const commentQuery = async (query?: any) => {
	return await request({
		url: "/comment",
		method: "get",
		params: query,
	});
};

/**
 * 查询评论回复
 *
 * @returns
 */
export const commentReplyQuery = async (query?: any) => {
	return await request({
		url: "/comment/query",
		method: "get",
		params: query,
	});
};

/**
 * 访问人数
 *
 * @returns
 */
export const visitors = async (data?: any) => {
	return await request({
		url: "/share/visits/add",
		method: "post",
		data: data,
	});
};

export const visitorsFind = async (fileId?: any) => {
	return await request({
		url: "/share/visits/find/" + fileId,
		method: "get",
	});
};
/**
 * 新增
 *
 * @returns
 */
export const add = async (data: any) => {
	return await request({
		url: "/share/add",
		method: "post",
		data: data,
	});
};

/**
 * @description 开放接口，请求路径带shareId
 * @param {Object} data - 行数据，需包含shareId
 */
export const open = async (data: { shareId: string | number }) => {
	return await request({
		url: `/comment/open/${data.shareId}`,
		method: "put"
	});
};

/**
 * @description 关闭接口，请求路径带shareId
 * @param {Object} data - 行数据，需包含shareId
 */
export const closed = async (data: { shareId: string | number }) => {
	return await request({
		url: `/comment/closed/${data.shareId}`,
		method: "put"
	});
};

/**
 * @description 删除接口，请求路径带shareId
 * @param {Object} data - 行数据，需包含shareId
 */
export const del = async (data: { shareId: string | number }) => {
	return await request({
		url: `/comment/del/${data.shareId}`,
		method: "delete"
	});
};