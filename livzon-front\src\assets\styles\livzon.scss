 /**
 * 通用css样式布局处理
 * Copyright (c) 2024 Tom
 */

 /** 基础通用 **/
.pt5 {
	padding-top: 5px;
}
.pr5 {
	padding-right: 5px;
}
.pb5 {
	padding-bottom: 5px;
}
.mt5 {
	margin-top: 5px;
}
.mr5 {
	margin-right: 5px;
}
.mb5 {
	margin-bottom: 5px;
}
.mb8 {
	margin-bottom: 8px;
}
.ml5 {
	margin-left: 5px;
}
.mt10 {
	margin-top: 10px;
}
.mr10 {
	margin-right: 10px;
}
.mb10 {
	margin-bottom: 10px;
}
.ml10 {
	margin-left: 10px;
}
.mt20 {
	margin-top: 20px;
}
.mr20 {
	margin-right: 20px;
}
.mb20 {
	margin-bottom: 20px;
}
.ml20 {
	margin-left: 20px;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
	font-family: inherit;
	font-weight: 500;
	line-height: 1.1;
	color: inherit;
}

.el-dialog:not(.is-fullscreen) {
	margin-top: 6vh !important;
}

.el-dialog.scrollbar .el-dialog__body {
    overflow: auto;
	overflow-x: hidden;
	max-height: 70vh;
	padding: 10px 20px 0;
}

.el-table {
	.el-table__header-wrapper, .el-table__fixed-header-wrapper {
		th {
			word-break: break-word;
			background-color: #f8f8f9 !important;
			color: #515a6e;
			height: 40px !important;
			font-size: 13px;
		}
	}
	.el-table__body-wrapper {
		.el-button [class*=""] + span {
			margin-left: 1px;
		}
	}
}

/** 表单布局 **/
.form-header {
    font-size:15px;
	color:#6379bb;
	border-bottom:1px solid #ddd;
	margin:8px 10px 25px 10px;
	padding-bottom:5px
}

/** 表格布局 **/
.pagination-container {
	// position: relative;
	height: 25px;
	margin-bottom: 10px;
	margin-top: 15px;
	padding: 10px 20px !important;
}

/* tree border */
.tree-border {
    margin-top: 5px;
    border: 1px solid #e5e6e7;
    background: #FFFFFF none;
    border-radius:4px;
    width: 100%;
}

.pagination-container .el-pagination {
	right: 0;
	position: absolute;
}

@media ( max-width : 768px) {
  .pagination-container .el-pagination > .el-pagination__jump {
    display: none !important;
  }
  .pagination-container .el-pagination > .el-pagination__sizes {
    display: none !important;
  }
}

.el-table .fixed-width .el-button--small {
	padding-left: 0;
	padding-right: 0;
	width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
	cursor: pointer;
	color: #409EFF;
	margin-left: 10px;
}

.el-table .el-dropdown, .arrow-down {
	font-size: 12px;
}

.el-tree-node__content > .el-checkbox {
	margin-right: 8px;
}

.list-group-striped > .list-group-item {
	border-left: 0;
	border-right: 0;
	border-radius: 0;
	padding-left: 0;
	padding-right: 0;
}

.list-group {
	padding-left: 0px;
	list-style: none;
}

.list-group-item {
	border-bottom: 1px solid #e7eaec;
	border-top: 1px solid #e7eaec;
	margin-bottom: -1px;
	padding: 11px 0px;
	font-size: 13px;
}

.pull-right {
	float: right !important;
}

.el-card__header {
	padding: 14px 15px 7px !important;
	min-height: 40px;
}

.el-card__body {
	padding: 15px 20px 20px 20px !important;
}

.card-box {
	padding-right: 15px;
	padding-left: 15px;
	margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

/* 滴度选择对话框样式 */
.titer-selection-dialog {
  width: 520px !important;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
  max-height: 80vh;
  z-index: 2001 !important;
  
  .el-message-box {
    border-radius: 12px;
    overflow: hidden;
    border: none;
    margin: 0;
    padding: 0;
  }
  
  .el-message-box__header {
    padding: 16px 20px;
    text-align: center;
    background: #409eff;
    color: white;
    border-radius: 12px 12px 0 0;
    margin: 0;
    border: none;
    box-sizing: border-box;
    width: 100%;
    
    .el-message-box__title {
      font-size: 16px;
      font-weight: 600;
      color: white;
      margin: 0;
      padding: 0;
      line-height: 1.2;
    }
  }
  
  .el-message-box__content {
    padding: 0;
    margin: 0;
    border: none;
    max-height: calc(85vh - 160px);
    overflow-y: auto;
    overflow-x: hidden;
    
    .titer-selection-content {
      padding: 24px;
      background: #fff;
      margin: 0;
      border: none;
      border-radius: 0 0 12px 12px;
    }
    
    .titer-description {
      font-size: 16px;
      color: #2c3e50;
      margin-bottom: 20px;
      text-align: center;
      font-weight: 500;
      line-height: 1.5;
    }
    
    .titer-options {
      display: flex;
      gap: 20px;
      justify-content: center;
      margin-bottom: 16px;
    }
    
    .titer-option {
      flex: 1;
      max-width: 200px;
      padding: 24px 20px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      text-align: center;
      border: 2px solid transparent;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      
      &:active {
        transform: scale(0.98);
      }
    }
    
    .titer-option-primary {
      background: #409eff;
      border-color: #409eff;
      color: white;
      
      &:hover {
        background: #66b1ff;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
      }
      
      .titer-option-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }
      
      .titer-option-title {
        font-size: 18px;
        font-weight: 600;
        color: white;
        margin-bottom: 8px;
      }
      
      .titer-option-desc {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 12px;
        line-height: 1.4;
      }
      
      .titer-option-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(255, 255, 255, 0.3);
        color: white;
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 11px;
        font-weight: 600;
      }
    }
    
    .titer-option-success {
      background: #67c23a;
      border-color: #67c23a;
      color: white;
      
      &:hover {
        background: #85ce61;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(103, 194, 58, 0.3);
      }
      
      .titer-option-icon {
        font-size: 36px;
        margin-bottom: 12px;
        color: white;
        font-weight: bold;
      }
      
      .titer-option-title {
        font-size: 18px;
        font-weight: 600;
        color: white;
        margin-bottom: 8px;
      }
      
      .titer-option-desc {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 12px;
        line-height: 1.4;
      }
      
      .titer-option-feature {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(255, 255, 255, 0.3);
        color: white;
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 11px;
        font-weight: 600;
      }
    }
    
    .titer-loading {
      display: none;
      text-align: center;
      padding: 20px;
      
      .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 12px;
      }
      
      .loading-text {
        font-size: 14px;
        color: #409eff;
      }
    }
  }
  
  .el-message-box__btns {
    display: none;
  }
  
  .el-message-box__message {
    margin: 0;
  }
}

/* 滴度选择对话框动画 */
@keyframes dialogSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 遮罩层动画 */
.el-overlay.is-message-box {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

/* 优化移动端适配 */
@media (max-width: 768px) {
  .titer-selection-dialog {
    width: 95vw !important;
    max-width: 400px !important;
    margin: 3vh auto !important;
    max-height: 94vh !important;
    overflow: hidden !important;
    border-radius: 12px !important;
    
    .el-message-box {
      border-radius: 12px !important;
      overflow: hidden !important;
      border: none !important;
      margin: 0 !important;
      padding: 0 !important;
    }
    
    .el-message-box__header {
      padding: 14px 16px !important;
      margin: 0 !important;
      border: none !important;
      box-sizing: border-box !important;
      width: 100% !important;
      
      .el-message-box__title {
        font-size: 16px !important;
        line-height: 1.2 !important;
        margin: 0 !important;
        padding: 0 !important;
      }
    }
    
    .el-message-box__content {
      max-height: calc(94vh - 100px) !important;
      overflow-y: auto !important;
      overflow-x: hidden !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      
      .titer-selection-content {
        padding: 20px 16px 16px !important;
        margin: 0 !important;
        border: none !important;
        border-radius: 0 0 12px 12px !important;
        
        &.mobile {
          font-size: 16px !important;
          line-height: 1.6 !important;
        }
      }
      
      .titer-options {
        flex-direction: column !important;
        gap: 16px !important;
        padding: 20px 10px !important;
        
        .titer-option {
          max-width: none !important;
          min-height: 120px !important;
          padding: 20px 16px !important;
          border-radius: 12px !important;
          border-width: 2px !important;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
          transition: all 0.3s ease !important;
          
          /* 移动端触摸反馈 */
          &:active {
            transform: scale(0.98) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
          }
          
          &:hover {
            transform: none !important; /* 移动端禁用悬停效果 */
          }
          
          .titer-option-icon {
            font-size: 32px !important;
            margin-bottom: 12px !important;
          }
          
          .titer-option-title {
            font-size: 20px !important;
            font-weight: bold !important;
            margin-bottom: 8px !important;
          }
          
          .titer-option-desc {
            font-size: 14px !important;
            margin-bottom: 10px !important;
            opacity: 0.9 !important;
          }
          
          .titer-option-badge,
          .titer-option-feature {
            font-size: 12px !important;
            padding: 4px 10px !important;
            border-radius: 12px !important;
            position: absolute !important;
            top: 12px !important;
            right: 12px !important;
          }
        }
        
        .titer-option-primary {
          background: #409eff !important;
          color: white !important;
          border-color: #409eff !important;
          
          .titer-option-badge {
            background: rgba(255,255,255,0.3) !important;
            color: white !important;
          }
        }
        
        .titer-option-success {
          background: #67c23a !important;
          color: white !important;
          border-color: #67c23a !important;
          
          .titer-option-feature {
            background: rgba(255,255,255,0.3) !important;
            color: white !important;
          }
        }
      }
      
      .titer-loading {
        text-align: center !important;
        padding: 30px 20px !important;
        
        .loading-spinner {
          width: 32px !important;
          height: 32px !important;
          margin: 0 auto 16px !important;
        }
        
        .loading-text {
          font-size: 16px !important;
          color: #666 !important;
        }
      }
    }
    
    .el-message-box__btns {
      display: none !important; /* 移动端隐藏按钮，使用卡片点击 */
    }
  }
  
  /* 移动端特定类样式 */
  .titer-selection-dialog.mobile-dialog {
    animation: mobileDialogSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
  
  /* 确保移动端页面整体可以滚动 */
  body {
    overflow-x: hidden !important;
    overflow-y: auto !important;
    
    /* 防止移动端页面缩放 */
    touch-action: manipulation !important;
  }
  
  /* 修复可能的对话框遮罩层问题 */
  .el-overlay.is-message-box {
    overflow-y: auto !important;
    padding: 3vh 2.5vw !important;
    
    /* 支持安全区域 */
    padding-top: max(3vh, env(safe-area-inset-top)) !important;
    padding-bottom: max(3vh, env(safe-area-inset-bottom)) !important;
    padding-left: max(2.5vw, env(safe-area-inset-left)) !important;
    padding-right: max(2.5vw, env(safe-area-inset-right)) !important;
  }
  
  /* 移动端触摸优化 */
  .titer-option {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    touch-action: manipulation !important;
  }
}

/* 移动端对话框动画 */
@keyframes mobileDialogSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 超小屏幕适配（如小屏手机） */
@media (max-width: 480px) {
  .titer-selection-dialog {
    width: 98vw !important;
    margin: 2vh auto !important;
    max-height: 96vh !important;
    
    .el-message-box__content {
      .titer-selection-content {
        padding: 16px 12px !important;
      }
      
      .titer-options {
        gap: 12px !important;
        padding: 16px 8px !important;
        
        .titer-option {
          min-height: 100px !important;
          padding: 16px 12px !important;
          
          .titer-option-icon {
            font-size: 28px !important;
            margin-bottom: 10px !important;
          }
          
          .titer-option-title {
            font-size: 18px !important;
          }
          
          .titer-option-desc {
            font-size: 13px !important;
          }
          
          .titer-option-badge,
          .titer-option-feature {
            font-size: 10px !important;
            padding: 3px 8px !important;
            top: 10px !important;
            right: 10px !important;
          }
        }
      }
    }
  }
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: #48D1CC;
  border-color: #48D1CC;
  color: #FFFFFF;
}

.el-button--cyan {
  background-color: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

/* text color */
.text-navy {
	color: #1ab394;
}

.text-primary {
	color: inherit;
}

.text-success {
	color: #1c84c6;
}

.text-info {
	color: #23c6c8;
}

.text-warning {
	color: #f8ac59;
}

.text-danger {
	color: #ed5565;
}

.text-muted {
	color: #888888;
}

/* image */
.img-circle {
	border-radius: 50%;
}

.img-lg {
	width: 120px;
	height: 120px;
}

.avatar-upload-preview {
	position: absolute;
	top: 50%;
	transform: translate(50%, -50%);
	width: 200px;
	height: 200px;
	border-radius: 50%;
	box-shadow: 0 0 4px #ccc;
	overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost{
	opacity: .8;
	color: #fff!important;
	background: #42b983!important;
}

/* 表格右侧工具栏样式 */
.top-right-btn {
	margin-left: auto;
}
