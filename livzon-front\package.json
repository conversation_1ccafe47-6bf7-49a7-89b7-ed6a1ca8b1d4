{"name": "LivZon", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc --noEmit && vite build ", "build:prod": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^2.0.0", "@element-plus/icons-vue": "^2.3.1", "@types/three": "^0.176.0", "@types/vue-router": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "buffer": "^6.0.3", "gsap": "^3.13.0", "hotkeys-js": "^3.13.7", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "minio": "^8.0.0", "minio-js": "^1.0.7", "qrcode": "^1.5.4", "simplex-noise": "^4.0.3", "three": "^0.176.0", "tiff.js": "^1.0.0", "tz-minio-upload_beta": "^0.1.0", "utif": "^3.1.0", "vue": "^3.2.35", "vue-easy-lightbox": "^1.19.0", "vue-qr": "^4.0.9", "vue-qrcode": "^2.2.2", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@highlightjs/vue-plugin": "^2.1.0", "@types/file-saver": "^2.0.5", "@types/js-cookie": "^3.0.2", "@types/node": "^17.0.21", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^2.2.0", "@vitejs/plugin-vue-jsx": "^1.3.7", "@vue/compiler-sfc": "^3.2.36", "@vueuse/core": "^8.5.0", "axios": "^0.27.2", "consola": "^2.15.3", "core-js": "^3.23.5", "cron-validator": "^1.3.1", "echarts": "^5.3.3", "element-plus": "^2.2.5", "eslint": "^8.14.0", "eslint-plugin-vue": "^8.6.0", "fast-glob": "^3.2.11", "file-saver": "^2.0.5", "fuse.js": "^6.6.2", "js-cookie": "^3.0.1", "jsencrypt": "^3.2.1", "nanoid": "^4.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.16", "quill": "^1.3.7", "sass": "^1.52.1", "screenfull": "^6.0.2", "sortablejs": "^1.14.0", "stylus": "^0.63.0", "stylus-loader": "^8.1.0", "typescript": "^4.6.4", "unplugin-auto-import": "^0.9.3", "unplugin-vue-components": "^0.21.1", "vite": "^2.9.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-svg-loader": "^3.4.0", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.0.5", "vue-router": "^4.3.3", "vue-tsc": "^0.38.4", "vue3-highlightjs": "^1.0.5"}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "engines": {"node": ">= 14"}}