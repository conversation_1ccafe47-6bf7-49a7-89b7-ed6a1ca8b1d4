<template>
  <div class="login-container">
    <!-- 预加载图片 -->
    <img v-show="false" :src="backgroundImageUrl" @load="onImageLoad" />
    
    <!-- 背景层 -->
    <div class="login-bg" :class="{ 'bg-loaded': isBgLoaded }" :style="{ backgroundImage: `url(${backgroundImageUrl})` }">
      <!-- 背景遮罩 -->
      <div class="bg-overlay"></div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="login-content">
      <!-- 左侧欢迎区域 -->
      <div class="welcome-section">
        <div class="welcome-content">
          <h1 class="welcome-title"></h1>
          <p class="welcome-description"></p>
        </div>
      </div>
      
      <!-- 右侧登录表单 -->
      <div class="form-section">
        <div class="login-form-wrapper">
          <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
            <div class="form-header">
              <h2 class="form-title">欢迎登录平台</h2>
            </div>
            
            <div class="form-content">
              <el-form-item prop="username" class="form-item">
                <el-input 
                  v-model="loginForm.username" 
                  type="text" 
                  auto-complete="off" 
                  placeholder="请输入登录账号"
                  class="modern-input"
                >
                  <template #prefix>
                    <svg-icon icon-class="user" class="input-icon" />
                    <div class="input-divider"></div>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-form-item prop="password" class="form-item">
                <el-input 
                  v-model="loginForm.password" 
                  type="password" 
                  auto-complete="off" 
                  placeholder="请输入登录密码"
                  class="modern-input password-input"
                  @keyup.enter="handleLogin" 
                  @blur="findRole"
                >
                  <template #prefix>
                    <svg-icon icon-class="password" class="input-icon" />
                    <div class="input-divider"></div>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-form-item prop="roleId" v-show="isRole" class="form-item">
                <el-select 
                  v-model="loginForm.roleId" 
                  placeholder="请选择角色" 
                  auto-complete="off" 
                  class="modern-select"
                >
                  <template #prefix>
                    <svg-icon icon-class="user" class="input-icon" />
                    <div class="input-divider"></div>
                  </template>
                  <el-option v-for="role in roleSelect" :key="role.roleId" :label="role.roleName" :value="role.roleId">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item prop="code" class="form-item captcha-item">
                <div class="captcha-wrapper">
                  <el-input 
                    v-model="loginForm.code" 
                    auto-complete="off" 
                    placeholder="验证码"
                    class="modern-input captcha-input"
                    @keyup.enter="handleLogin"
                  >
                    <template #prefix>
                      <svg-icon icon-class="validCode" class="input-icon" />
                      <div class="input-divider"></div>
                    </template>
                  </el-input>
                  <div class="captcha-code">
                    <img :src="codeUrl" @click="getCode()" class="captcha-img" title="获取验证码" />
                  </div>
                </div>
              </el-form-item>
              
              <div class="form-options">
                <el-checkbox v-model="loginForm.rememberMe" class="remember-checkbox">
                  记住密码
                </el-checkbox>
              </div>
              
              <el-form-item class="form-item login-btn-item">
                <el-button 
                  :loading="loading" 
                  type="primary" 
                  size="large" 
                  class="login-btn"
                  @click="handleLogin"
                >
                  <span v-if="!loading">立即登陆</span>
                  <span v-else>登录中...</span>
                </el-button>
              </el-form-item>
              
              <!-- 立即注册链接紧挨着登录按钮 -->
              <div class="register-wrapper" v-if="register">
                <router-link class="register-link" :to="'/register'">立即注册</router-link>
              </div>
              
              <div class="form-footer">
                <span>荧枢⊙FluoroCore</span>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="Login" setup>
import Login from "@/api/request/login";
import { ref, onMounted } from 'vue';
import preload from '@/utils/preload';

const {
  loginFormRef, loginForm, loginRules, codeUrl, loading, getCode, handleLogin, roleSelect, findRole,
  register, isRole,
} = Login();

// 添加背景图加载状态
const isBgLoaded = ref(false);

// 动态导入背景图片
const backgroundImageUrl = new URL('../assets/images/login-background.jpg', import.meta.url).href;

// 图片加载完成回调
const onImageLoad = () => {
  isBgLoaded.value = true;
};

// 在组件挂载时预加载背景图
onMounted(async () => {
  await preload.preloadImage(backgroundImageUrl);
  isBgLoaded.value = true;
});
</script>

<style lang="scss" scoped>
/**
 * 现代化登录页面样式 - 移动端优化版本
 * 包含响应式设计、动画效果、移动端适配和美化样式
 */

// 颜色变量定义
$primary-color: #1890ff;
$primary-hover: #40a9ff;
$primary-active: #096dd9;
$bg-color: #f0f2f5;
$white: #ffffff;
$text-primary: #262626;
$text-secondary: #8c8c8c;
$text-disabled: #bfbfbf;
$border-color: #d9d9d9;
$border-hover: #40a9ff;
$shadow-1: 0 2px 8px rgba(0, 0, 0, 0.15);
$shadow-2: 0 4px 16px rgba(0, 0, 0, 0.15);
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

// 主容器
.login-container {
  display: flex;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  
  // 移动端视口优化
  @supports (height: 100dvh) {
    min-height: 100dvh;
  }
}

// 背景层
.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: $gradient-primary;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;

  &.bg-loaded {
    opacity: 1;
  }
  
  // 移动端背景优化
  @media (max-width: 768px) {
    background-attachment: scroll; // 避免iOS背景固定问题
    background-size: cover;
  }
}

// 背景遮罩
.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(1px);
}

// 主要内容区域
.login-content {
  display: flex;
  width: 100%;
  position: relative;
  z-index: 2;
  animation: slideInUp 1s ease-out;
}

// 左侧欢迎区域
.welcome-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 60px;
  color: $white;
  
  .welcome-content {
    max-width: 480px;
    
    .welcome-title {
      font-size: 48px;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 24px;
      background: linear-gradient(45deg, #fff, #e6f7ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: fadeInLeft 1s ease-out 0.3s both;
    }
    
    .welcome-description {
      font-size: 18px;
      line-height: 1.6;
      margin-bottom: 48px;
      opacity: 0.9;
      animation: fadeInLeft 1s ease-out 0.9s both;
    }
  }
}

// 右侧表单区域
.form-section {
  width: 520px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.login-form-wrapper {
  width: 100%;
  max-width: 400px;
  padding: 60px 40px;
  animation: slideInRight 1s ease-out 0.5s both;
}

.login-form {
  width: 100%;
  
  .form-header {
    text-align: center;
    margin-bottom: 48px;
    
    .form-title {
      font-size: 32px;
      font-weight: 700;
      color: #051634 !important;
      margin-bottom: 8px;
      
      // 简化样式，确保始终显示深蓝色
      background: none !important;
      -webkit-background-clip: unset !important;
      -webkit-text-fill-color: #051634 !important;
    }
    
    .form-subtitle {
      font-size: 14px;
      color: $text-secondary;
      margin: 0;
    }
  }
  
  .form-content {
    .form-item {
      margin-bottom: 24px;
      
      &.captcha-item {
        margin-bottom: 16px;
      }
      
      &.login-btn-item {
        margin-bottom: 4px;
      }
    }
  }
}

// 现代化输入框样式
.modern-input {
  height: 48px !important;
  
  :deep(.el-input__wrapper) {
    border-radius: 12px;
    border: 2px solid #e0e0e0;
    background: #fafafa;
    box-shadow: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:hover {
      background: #f5f5f5;
      border-color: rgba(24, 144, 255, 0.5);
    }
    
    &.is-focus {
      background: $white;
      border-color: $primary-color;
      box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
    }
  }
  
  :deep(.el-input__inner) {
    height: 44px;
    line-height: 44px;
    font-size: 15px;
    color: #000000;
    font-weight: 500;
    
    &::placeholder {
      color: $text-disabled;
      font-size: 14px;
    }
  }
}

.modern-select {
  width: 100%;
  height: 48px;
  
  :deep(.el-input__wrapper) {
    border-radius: 12px;
    border: 2px solid #e0e0e0;
    background: #fafafa;
    box-shadow: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:hover {
      background: #f5f5f5;
      border-color: rgba(24, 144, 255, 0.5);
    }
    
    &.is-focus {
      background: $white;
      border-color: $primary-color;
      box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
    }
  }
  
  :deep(.el-input__inner) {
    height: 44px;
    line-height: 44px;
    font-size: 15px;
    color: #000000;
    font-weight: 500;
    
    &::placeholder {
      color: $text-disabled;
      font-size: 14px;
    }
  }
  
  // 移除角色选择框的默认外边距
  :deep(*) {
    margin-right: 3px !important;
  }
  
  :deep(.el-input__suffix) {
    margin-left: 0 !important;
  }
  
  :deep(.el-input__suffix-inner) {
    margin-left: 0 !important;
  }
}

// 下拉菜单样式美化
:deep(.el-select-dropdown) {
  border-radius: 12px;
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  padding: 4px;
  margin-top: 8px;
  
  .el-select-dropdown__item {
    height: 44px;
    line-height: 44px;
    padding: 0 16px;
    font-size: 15px;
    color: #000000;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
    margin: 2px 0;
    background: transparent;
    
    &:hover {
      background: #f5f5f5;
      color: #000000;
    }
    
    &.selected {
      background: #fafafa;
      color: $primary-color;
      font-weight: 600;
      
      &::after {
        content: '';
      }
    }
  }
}

// 输入框图标
.input-icon {
  color: $text-secondary;
  font-size: 16px;
  transition: color 0.3s ease;
}

// 验证码部分
.captcha-wrapper {
  display: flex;
  gap: 12px;
  
  .captcha-input {
    flex: 1;
  }
  
  .captcha-code {
    width: 120px;
    height: 48px;
    border-radius: 12px;
    border: 2px solid #e0e0e0;
    background: #fafafa;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
    // 移动端优化触摸区域
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    
    &:hover {
      background: #f5f5f5;
      border-color: rgba(24, 144, 255, 0.5);
      transform: scale(1.02);
    }
    
    &:active {
      transform: scale(0.98);
    }
    
    .captcha-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      border: none;
      border-radius: 10px;
      // 防止图片被选中
      user-select: none;
      -webkit-user-select: none;
    }
  }
}

// 表单选项
.form-options {
  margin-bottom: 32px;
  
  .remember-checkbox {
    // 移动端优化复选框点击区域
    :deep(.el-checkbox__input) {
      .el-checkbox__inner {
        width: 18px;
        height: 18px;
        border-radius: 4px;
      }
    }
    
    :deep(.el-checkbox__label) {
      color: $text-secondary;
      font-size: 14px;
      padding-left: 8px;
    }
    
    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
      background-color: $primary-color;
      border-color: $primary-color;
    }
  }
}

// 登录按钮
.login-btn {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: #0066cb !important;
  border: none;
  box-shadow: $shadow-1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  // 移动端优化
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  
  &:hover {
    background: #0052a3 !important;
    transform: translateY(-2px);
    box-shadow: $shadow-2;
  }
  
  &:active {
    background: #004182 !important;
    transform: translateY(0);
  }
  
  &.is-loading {
    opacity: 0.8;
  }
}

// 输入框内部竖线样式
.input-divider {
  width: 1px;
  height: 20px;
  background-color: #333333;
  margin: 0 12px 0 8px;
  align-self: center;
}

// 注册链接容器（紧挨着登录按钮）
.register-wrapper {
  text-align: left;
  margin-top: 0px;
  margin-bottom: 20px;
  
  .register-link {
    color: $primary-color;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
    // 移动端优化点击区域
    display: inline-block;
    padding: 8px 0;
    -webkit-tap-highlight-color: transparent;
    
    &:hover {
      color: $primary-hover;
    }
    
    &:active {
      color: $primary-active;
    }
  }
}

// 表单底部
.form-footer {
  text-align: center;
  margin-top: 20px;
  
  span {
    color: #061632;
    font-size: 20px;
    font-weight: 500;
  }
}

// 动画定义
@keyframes slideInUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

// 响应式设计 - 优化移动端体验
@media (max-width: 1200px) {
  .welcome-section {
    padding: 60px 40px;
    
    .welcome-content .welcome-title {
      font-size: 40px;
    }
  }
  
  .form-section {
    width: 480px;
  }
}

// 平板适配
@media (max-width: 992px) {
  .login-content {
    flex-direction: column;
  }
  
  .welcome-section {
    padding: 40px 20px 30px;
    min-height: 30vh;
    
    .welcome-content {
      text-align: center;
      
      .welcome-title {
        font-size: 36px;
        margin-bottom: 16px;
      }
      
      .welcome-description {
        font-size: 16px;
        margin-bottom: 24px;
      }
    }
  }
  
  .form-section {
    width: 100%;
    min-height: 70vh;
    background: rgba(255, 255, 255, 0.98);
  }
  
  .login-form-wrapper {
    padding: 40px 30px 60px;
    max-width: 450px;
  }
}

// 移动端适配 - 主要优化
@media (max-width: 768px) {
  .login-container {
    // 防止iOS底部安全区域问题
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .welcome-section {
    padding: 20px 20px 20px;
    min-height: 25vh;
    
    .welcome-content {
      .welcome-title {
        font-size: 28px;
        margin-bottom: 12px;
      }
      
      .welcome-description {
        font-size: 14px;
        margin-bottom: 16px;
      }
    }
  }
  
  .form-section {
    min-height: 75vh;
    background: rgba(255, 255, 255, 0.98);
    border-left: none;
  }
  
  .login-form-wrapper {
    max-width: 100%;
    padding: 30px 20px 40px;
  }
  
  .login-form {
    .form-header {
      margin-bottom: 32px;
      
      .form-title {
        font-size: 26px;
      }
    }
    
    .form-content .form-item {
      margin-bottom: 20px;
      
      &.captcha-item {
        margin-bottom: 16px;
      }
    }
  }
  
  // 移动端输入框优化
  .modern-input,
  .modern-select {
    height: 52px !important; // 移动端增大触摸区域
    
    :deep(.el-input__inner) {
      height: 48px;
      line-height: 48px;
      font-size: 16px; // 防止iOS缩放
    }
  }
  
  .login-btn {
    height: 52px;
    font-size: 16px;
  }
  
  // 验证码移动端优化
  .captcha-wrapper {
    gap: 8px;
    
    .captcha-code {
      width: 100px;
      height: 52px;
    }
  }
  
  .form-options {
    margin-bottom: 24px;
    
    .remember-checkbox {
      :deep(.el-checkbox__label) {
        font-size: 15px;
      }
    }
  }
  
  .form-footer span {
    font-size: 18px;
  }
}

// 小屏手机适配
@media (max-width: 480px) {
  .welcome-section {
    padding: 15px 15px 15px;
    min-height: 20vh;
    
    .welcome-content .welcome-title {
      font-size: 24px;
    }
  }
  
  .login-form-wrapper {
    padding: 25px 15px 35px;
  }
  
  .login-form .form-header .form-title {
    font-size: 22px;
  }
  
  // 验证码在小屏幕上堆叠显示
  .captcha-wrapper {
    flex-direction: column;
    gap: 12px;
    
    .captcha-code {
      width: 100%;
      height: 52px;
    }
  }
  
  .form-footer span {
    font-size: 16px;
  }
}

// 超小屏设备适配
@media (max-width: 375px) {
  .login-form-wrapper {
    padding: 20px 12px 30px;
  }
  
  .login-form .form-header {
    margin-bottom: 24px;
    
    .form-title {
      font-size: 20px;
    }
  }
  
  .modern-input,
  .modern-select,
  .login-btn {
    height: 48px !important;
    
    :deep(.el-input__inner) {
      height: 44px;
      line-height: 44px;
      font-size: 15px;
    }
  }
  
  .captcha-code {
    height: 48px !important;
  }
}

// 横屏移动设备适配
@media (max-width: 768px) and (orientation: landscape) {
  .login-content {
    flex-direction: row;
  }
  
  .welcome-section {
    display: none; // 横屏时隐藏欢迎区域以节省空间
  }
  
  .form-section {
    width: 100%;
    min-height: 100vh;
  }
  
  .login-form-wrapper {
    padding: 20px;
    max-width: 400px;
  }
  
  .login-form .form-header {
    margin-bottom: 20px;
  }
  
  .form-content .form-item {
    margin-bottom: 16px;
  }
}

// 高分辨率移动设备优化
@media (-webkit-min-device-pixel-ratio: 2) and (max-width: 768px) {
  .captcha-img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

// Element Plus 样式覆盖
:deep(.el-form-item__error) {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
  
  @media (max-width: 768px) {
    font-size: 13px;
  }
}

:deep(.el-loading-mask) {
  border-radius: 12px;
}

// 移动端下拉菜单优化
@media (max-width: 768px) {
  :deep(.el-select-dropdown) {
    max-height: 40vh; // 限制下拉菜单高度
    
    .el-select-dropdown__item {
      height: 48px;
      line-height: 48px;
      font-size: 16px;
    }
  }
}

// 禁用移动端文本选择和拖拽
@media (max-width: 768px) {
  .login-container {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    
    // 允许输入框内文本选择
    input, textarea {
      -webkit-user-select: text;
      -khtml-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
    }
  }
}
</style>

