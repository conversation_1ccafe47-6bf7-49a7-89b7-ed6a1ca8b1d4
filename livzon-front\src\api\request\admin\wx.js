import request from '@/utils/request'

/**
 * 获取微信JS-SDK配置
 * @param {string} url 当前页面URL
 * @returns {Promise}
 */
export function getWxConfig(url) {
    return request({
        url: '/wechat/config',
        method: 'get',
        params: { url },
        headers: {
            isToken: false // 不携带token
        },
        withCredentials: false // 不携带cookie
    })
}

/**
 * 获取微信授权
 * @param {string} url 当前页面URL
 * @returns {Promise}
 */
export function getWxAuth(url) {
    return request({
        url: '/wechat/auth',
        method: 'get',
        params: { url },
        headers: {
            isToken: false, // 不携带token
            'Content-Type': 'application/json'
        },
        withCredentials: false, // 不携带cookie
        timeout: 10000, // 设置超时时间为10秒
        validateStatus: function (status) {
            return status >= 200 && status < 500; // 接受所有2xx和3xx的状态码
        }
    })
} 