<template>
    <div class="upload-container">
        <el-upload ref="uploadRef" class="my-upload" action="#" :multiple="true"
            accept=".jpg,.png,.jpeg,.tiff,.tif,bmp" drag :before-upload="beforeUpload" :http-request="handleRequest"
            :auto-upload="true" :disabled="isUploading" :limit="1001"
            :on-exceed="handleExceed" :show-file-list="false">
            <template #default>
                <div class="upload-tip">
                    <span class="warning-title">温馨提示</span>
                    <div class="tip-list">
                        <div class="tip-item">1. 格式：jpg/png/jpeg/tiff/tif/bmp<span style="color: #ff0000;">（20倍物镜最佳）</span>，单张图片体积不超过10MB，一次最多上传1000张</div>
                        <div class="tip-item">2. 请尽可能准确选择试剂与显微镜型号；</div>
                        <div class="tip-item tip-danger">3. 当前算法版本针对欧蒙试剂搭配EuroPattern生成的图片判读效果最优</div>
                    </div>
                </div>
                <div v-show="isShowButton" class="el-upload__text">
                    将文件拖到此处，或 <em>点击上传</em>
                </div>
                <el-progress v-show="showProgress" :text-inside="true" :stroke-width="18" :percentage="progressPercent"
                    status="success" />
            </template>

            <template #tip>
                <div class="file-stats" v-if="filesList.length > 0">
                    <el-text type="info" class="file-stats-text">您已选择 {{ filesList.length }} 张图片，共 {{ formatFileSize(totalFileSize) }}</el-text>
                </div>
            </template>
        </el-upload>

        <!-- 自定义文件列表渲染 -->
        <div class="custom-upload-list" v-if="filesList.length > 0">
            <div v-for="file in sortedFilesList" :key="file.uid" class="custom-upload-item">
                <span class="file-name">{{ file.name }}</span>
                <span v-if="file.status === 'success'" class="file-success">100%</span>
                <span v-else-if="file.status === 'fail'" class="file-error">{{ file.error || '失败' }}</span>
                <el-button 
                    v-if="file.status === 'fail'"
                    type="primary" 
                    size="small"
                    @click.stop="retryUpload(file)"
                    class="retry-button"
                >重试</el-button>
                <el-icon
                    class="file-remove"
                    @click.stop="removeFile(file)"
                    style="cursor:pointer;margin-left:8px;color:#f56c6c;"
                    title="移除"
                >
                    <Close />
                </el-icon>
            </div>
        </div>

        <div class="action-footer">
            <el-text type="success" class="file-stats-text">成功上传：{{ filesList.filter(f => f.status === 'success').length }} 张图片</el-text>
            <el-button
                v-if="filesList.some(f => f.status === 'fail')"
                type="primary"
                size="small"
                class="retry-all-btn unified-retry-btn"
                @click="retryAllFailed"
            >
                <el-icon style="margin-right:4px;"><Refresh /></el-icon>
                一键重试
            </el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { Close, Refresh } from '@element-plus/icons-vue';
const baseURL = import.meta.env.VITE_APP_BASE_API;
import { getToken } from "@/utils/auth";

/**
 * @description 防抖函数，用于限制函数调用频率
 * @param {Function} fn 需要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
const debounce = (fn, delay) => {
    let timer = null;
    return function (...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
            fn.apply(this, args);
        }, delay);
    };
};

const props = defineProps({
    reagentManufacturer: String,
    imagingDevice: String,
    objective: String,
})
const emit = defineEmits(['start-upload', 'upload-complete', 'update:isUploading']);

const uploadRef = ref(null);
const filesList = ref([]);
const showProgress = ref(false);
const progressPercent = ref(0);
const isShowButton = ref(true);
const isUploading = ref(false);
const lastWarningTime = ref(0);
const WARNING_INTERVAL = 3000;
const totalFileSize = ref(0);

const showDebouncedMessage = debounce((message, type = 'warning') => {
    const now = Date.now();
    if (now - lastWarningTime.value > WARNING_INTERVAL) {
        ElMessage[type](message);
        lastWarningTime.value = now;
    }
}, 300);

const showWarning = (message) => {
    showDebouncedMessage(message, 'warning');
};

const handleRequest = async (options) => {
    const formData = new FormData();
    formData.append('files', options.file);
    formData.append('reagentManufacturer', props.reagentManufacturer);
    formData.append('imagingDevice', props.imagingDevice);
    formData.append('objective', props.objective);
    
    if (!props.reagentManufacturer || !props.imagingDevice || !props.objective) {
        showWarning('请先选择试剂厂家、成像设备和物镜');
        options.onError(new Error('请先选择试剂厂家、成像设备和物镜'));
        return;
    }

    try {
        isUploading.value = true;
        emit('update:isUploading', true);
        showProgress.value = true;
        
        const response = await axios.post(baseURL + `/file/upload`, formData, {
            headers: {
                Authorization: "Bearer " + getToken(),
            },
            onUploadProgress: (progressEvent) => {
                if (progressEvent.lengthComputable) {
                    const fileProgress = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total
                    );
                    const idx = filesList.value.findIndex(f => f.uid === options.file.uid);
                    if (idx !== -1) {
                        filesList.value[idx].percentage = fileProgress;
                    }
                    
                    const totalProgress = filesList.value.reduce((acc, file) => {
                        if (file.status === 'success') return acc + 100;
                        if (file.status === 'fail') return acc;
                        return acc + (file.percentage || 0);
                    }, 0);
                    
                    progressPercent.value = filesList.value.length
                        ? Math.round(totalProgress / filesList.value.length)
                        : 0;
                    options.onProgress({ percent: fileProgress });
                }
            },
        });

        const idx = filesList.value.findIndex(f => f.uid === options.file.uid);
        if (response.data.code === 200) {
            if (idx !== -1) {
                filesList.value[idx].status = 'success';
                filesList.value[idx].percentage = 100;
                filesList.value[idx].error = null;
            }
            options.onSuccess({ data: '上传成功' });
        } else {
            const errorMsg = response.data.msg || '上传失败';
            if (idx !== -1) {
                filesList.value[idx].status = 'fail';
                filesList.value[idx].percentage = 0;
                filesList.value[idx].error = errorMsg;
                filesList.value[idx].raw = options.file;
            }
            showDebouncedMessage(errorMsg, 'error');
            options.onError(new Error(errorMsg));
        }

        if (filesList.value.every(file => file.status === 'success' || file.status === 'fail')) {
            isUploading.value = false;
            emit('update:isUploading', false);
            if (filesList.value.some(f => f.status === 'success')) {
                triggerRefresh(false);
            } else {
                setTimeout(() => {
                    showProgress.value = false;
                    progressPercent.value = 0;
                }, 1000);
            }
        }
    } catch (error) {
        const idx = filesList.value.findIndex(f => f.uid === options.file.uid);
        let errorMsg = error?.response?.data?.msg || error.message || '上传失败';
        if (idx !== -1) {
            filesList.value[idx].status = 'fail';
            filesList.value[idx].percentage = 0;
            filesList.value[idx].error = errorMsg;
            filesList.value[idx].raw = options.file;
        }
        showDebouncedMessage(errorMsg, 'error');
        options.onError(error);

        if (filesList.value.every(file => file.status === 'success' || file.status === 'fail')) {
            isUploading.value = false;
            emit('update:isUploading', false);
            if (filesList.value.some(f => f.status === 'success')) {
                triggerRefresh(false);
            } else {
                setTimeout(() => {
                    showProgress.value = false;
                    progressPercent.value = 0;
                }, 1000);
            }
        }
    }
};

const beforeUpload = (file) => {
    const validTypes = ['image/jpeg', 'image/png', 'image/tiff', 'image/tif', 'image/bmp'];
    const validSize = file.size / 1024 / 1024 < 10;
    
    if (!props.reagentManufacturer || !props.imagingDevice || !props.objective) {
        showWarning('请先选择试剂厂家、显微镜型号和物镜');
        return false;
    }

    if (filesList.value.length > 1000) {
        showDebouncedMessage('最多只能上传1000张图片!', 'error');
        return false;
    }

    if (!validTypes.includes(file.type)) {
        showDebouncedMessage('仅支持 JPG/PNG/TIFF/TIF/BMP 格式图片!', 'error');
        return false;
    }

    if (!validSize) {
        showDebouncedMessage('图片大小不能超过10MB!', 'error');
        return false;
    }

    filesList.value.push({
        name: file.name,
        size: file.size,
        uid: file.uid,
        status: 'ready',
        percentage: 0,
        raw: file
    });
    return true;
};

const handleExceed = () => {
    showDebouncedMessage('最多只能上传1000张图片!', 'error');
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
};

const removeFile = (file) => {
    const idx = filesList.value.findIndex(f => f.uid === file.uid);
    if (idx !== -1) {
        filesList.value.splice(idx, 1);
    }
    uploadRef.value?.handleRemove?.(file);
};

const retryUpload = async (file) => {
    if (!props.reagentManufacturer || !props.imagingDevice || !props.objective) {
        showWarning('请先选择试剂厂家、成像设备和物镜');
        return;
    }

    const idx = filesList.value.findIndex(f => f.uid === file.uid);
    if (idx !== -1) {
        filesList.value[idx].status = 'ready';
        filesList.value[idx].percentage = 0;
        filesList.value[idx].error = null;
    }

    const options = {
        file: file.raw || file,
        onProgress: (progress) => {
            if (idx !== -1) {
                filesList.value[idx].percentage = progress.percent;
            }
        },
        onSuccess: () => {
            if (idx !== -1) {
                filesList.value[idx].status = 'success';
                filesList.value[idx].percentage = 100;
                filesList.value[idx].error = null;
            }
        },
        onError: (error) => {
            if (idx !== -1) {
                filesList.value[idx].status = 'fail';
                filesList.value[idx].percentage = 0;
                filesList.value[idx].error = error.message || '上传失败';
            }
        }
    };

    await handleRequest(options);
};

const retryAllFailed = async () => {
    const failedFiles = filesList.value.filter(f => f.status === 'fail');
    for (const file of failedFiles) {
        await retryUpload(file);
    }
};

const triggerRefresh = (shouldClearFiles = false) => {
    setTimeout(() => {
        emit('upload-complete');
        if (shouldClearFiles) {
            filesList.value = [];
            uploadRef.value?.clearFiles();
            isShowButton.value = true;
            showProgress.value = false;
            progressPercent.value = 0;
            totalFileSize.value = 0;
            isUploading.value = false;
            emit('update:isUploading', false);
        } else {
            isShowButton.value = true;
            showProgress.value = false;
            progressPercent.value = 0;
            isUploading.value = false;
            emit('update:isUploading', false);
        }
    }, 1000);
};

watch(filesList, (newList) => {
  totalFileSize.value = newList.reduce((total, file) => total + (file.size || 0), 0);
}, { deep: true });

/**
 * @description 对文件列表进行排序，将失败的文件排在前面
 */
const sortedFilesList = computed(() => {
    return [...filesList.value].sort((a, b) => {
        if (a.status === 'fail' && b.status !== 'fail') return -1;
        if (a.status !== 'fail' && b.status === 'fail') return 1;
        return 0;
    });
});
</script>

<style scoped lang="scss">
.upload-container {
    width: 100%;
    margin: 0 auto;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    box-sizing: border-box;
    max-height: 90vh;
    overflow-y: auto;
}

@media screen and (max-width: 768px) {
    .upload-container {
        max-width: 98vw;
        min-width: 320px;
        width: 95vw;
        padding: 0.5rem;
    }
}

:deep(.my-upload) {
    .el-upload-list__item-status-label {
        display: none !important;
    }
    
    .el-upload-list {
        max-height: 240px;
        overflow-y: auto;

        .el-upload-list__item {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            margin-bottom: 8px;
            padding: 6px 12px;
            display: flex;
            align-items: center;
            transition: box-shadow 0.2s, background-color 0.2s;
            box-shadow: 0 1px 4px rgba(24, 144, 255, 0.08);

            @media screen and (max-width: 768px) {
                flex-wrap: wrap;
                gap: 8px;
            }

            &:hover {
                background-color: #bae7ff;
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);

                .el-icon--close {
                    color: #ff4d4f;
                    font-size: 25px;
                    font-weight: bold;
                }
            }
        }
    }

    .el-upload {
        width: 100%;
        padding: 0.5rem;

        @media screen and (max-width: 768px) {
            padding: 0.25rem;
        }

        .el-upload-dragger {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
            background: #fff;
            transition: all 0.3s ease;
            height: 240px;
            @media screen and (max-width: 768px) {
                padding: 0.75rem;
                gap: 0.5rem;
            }

            &:hover {
                border-color: #409eff;
            }
        }
    }
}

.upload-tip {
    margin-top: 1rem;
    text-align: center;
    
    .warning-title {
        font-size: 20px;
        color: #ff0000;
        font-weight: bold;
        margin-right: 8px;

        @media screen and (max-width: 768px) {
            font-size: 18px;
        }
    }
    
    .el-text {
        font-size: 16px;
        color: #333;
        
        &[type="danger"] {
            font-size: 24px;
            color: #ff0000;
            font-weight: bold;
        }
    }
}

.action-footer {
    margin-top: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
    flex-wrap: wrap;
    gap: 1rem;

    @media screen and (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        padding: 0;
    }
}

:deep(.el-progress) {
    margin-top: 1rem;
    width: 100%;
}

:deep(.el-upload__text) {
    font-size: 16px;
    color: #606266;
    word-break: break-all;
    word-wrap: break-word;
    @media screen and (max-width: 768px) {
        font-size: 13px;
        line-height: 1.3;
        padding: 0 2px;
        text-align: center;
    }
    em {
        color: #409eff;
        font-style: normal;
        cursor: pointer;
        &:hover {
            text-decoration: underline;
        }
    }
}

.file-stats {
    margin-top: 0.5rem;
    text-align: center;
}

.file-stats-text {
    color: #4682B4; /* 钢蓝色，比之前的浅蓝色深 */
    font-weight: bold;
}

.custom-upload-item {
    display: flex;
    align-items: center;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 6px 12px;
    font-size: 16px;
    box-shadow: 0 1px 4px rgba(24, 144, 255, 0.08);
    .file-name {
        flex: 1;
        color: #333;
        word-break: break-all;
    }
    .file-success {
        color: #52c41a;
        font-weight: bold;
        margin-left: 8px;
    }
    .file-error {
        color: #ff0000;
        font-weight: bold;
        margin-left: 8px;
    }
    .retry-button {
        margin-left: 8px;
        padding: 2px 8px;
        font-size: 12px;
        height: 24px;
        line-height: 24px;
    }
    .file-remove {
        font-size: 20px;
        margin-left: 8px;
        color: #f56c6c;
        cursor: pointer;
        &:hover {
            color: #ff0000;
        }
    }
}

.tip-list {
  margin-top: 8px;
  text-align: left;
  margin-left: 0.5em;
  padding: 0;
}
.tip-item {
  font-size: 16px;
  color: #333;
  line-height: 1.6;
  display: block;
  word-break: break-all;
  word-wrap: break-word;
  @media screen and (max-width: 768px) {
    font-size: 13px;
    line-height: 1.3;
  }
}
.tip-danger {
  color: #ff0000;
  font-weight: bold;
  font-size: 16px;
  word-break: break-all;
  word-wrap: break-word;
  @media screen and (max-width: 768px) {
    font-size: 13px;
    line-height: 1.3;
  }
}

.custom-upload-list {
  max-height: 220px;
  overflow-y: auto;
  margin-bottom: 1rem;
  border: 2px solid #d9d9d9;
  border-radius: 10px;
  background: #f6fbff;
  padding: 8px 0;
  box-sizing: border-box;
}

.custom-upload-item:hover {
  background-color: #bae7ff;
}

@media screen and (max-width: 768px) {
  .custom-upload-list {
    max-height: 50vh;
    min-width: 0;
    width: 100%;
    padding: 4px 0;
  }
  .custom-upload-item {
    font-size: 14px;
    padding: 6px 8px;
    flex-wrap: wrap;
    word-break: break-all;
    gap: 4px;
    width: 100%;
    min-width: 0;
  }
  .file-name {
    font-size: 14px;
    word-break: break-all;
    max-width: 60vw;
    min-width: 0;
  }
}

.unified-retry-btn {
  background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%) !important;
  color: #fff !important;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.10);
  transition: all 0.18s;
  padding: 0 12px;
  height: 28px;
  display: flex;
  align-items: center;
  font-size: 13px;
  margin-left: auto;
  margin-right: 0;
  min-width: 80px;
  &:hover {
    background: linear-gradient(90deg, #66b1ff 0%, #409eff 100%) !important;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.18);
    transform: translateY(-1px) scale(1.03);
    color: #fff;
  }
}
</style>