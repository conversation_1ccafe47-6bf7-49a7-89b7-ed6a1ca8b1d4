<template>
	<div class="navbar">
		<el-tooltip content="操作手册" effect="dark" placement="bottom">
			<doc id="doc" class="doc-icon" />
		</el-tooltip>
		<hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container"
			@toggleClick="toggleSideBar" v-if="!settingsStore.topNav" />

		<breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!settingsStore.topNav" />
		<top-nav id="topmenu-container" class="topmenu-container" v-if="settingsStore.topNav" />


		<div class="right-menu">
			<div class="info-bar">
				<span v-if="appStore.device !== 'mobile'" class="last-login-info">
					上次登录：{{ lastLongin }}
				</span>
				<span v-if="rolesOptions.length > 1 && currentRoleName" class="role-info">
					角色：{{ currentRoleName }}
				</span>
			</div>
			<template v-if="appStore.device !== 'mobile'">
				<header-search id="header-search" class="right-menu-item" />
				<el-tooltip content="暂时无内容" effect="dark" placement="bottom">
					<git id="git" class="right-menu-item hover-effect" />
				</el-tooltip>
				<screenfull id="screenfull" class="right-menu-item hover-effect" />

				<el-tooltip content="布局大小" effect="dark" placement="bottom">
					<size-select id="size-select" class="right-menu-item hover-effect" style="margin-top: 13px" />
				</el-tooltip>
			</template>
			<div class="avatar-container">
				<el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click"
					ref="downMenu">
					<div class="avatar-wrapper">
						<img :src="userStore.avatar" class="user-avatar" />
					</div>
					<template #dropdown>
						<el-dropdown-menu>
							<router-link to="/user/profile">
								<el-dropdown-item> <el-button>个人中心</el-button></el-dropdown-item>
							</router-link>
							<el-dropdown-item command="setLayout">
								<el-button>布局设置</el-button>
							</el-dropdown-item>
							<el-popover ref="popoverRef" :virtual-ref="buttonRef" trigger="hover" placement="left">
								<template #reference>
									<el-dropdown-item>
										<el-button v-popover="popoverRef" v-click-outside="onClickOutside">
											角色切换
										</el-button>
									</el-dropdown-item>
								</template>
								<div class="menu-list">
									<div
										@click.stop="hadnleCheckRole(item)"
										class="menu-item"
										v-for="(item, index) in rolesOptions"
										:key="item.roleId"
										:class="{ active: roleId == item.roleId }"
									>
										{{ item.roleName }}
									</div>
								</div>
							</el-popover>
							<el-dropdown-item divided command="logout">
								<el-button>退出登录</el-button>
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>
		</div>
	</div>
</template>

<script setup>
import router from "@/router";
import { ref, onMounted, watch, unref } from "vue";
import { ElMessageBox, ClickOutside as vClickOutside } from "element-plus";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import Breadcrumb from "@/components/Breadcrumb/index.vue";
import TopNav from "@/components/TopNav/index.vue";
import Hamburger from "@/components/Hamburger/index.vue";
import Screenfull from "@/components/Screenfull/index.vue";
import SizeSelect from "@/components/SizeSelect/index.vue";
import HeaderSearch from "@/components/HeaderSearch/index.vue";
import Git from "@/components/LivZon/Git/index.vue";
import Doc from "@/components/LivZon/Doc/index.vue";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import request from "@/utils/request";
import Cookies from "js-cookie";
import { switchRole } from "@/api/login";
const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const lastLongin = ref("");
const roleId = ref(localStorage.getItem("roleId") || "");
const rolesOptions = ref([]);
const currentRoleName = ref("");

const buttonRef = ref()
const popoverRef = ref()
const onClickOutside = () => {
	unref(popoverRef).popperRef?.delayHide?.()
}

function toggleSideBar() {
	appStore.toggleSideBar();
}

function handleCommand(command) {
	switch (command) {
		case "setLayout":
			setLayout();
			break;
		case "logout":
			logout();
			break;
		default:
			break;
	}
}

function logout() {
	ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			localStorage.removeItem('hasShownNotice');
			userStore.logOut().then(() => {
				location.href = "/index";
			});
		})
		.catch(() => { });
}

const emits = defineEmits(["setLayout"]);
function setLayout() {
	emits("setLayout");
}

const lastLoginTime = async () => {
	await request({
		url: "/monitor/logininfor/lastLogin",
		method: "get",
	}).then(response => {
		if (response.code === 200) {
			lastLongin.value = response.data;
		}
	});
};

onMounted(() => {
	lastLoginTime()
	findByRoles();
	console.log('Navbar 渲染了');
});

watch(() => router.currentRoute.value.path, (newValue, oldValue) => {
	console.log("路由变化", newValue, "刷新距离上次登录时间");
	lastLoginTime()
},
	{ immediate: true }
);

setInterval(() => {
	lastLoginTime();
}, 1000 * 60);

const findByRoles = async () => {
	await request({
		url: "/findByRoles",
		method: "get",
	}).then(response => {
		if (response.code === 200) {
			rolesOptions.value = response.data;
			const exist = rolesOptions.value.some(item =>(item.roleId == roleId.value));
			if (!exist && rolesOptions.value.length > 0) {
				roleId.value = rolesOptions.value[0].roleId;
				localStorage.setItem("roleId", roleId.value);
			}
			const role = rolesOptions.value.find(item => item.roleId == roleId.value);
			currentRoleName.value = role ? role.roleName : '';
		}
	});
}

const loginForm = {
	username: localStorage.getItem("username"),
	password: decrypt(localStorage.getItem("password")),
	rememberMe: localStorage.getItem("rememberMe"),
	roleId: null,
	isCode: false,
};

/**
 * 处理角色切换
 * @param {Object} e 角色对象
 */
const hadnleCheckRole = (e) => {
	roleId.value = e.roleId;
	loginForm.roleId = e.roleId;
	localStorage.setItem('roleId', e.roleId);
	Cookies.set('roleId', e.roleId);
	popoverRef.value?.hide();
	setRoleCode();
}

/**
 * 切换角色后刷新权限和菜单
 */
const setRoleCode = () => {
	switchRole(loginForm.roleId)
		.then(() => {
			userStore.getInfo().then(() => {
				location.reload();
			});
		})
		.catch(error => {
			console.error("角色切换失败:", error);
		});
}
</script>

<style lang="scss" scoped>
.navbar {
	height: 50px;
	overflow: hidden;
	position: relative;
	background: #fff;
	box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

	@keyframes float {
		0% {
			transform: translateY(0px);
		}

		50% {
			transform: translateY(-5px);
		}

		100% {
			transform: translateY(0px);
		}
	}

	.doc-icon {
		float: left;
		line-height: 50px;
		height: 100%;
		padding: 0 15px;
		font-size: 20px;
		color: #409EFF;
		cursor: pointer;
		animation: float 2s ease-in-out infinite;

		&:hover {
			background: rgba(0, 0, 0, 0.025);
			animation-play-state: paused;
		}
	}

	.hamburger-container {
		line-height: 46px;
		height: 100%;
		float: left;
		cursor: pointer;
		-webkit-tap-highlight-color: transparent;

		&:hover {
			background: rgba(0, 0, 0, 0.025);
		}
	}

	.breadcrumb-container {
		float: left;
	}

	.topmenu-container {
		position: absolute;
		left: 50px;
		width: calc(100% - 100px);
		height: 100%;
		display: flex;
		align-items: center;
		overflow: hidden;

		:deep(.el-menu--horizontal) {
			width: 100%;
			display: flex;
			flex-wrap: nowrap;

			&>.el-menu-item,
			&>.el-sub-menu .el-sub-menu__title {
				position: relative;
				white-space: nowrap;

				&::after {
					content: '';
					position: absolute;
					top: 50%;
					right: 0;
					transform: translateY(-50%);
					width: 1px;
					height: 20px;
					background-color: #dcdfe6;
				}

				&:last-child::after {
					display: none;
				}
			}
		}
	}

	.errLog-container {
		display: inline-block;
		vertical-align: top;
	}

	.right-menu {
		float: right;
		height: 100%;
		line-height: 50px;
		display: flex;
		justify-content: flex-end;

		&:focus {
			outline: none;
		}

		.right-menu-item {
			display: inline-block;
			padding: 0 8px;
			height: 100%;
			font-size: 18px;
			color: #5a5e66;
			vertical-align: text-bottom;

			&.hover-effect {
				cursor: pointer;

				&:hover {
					background: rgba(0, 0, 0, 0.025);
				}
			}
		}

		.avatar-container {
			margin-right: 0;

			.avatar-wrapper {
				display: flex;
				align-items: center;
				margin-top: 5px;
				position: relative;

				.user-avatar {
					cursor: pointer;
					width: 36px;
					height: 36px;
					border-radius: 10px;
					margin-right: 4px;
				}

				.el-icon {
					font-size: 16px;
					color: #5a5e66;
				}
			}
		}

		.info-bar {
			display: flex;
			align-items: center;
		}

		.last-login-info,
		.role-info {
			display: inline-block !important;
			font-size: 12px !important;
			margin-right: 20px !important;
			color: #696969 !important;
			z-index: 999 !important;
			position: relative !important;
			white-space: nowrap;
		}
	}
}

.menu-list {
	box-sizing: border-box;

	.menu-item {
		cursor: pointer;
		box-sizing: border-box;
		padding: 4px 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: .3s;
		border: 1px solid #eee;
		font-size: 14px;
		color: var(--el-button-text-color);
		margin-bottom: 4px;

		&:hover {
			color: #fff;
			background-color: var(--el-color-primary);
		}

		&.active {
			color: #fff;
			background-color: var(--el-color-primary);
		}
	}
}

@media (max-width: 600px) {
	.navbar .right-menu .info-bar {
		flex-direction: column;
		align-items: flex-start;
		width: 100%;
	}
	.navbar .right-menu .last-login-info,
	.navbar .right-menu .role-info {
		margin-right: 0 !important;
		margin-bottom: 2px;
		font-size: 14px !important;
		width: 100%;
		text-align: left;
		background: #fff;
		position: static !important;
		z-index: auto !important;
	}
}
</style>
