<template>
    <div>
        <!-- 非微信环境提示 -->
        <div v-if="!isWeixinBrowser" class="weixin-tip">
            <div class="weixin-tip-inner">
                <img class="weixin-logo" src="https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico" alt="微信LOGO" />
                <div class="weixin-title">请在微信中打开</div>
                <div class="weixin-desc">当前页面需要在微信浏览器中打开才能正常使用</div>
            </div>
        </div>
        <!-- 微信环境下的内容 -->
        <template v-else>
            <div :class="{ 'collapsed-content-hint': collapsedReplyBar }">
            <!-- 添加访问人数显示（移到最外层，绝对定位） -->
            <div class="visitor-count">
                <i class="el-icon-view"></i>
                <div class="count-wrapper">
                    <span>{{ toDayShareNum }}</span>
                    <span>{{ visitorCount }}</span>
                </div>
            </div>
            <!-- 添加HTTP访问提示 -->
            <!-- <div v-if="showHttpWarning" class="http-warning">
                <el-alert title="安全提示" type="warning" :closable="false" show-icon>
                    <p>当前页面使用HTTP协议，微信浏览器可能会拦截访问。</p>
                    <p>如无法正常访问，请点击右上角菜单，选择"继续刷新"。</p>
                </el-alert>
            </div> -->
            <!-- 添加微信右上角分享提示 -->
            <div v-if="showShareTips" class="share-tips" @click="showShareTips = false">
                <div class="share-icon">
                    <span class="text">点击右上角</span>
                    <span class="dots">...</span>
                    <span class="text">分享</span>
                </div>
                <div class="arrow-container">
                    <svg class="arrow-icon" viewBox="0 0 1024 1024" width="45" height="45">
                        <defs>
                            <linearGradient id="arrowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#409EFF" />
                                <stop offset="100%" style="stop-color:#36cfc9" />
                            </linearGradient>
                        </defs>
                        <path d="M512 64L960 512L512 960L512 768L192 768L192 256L512 256L512 64Z"
                            fill="url(#arrowGradient)" transform="rotate(-45 512 512)" />
                    </svg>
                </div>
            </div>
            <!-- 其他内容 -->
            <div class="demo-image__preview">
                <div class="image-container">
                    <div class="logo-title" @click="goToRegister">荧枢<small
                            style="font-size:12px;vertical-align:middle;">⊙</small><span
                            class="fluoro">FluoroCore</span></div>
                    <el-image :src="url" :preview-src-list="[url]" :initial-index="0" :zoom-rate="1.2" :max-scale="7"
                        :min-scale="0.2" fit="contain" />
                    <div class="logo-title-2">荧光判读<span class="ai-blue ai-large">AI</span><span
                        class="ai-blue">模型</span></div>
                </div>
                <div class="form-container">
                    <el-form ref="aiFormRef" label-width="60px" :inline="true" :model="aiForm" :rules="rules">
                        <el-row>
                            <el-col :span="12">
                                <el-popover effect="dark" trigger="hover" v-if="aiForm.predictType"
                                    :content="aiForm.predictType + '-' + aiForm.type">
                                    <template #reference>
                                        <el-form-item label="定性" prop="predictType">
                                            <el-input class="negative" v-if="aiForm.predictType === 'negative'"
                                                value="阴性" disabled></el-input>
                                            <el-input class="positive" v-else-if="aiForm.predictType === 'positive'"
                                                value="阳性" disabled></el-input>
                                            <el-input v-else v-model="aiForm.predictType" disabled />
                                        </el-form-item>
                                    </template>
                                </el-popover>
                            </el-col>
                            <el-col :span="12">

                            </el-col>
                            <template v-for="(items, index) in aiForm.aiPredictKaryotypeDetailsVOS">
                                <el-col :span="12">
                                    <el-popover width="200" effect="dark" trigger="hover"
                                        :content="items.predictKaryotype && items.karyotype ? `${items.predictKaryotype}-${items.karyotype}` : '暂无数据'">
                                        <template #reference>
                                            <el-form-item :label="`核型 ${index + 1}`" prop="predictKaryotype">
                                                <el-input :value="items.predictKaryotype || '暂无数据'" disabled
                                                    class="ellipsis-input ios-input" readonly />
                                            </el-form-item>
                                        </template>
                                    </el-popover>
                                </el-col>
                                <el-col :span="12">
                                    <el-popover width="200" effect="dark" trigger="hover"
                                        :content="items.predictTiters && items.titers ? `${items.predictTiters}-${items.titers}` : '暂无数据'">
                                        <template #reference>
                                            <el-form-item :label="`滴度 ${index + 1}`" prop="predictTiters">
                                                <el-input :value="items.predictTiters || '暂无数据'" disabled
                                                    class="ellipsis-input ios-input" readonly />
                                            </el-form-item>
                                        </template>
                                    </el-popover>
                                </el-col>
                            </template>
                        </el-row>
                    </el-form>
                    <div class="logo-title-3">智能判读新时代</div>
                </div>
            </div>
            <!-- 底部输入区容器，包含输入框和表情弹窗 -->
            <div class="bottom-reply-bar-wrapper" :class="{ 'keyboard-wrapper': isKeyboardVisible }">

                <div class="bottom-reply-bar" v-show="!collapsedReplyBar" :class="{ 'keyboard-mode': isKeyboardVisible }">
                    <!-- 收缩/展开图标按钮 -->
                    <div class="collapse-icon-btn" @click="toggleReplyBarCollapse" :aria-label="collapsedReplyBar ? '展开评论输入区' : '收起评论输入区'">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" class="collapse-arrow" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 14l6 6 6-6" stroke="#21c16c" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div v-clickoutside="hideReplyBtn" class="my-reply">
                        <div class="avatar-name-row">
                            <el-avatar class="header-img" :size="40" :src="myHeader"></el-avatar>
                            <div v-if="wxUserInfo" class="user-name">{{ formData.surname }}</div>
                            <el-input v-else v-model="formData.surname" class="surname-input" placeholder="姓氏"
                                maxlength="1" :rules="formRules.surname" @input="handleSurnameInput"></el-input>
                            <el-button class="register-btn" size="medium" @click="goToRegister"
                                type="primary">立即登录</el-button>
                        </div>
                        <!-- 回复目标提示 -->
                        <!-- 移除回复目标栏，回复提示直接显示在输入框中 -->
                        <div class="reply-info">
                            <div class="comment-box" :class="{ 'self-reply': isSelfReply }">
                                <div class="comment-input" contenteditable="true" 
                                    :placeholder="isSelfReply ? '不能回复自己的评论' : (replyTarget ? '写下你的回复...' : '评论')"
                                    @input="onDivInput($event)" @keydown="onKeyDown($event)" id="replyInput" @focus="onReplyFocus"></div>
                                <el-tooltip content="表情" placement="top">
                                    <button class="emoji-btn" @click="toggleEmoji" type="button">
                                        <svg t="1747901154328" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                            xmlns="http://www.w3.org/2000/svg" p-id="3698" width="22" height="22">
                                            <path
                                                d="M512 992C247.3 992 32 776.7 32 512S247.3 32 512 32s480 215.3 480 480-215.3 480-480 480z m0-896C282.6 96 96 282.6 96 512s186.6 416 416 416 416-186.6 416-416S741.4 96 512 96z"
                                                fill="#243154" p-id="3699"></path>
                                            <path
                                                d="M512 800c-78 0-151.1-30.7-205.7-86.5C253.2 659.4 224 587.8 224 512c0-17.7 14.3-32 32-32h512c17.7 0 32 14.3 32 32 0 75.8-29.2 147.4-82.3 201.5C663.1 769.3 590 800 512 800zM352 668.8c42.5 43.4 99.3 67.2 160 67.2s117.5-23.9 160-67.2c33.7-34.4 55-77.9 61.7-124.8H290.3c6.6 46.9 28 90.3 61.7 124.8zM368 416c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zM656 416c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z"
                                                fill="#243154" p-id="3700"></path>
                                        </svg>
                                    </button>
                                </el-tooltip>
                                <button v-if="replyTarget" class="cancel-btn" @click="cancelReply" type="button">取消</button>
                                <button class="send-btn" :disabled="!replyComment || !formData.surname || isSelfReply" @click="debouncedSendComment"
                                    type="button">{{ replyTarget ? '回复' : '发送' }}</button>
                            </div>
                        </div>
                    </div>
                    <div v-if="showEmoji" class="custom-emoji-popover" :class="{ 'keyboard-mode': isKeyboardVisible }" @click.stop>
                        <div class="emoji-section">
                            <div class="emoji-label">最近使用</div>
                            <div class="emoji-container">
                                <span v-if="recentEmojis.length === 0" class="emoji-empty">暂无最近使用</span>
                                <img v-for="(emoji, index) in recentEmojis" :key="'recent-' + index"
                                    :src="emoji.url" :alt="emoji.name" class="emoji-img"
                                    @click.stop.prevent="insertWechatEmoji(emoji.url, emoji.name)" />
                            </div>
                            <div class="emoji-label">所有表情</div>
                            <div class="emoji-container">
                                <img v-for="(emoji, index) in emojiList" :key="'all-' + index" :src="emoji.url"
                                    :alt="emoji.name" class="emoji-img"
                                    @click.stop.prevent="insertWechatEmoji(emoji.url, emoji.name)" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 评论列表外层容器，防止被底部输入区遮挡 -->
            <div class="comment-list-wrapper" ref="commentListWrapper">

                <!-- 微信朋友圈风格评论组件 -->
                <CommentItem 
                    v-for="(item, i) in comments" 
                    :key="item.shareId || i"
                    :comment="item" 
                    :depth="0" 
                    @send-reply="sendReply"
                    :extractColorByName="extractColorByName"
                    :currentUserName="currentUserName"
                />
            </div>
            
            <!-- 收缩时底部悬浮展开按钮 -->
            <div v-if="collapsedReplyBar" class="floating-comment-btn" @click="toggleReplyBarCollapse">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" class="collapse-arrow" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 18l6-6 6 6" stroke="#21c16c" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            </div>
        </template>
    </div>
</template>
<script>
//api
import { query, visitors, visitorsFind, add } from "@/api/request/admin/share";
import { poster } from "@/api/request/admin/tagging";
import { getWxConfig, getWxAuth } from "@/api/request/admin/wx";
import wx from 'weixin-js-sdk';
// 导入评论组件
import CommentItem from '@/components/CommentItem/index.vue';
// 导入敏感词检测工具
import SensitiveWordDetector from '@/utils/sensitiveWords.ts';

const baseURL = import.meta.env.VITE_PC_BASE_PATH;
const clickoutside = {
    // 初始化指令
    bind(el, binding, vnode) {
        function documentHandler(e) {
            // 这里判断点击的元素是否是本身，是本身，则返回
            if (el.contains(e.target)) {
                return false;
            }
            // 判断指令中是否绑定了函数
            if (binding.expression) {
                // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
                binding.value(e);
            }
        }
        // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
        el.vueClickOutside = documentHandler;
        document.addEventListener('click', documentHandler);
    },
    update() { },
    unbind(el, binding) {
        // 解除事件监听
        document.removeEventListener('click', el.vueClickOutside);
        delete el.vueClickOutside;
    },
};

export default {
    name: 'ArticleComment',
    data() {
        return {
            //文件ID
            fileId: this.$route.query.fileId,
            /**
             * 评论输入区是否收起
             * @type {boolean}
             */
            collapsedReplyBar: false,
            /**
             * 是否显示微信右上角分享提示
             * 调试用，默认true，任何浏览器都能看到
             * @type {boolean}
             */
            showShareTips: true,
            // 今日访问人数
            toDayShareNum: 0,
            // 总访问人数
            visitorCount: 0,
            aiForm: {
                predictType: "",
                type: "",
                aiPredictKaryotypeDetailsVOS: []
            },
            formData: {
                surname: ''
            },
            formRules: {
                surname: [
                    { required: true, message: '请输入姓氏', trigger: 'blur' },
                    { pattern: /^[\u4e00-\u9fa5a-zA-Z]{1}$/, message: '请输入一个汉字或英文字符', trigger: 'blur' }
                ]
            },
            url: '',
            srcList: [],
            btnShow: false,
            index: '0',
            replyComment: '',
            myName: '我是这个图片',
            openid: '',
            sex: '',
            myHeader: '',
            toName: '',
            toId: 0,
            comments: [],
            // 添加HTTP警告显示状态
            showHttpWarning: window.location.protocol === 'http:' && /MicroMessenger/i.test(navigator.userAgent),
            // 添加微信环境检测状态
            isWeixinBrowser: /MicroMessenger/i.test(navigator.userAgent),
            // 添加微信用户信息
            wxUserInfo: null,
            emojiTab: 'recent', // 当前表情tab: recent/all
            recentEmojis: JSON.parse(localStorage.getItem('recentEmojis') || '[]'), // 最近使用
            emojiList: Array.from({ length: 105 }, (_, i) => ({
                name: `微信表情${i}`,
                url: `https://res.wx.qq.com/mpres/htmledition/images/icon/emotion/${i}.gif`
            })),
            showEmoji: false,
            // 简化的加载状态（移除分页，后端返回完整树形结构）
            pagination: {
                total: 0,
                loading: false,
                finished: false
            },
            // 添加防抖延迟时间
            debounceDelay: 1000,
            // 添加防抖后的发送函数
            debouncedSendComment: null,
            /**
             * 当前回复的目标评论信息
             * @type {Object|null}
             */
            replyTarget: null,
            
            /**
             * 是否正在尝试回复自己的评论
             * @type {boolean}
             */
            isSelfReply: false,
            
            /**
             * 敏感词检查定时器
             * @type {number|null}
             */
            sensitiveCheckTimer: null,
            
            /**
             * 敏感词检测器实例
             * @type {SensitiveWordDetector}
             */
            sensitiveDetector: new SensitiveWordDetector(),

            /**
             * 输入法是否弹出
             * @type {boolean}
             */
            isKeyboardVisible: false,

            /**
             * 原始视口高度
             * @type {number}
             */
            originalViewportHeight: 0,

        }
    },
    directives: { clickoutside },
    components: {
        CommentItem
    },
    computed: {
        /**
         * 获取当前用户名
         * @returns {string} 当前用户名
         */
        currentUserName() {
            if (!this.formData.surname) {
                return ''; // 如果姓氏为空，返回空字符串
            }
            return this.wxUserInfo ? this.formData.surname : this.formData.surname + '老师';
        }
    },
    async mounted() {
        // 检查是否是微信浏览器
        if (!this.isWeixinBrowser) {
            // 非微信浏览器也需要初始化基本功能（用于调试）
            if (this.fileId) {
                this.initImgeInfo();
                this.initShare();
                this.initName();
            }
            return; // 显示提示，但也加载基本内容
        }

        // 检查是否是页面内跳转（通过 URL 中的 returnUrl 参数判断）
        const isInternalRedirect = window.location.href.includes('code=');

        // 只在非页面内跳转时记录访问次数
        if (!isInternalRedirect && this.fileId) {
            await this.getVisitorCount();
        } else {
            await this.getVisitorFind();
        }

        // 优先从本地读取微信用户信息
        // const wxUserInfoStr = localStorage.getItem('wxUserInfo');
        // if (wxUserInfoStr) {
        //     const wxUserInfo = JSON.parse(wxUserInfoStr);
        //     this.wxUserInfo = wxUserInfo;
        //     if (wxUserInfo.nickname) {
        //         this.formData.surname = wxUserInfo.nickname;
        //     }
        //     if (wxUserInfo.headimgurl) {
        //         this.myHeader = wxUserInfo.headimgurl;
        //     }
        // } else {
        //     // 本地没有才走授权
        //     await this.handleWxAuth();
        // }
        // // 检查是否是登录后返回
        // const returnUrl = localStorage.getItem('returnUrl');
        // if (returnUrl && returnUrl === window.location.href) {
        //     localStorage.removeItem('returnUrl');
        //     // 重新初始化微信配置
        //     this.initWxConfig();
        // }

        if (!this.fileId) {
            this.$message.error('缺少必要的文件ID参数');
            return;
        }
        this.initImgeInfo();
        this.initShare();
        this.initName();

        // 8秒后隐藏分享提示
        setTimeout(() => {
            this.showShareTips = false;
        }, 8000);

        document.addEventListener('click', this.handleGlobalClick, true);
        // 添加页面内容点击监听，用于自动展开评论框
        document.addEventListener('click', this.handleContentClick, false);
        // 初始化输入法适配
        this.initKeyboardAdaptation();
        // 移除滚动监听（不再需要分页加载）
        // window.addEventListener('scroll', this.handleScroll);
        


    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleGlobalClick, true);
        // 清理敏感词检查定时器
        if (this.sensitiveCheckTimer) {
            clearTimeout(this.sensitiveCheckTimer);
        }

        // 清理输入法适配监听器
        if (window.visualViewport) {
            window.visualViewport.removeEventListener('resize', this.handleVisualViewportResize);
        } else {
            window.removeEventListener('resize', this.handleWindowResize);
        }
        document.removeEventListener('focusin', this.handleInputFocus);
        document.removeEventListener('focusout', this.handleInputBlur);
        document.removeEventListener('click', this.handleContentClick, false);

        // 已移除滚动监听（不再需要分页加载）
        // window.removeEventListener('scroll', this.handleScroll);
    },
    created() {
        // 初始化防抖后的发送函数
        this.debouncedSendComment = this.debounce(this.sendComment, this.debounceDelay);
    },
    methods: {
        // 跳转到注册页面
        goToRegister() {
            // 保存当前URL，用于登录后返回
            const currentUrl = window.location.href;
            localStorage.setItem('returnUrl', currentUrl);
            window.location.href = baseURL;
        },
        //初始化匿名
        initName() {
            this.myName = '老师';
        },
        //初始化内容：图片信息、评论信息
        async initImgeInfo() {
            try {
                await poster(this.fileId).then(async res => {
                    this.srcList = [];
                    if (res.code === 200) {
                        let data = res.data;
                        this.url = data.filePath;
                        this.srcList.push(data.filePath);
                        this.aiForm.predictType = data.predictType;
                        this.aiForm.type = data.type;
                        this.aiForm.aiPredictKaryotypeDetailsVOS = data.aiPredictKaryotypeDetailsVOS;

                        // 设置分享信息
                        const shareTitle = '每日一学，智判日新';
                        const shareDesc = 'AI智能判读ANA结果：快来看看，一起讨论分享。';
                        document.title = shareTitle;

                        // 确保图片URL是完整的绝对路径
                        const imgUrl = this.url.startsWith('http') ? this.url : window.location.origin + this.url;

                        // 配置微信分享 - 只在非授权状态下配置
                        if (!window.location.href.includes('code=')) {
                            try {
                                const wxRes = await getWxConfig(window.location.href.split('#')[0]);
                                if (wxRes.code === 200) {
                                    const config = wxRes.data;
                                    wx.config({
                                        debug: false,
                                        appId: config.appId,
                                        timestamp: config.timestamp,
                                        nonceStr: config.nonceStr,
                                        signature: config.signature,
                                        jsApiList: [
                                            'updateAppMessageShareData',
                                            'updateTimelineShareData',
                                            'onMenuShareTimeline',
                                            'onMenuShareAppMessage'
                                        ]
                                    });
                                    wx.ready(() => {
                                        // 分享给朋友
                                        wx.updateAppMessageShareData({
                                            title: shareTitle,
                                            desc: shareDesc,
                                            link: window.location.href,
                                            imgUrl: imgUrl,
                                            success: function () {
                                                console.log('分享设置成功');
                                            }
                                        });

                                        // 分享到朋友圈
                                        wx.updateTimelineShareData({
                                            title: `${shareTitle} - ${shareDesc}`,
                                            link: window.location.href,
                                            imgUrl: imgUrl,
                                            success: function () {
                                                console.log('分享设置成功');
                                            }
                                        });

                                        // 兼容旧版本
                                        wx.onMenuShareTimeline({
                                            title: shareTitle,
                                            link: window.location.href,
                                            imgUrl: imgUrl,
                                            success: function () {
                                                console.log('分享设置成功');
                                            }
                                        });

                                        wx.onMenuShareAppMessage({
                                            title: shareTitle,
                                            desc: shareDesc,
                                            link: window.location.href,
                                            imgUrl: imgUrl,
                                            type: '',
                                            dataUrl: '',
                                            success: function () {
                                                console.log('分享设置成功');
                                            }
                                        });
                                    });

                                    wx.error((err) => {
                                        console.error('微信配置失败:', err);
                                    });
                                }
                            } catch (error) {
                                console.error('获取微信配置失败:', error);
                            }
                        }
                    } else {
                        this.$message.error('获取图片信息失败');
                    }
                }).catch(err => {
                    this.$message.error('获取图片信息失败');
                    console.error(err);
                });
            } catch (error) {
                this.$message.error('系统错误');
                console.error(error);
            }
        },
        initShare() {
            if (!this.fileId) return;
            this.loadComments();
        },
        /**
         * 加载评论列表
         */
        async loadComments() {
            if (this.pagination.loading) return;

            this.pagination.loading = true;
            console.log('开始加载评论，fileId:', this.fileId);
            
            try {
                // 从 localStorage 获取 openid
                const storedOpenid = localStorage.getItem('userOpenid');

                const param = {
                    fileId: this.fileId,
                    openid: storedOpenid || this.openid // 优先使用存储的 openid
                };

                console.log('请求参数:', param);
                const res = await query(param);
                console.log('评论接口返回:', res);
                
                if (res.code === 200) {
                    const { list, total } = res.data;
                    this.pagination.total = total;
                    // 后端返回的已经是完整的树形结构，直接设置
                    this.comments = list;
                    console.log('评论列表设置成功，数量:', list.length, '数据:', list);
                    this.pagination.finished = true; // 标记为已完成，不再加载更多

                } else {
                    this.$message.error('获取评论信息失败');
                    console.error('接口返回错误:', res);
                }
            } catch (err) {
                this.$message.error('获取评论信息失败');
                console.error('加载评论失败:', err);
            } finally {
                this.pagination.loading = false;
            }
        },
        /**
         * 处理滚动事件（已移除分页加载，保留以防其他功能需要）
         */
        handleScroll() {
            // 已移除滚动加载更多功能
            // 后端返回完整树形结构，不需要分页
        },
        onReplyFocus() {
            var replyInput = document.getElementById('replyInput');
            replyInput.style.padding = "8px 8px";
            replyInput.style.border = "2px solid #409EFF";
            
            // 主动触发输入法适配检查
            setTimeout(() => {
                this.checkKeyboardState();
                this.scrollToInput();
            }, 300);
        },
        hideReplyBtn() {
            var replyInput = document.getElementById('replyInput');
            replyInput.style.padding = "10px";
            replyInput.style.border = "1px solid #DCDFE6";
        },
        showReplyInput(i, name, id) {
            this.comments[this.index].inputShow = false
            this.index = i
            this.comments[i].inputShow = true
            this.toName = name
            this.toId = id
        },
        _inputShow(i) {
            return this.comments[i].inputShow
        },
        /**
         * 滚动评论区到顶部，确保最新评论（最上面）可见
         */
        scrollToBottom() {
            this.$nextTick(() => {
                const commentList = this.$refs.commentListWrapper;
                if (commentList) {
                    commentList.scrollTop = 0;
                }
            });
        },
        /**
         * 防抖函数
         * @param {Function} fn - 需要防抖的函数
         * @param {number} delay - 延迟时间(ms)
         * @returns {Function} - 防抖后的函数
         */
        debounce(fn, delay) {
            let timer = null;
            return function (...args) {
                if (timer) clearTimeout(timer);
                timer = setTimeout(() => {
                    fn.apply(this, args);
                }, delay);
            };
        },
        /**
         * 发表评论
         * @returns {void}
         */
        sendComment() {
            console.log('开始发送评论, replyComment:', this.replyComment);
            
            // 检查姓氏是否已填写
            if (!this.formData.surname) {
                this.$message({
                    showClose: true,
                    type: 'warning',
                    message: '请输入姓氏'
                });
                return;
            }

            if (!this.replyComment) {
                console.log('评论内容为空');
                this.$message({
                    showClose: true,
                    type: 'warning',
                    message: '评论不能为空'
                });
                return;
            }

            console.log('检查敏感词之前的内容:', this.replyComment);
            
            // 检查评论内容是否包含敏感词
            const sensitiveCheck = this.checkSensitiveContent(this.replyComment);
            console.log('敏感词检查结果:', sensitiveCheck);
            
            if (!sensitiveCheck.isValid) {
                console.log('敏感词检查失败:', sensitiveCheck.message);
                // 不显示上方的错误提示，只在下方显示温和提示
                this.showSensitiveWarning(sensitiveCheck.message);
                return;
            }

            // 隐藏下方的敏感词警告（如果有的话）
            this.hideSensitiveWarning();

            // 富文本下自动识别所有文本节点中的链接为a标签
            function linkifyElement(element) {
                const urlReg = /(https?:\/\/[^\s<>'"\)\(]+|www\.[^\s<>'"\)\(]+)/gi;
                for (let node of Array.from(element.childNodes)) {
                    if (node.nodeType === 3) { // 文本节点
                        let text = node.nodeValue;
                        let frag = document.createDocumentFragment();
                        let lastIdx = 0;
                        let match;
                        urlReg.lastIndex = 0;
                        while ((match = urlReg.exec(text)) !== null) {
                            if (match.index > lastIdx) {
                                frag.appendChild(document.createTextNode(text.slice(lastIdx, match.index)));
                            }
                            let url = match[0];
                            let link = url;
                            if (!/^https?:\/\//i.test(url)) link = 'http://' + url;
                            let a = document.createElement('a');
                            a.href = link;
                            a.target = '_blank';
                            a.rel = 'noopener noreferrer';
                            a.textContent = url;
                            frag.appendChild(a);
                            lastIdx = match.index + url.length;
                        }
                        if (lastIdx < text.length) {
                            frag.appendChild(document.createTextNode(text.slice(lastIdx)));
                        }
                        node.replaceWith(frag);
                    } else if (node.nodeType === 1) {
                        linkifyElement(node);
                    }
                }
            }
            // 解析输入内容
            let input = document.createElement('div');
            input.innerHTML = this.replyComment;
            linkifyElement(input);
            let commentContent = input.innerHTML;
            console.log('处理后的评论内容:', commentContent);
            
            // 处理 #话题# 或 ＃话题＃，直接跳转微信搜一搜网页版
            commentContent = commentContent.replace(/[#＃]\s*([^#＃]+?)\s*[#＃]/g, (match, topic) => {
                return `<a href="https://weixin.sogou.com/weixin?type=2&query=${encodeURIComponent(topic.trim())}" class="topic-link" target="_blank">#${topic.trim()}#</a>`;
            });
            let data = {};
            let replyInput = document.getElementById('replyInput');
            data.fileId = this.fileId;//文件ID
            data.name = this.currentUserName;//使用统一的当前用户名逻辑
            data.headImg = this.myHeader;//头像
            data.comment = commentContent;//评论内容
            data.time = this.formatDateTime(new Date());//评论时间
            data.commentNum = 0;//内容数量
            data.likeNum = 0;//点赞数
            data.toId = this.replyTarget ? this.replyTarget.id : 0;//父ID，如果有回复目标则设置父ID
            data.toName = this.replyTarget ? this.replyTarget.name : '';//回复目标用户名
            data.openid = this.openid;//微信openid
            data.sex = this.sex;//用户性别
            
            console.log('即将发送的数据:', data);
            
            //写入库
            add(data).then(res => {
                console.log('评论发送结果:', res);
                if (res.code == 200) {
                    const successMsg = this.replyTarget ? "回复成功" : "发表成功";
                    this.$message.success(successMsg);
                    data.shareId = res.data;
                    
                    // 如果是回复，需要重新加载评论列表以获取更新后的层级结构
                    if (this.replyTarget) {
                        this.loadComments();
                        // 重置回复目标
                        this.replyTarget = null;
                    } else {
                        // 如果是新评论，直接添加到列表顶部
                        // 确保新评论有完整的结构
                        data.child = []; // 初始化子评论数组
                        this.comments.unshift(data);
                    }
                    
                    // 清空评论内容
                    this.replyComment = '';
                    replyInput.innerHTML = '';
                    
                    // 发送后自动滚动到刚发表的评论
                    this.$nextTick(() => {
                        const commentId = 'comment-' + data.shareId;
                        const commentEl = document.getElementById(commentId);
                        if (commentEl) {
                            commentEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    });
                } else {
                    console.log('评论发送失败:', res.message || "发表失败");
                    this.$message.error(res.message || "发表失败");
                }
            }).catch(err => {
                console.error('评论发送出错:', err);
                this.$message.error("发表失败");
            });
        },
        randomName() {
            // const anonymousArray = ['瑾', 'A', '瀚', 'B', '煜', 'C', '琛', 'D', '辰', 'E', '逸', 'F', '睿', 'G', '宸', 'H', '轩'];
            // return anonymousArray[Math.floor(Math.random() * anonymousArray.length)];
            return '老师';
        },
        onDivInput: function (e) {
            // 如果当前是回复状态且还是占位符文本，在用户输入时清除占位符
            if (this.replyTarget && this.replyTarget.isPlaceholder) {
                const replyInput = e.target;
                const placeholderSpan = replyInput.querySelector('.reply-placeholder');
                if (placeholderSpan) {
                    // 清除占位符，但保留用户输入的内容
                    placeholderSpan.remove();
                    this.replyTarget.isPlaceholder = false;
                }
            }
            
            this.replyComment = e.target.innerHTML;
            
            // 实时检查敏感词（防抖处理，避免频繁检查）
            clearTimeout(this.sensitiveCheckTimer);
            this.sensitiveCheckTimer = setTimeout(() => {
                this.checkRealtimeSensitive();
            }, 500);
        },

        /**
         * 处理键盘按键事件，在用户开始输入时清除回复占位符
         * @param {KeyboardEvent} e - 键盘事件
         */
        onKeyDown: function (e) {
            // 如果当前是回复状态且还是占位符文本，在用户输入任何字符时清除占位符
            if (this.replyTarget && this.replyTarget.isPlaceholder) {
                // 排除功能键，只在输入内容字符时清除占位符
                const isContentKey = !['Shift', 'Ctrl', 'Alt', 'Meta', 'CapsLock', 'Tab', 'Escape', 'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'].includes(e.key) 
                    && !e.key.startsWith('Arrow');
                
                if (isContentKey) {
                    const replyInput = e.target;
                    const placeholderSpan = replyInput.querySelector('.reply-placeholder');
                    if (placeholderSpan) {
                        // 清除占位符
                        replyInput.innerHTML = '';
                        this.replyTarget.isPlaceholder = false;
                    }
                }
            }
        },
        //传入名字,根据名字生成颜色,这样颜色就固定下来了
        extractColorByName(name) {
            var temp = [];
            temp.push("#");
            for (let index = 0; index < name.length; index++) {
                temp.push(parseInt(name[index].charCodeAt(0), 10).toString(16));
            }
            return temp.slice(0, 5).join('').slice(0, 4);
        },

        formatDateTime(date) {
            try {
                if (!(date instanceof Date) || isNaN(date.getTime())) {
                    console.warn('无效的日期对象:', date);
                    return new Date().toISOString().split('.')[0].replace('T', ' ');
                }
                
                function padZero(num) {
                    return num < 10 ? '0' + num : num;
                }
                
                const year = date.getFullYear();
                const month = padZero(date.getMonth() + 1); // getMonth() 返回的月份是从 0 开始的
                const day = padZero(date.getDate());
                const hours = padZero(date.getHours());
                const minutes = padZero(date.getMinutes());
                const seconds = padZero(date.getSeconds());

                // 使用标准的日期时间格式：YYYY-MM-DD HH:MM:SS
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            } catch (error) {
                console.error('日期格式化错误:', error);
                // 返回当前时间作为备选
                return new Date().toISOString().split('.')[0].replace('T', ' ');
            }
        },

        dateStr(date) {
            //获取js 时间戳
            var time = new Date().getTime();
            //去掉 js 时间戳后三位，与php 时间戳保持一致
            time = parseInt((time - date) / 1000);
            //存储转换值 
            var s;
            if (time < 60 * 10) {//十分钟内
                return '刚刚';
            } else if ((time < 60 * 60) && (time >= 60 * 10)) {
                //超过十分钟少于1小时
                s = Math.floor(time / 60);
                return s + "分钟前";
            } else if ((time < 60 * 60 * 24) && (time >= 60 * 60)) {
                //超过1小时少于24小时
                s = Math.floor(time / 60 / 60);
                return s + "小时前";
            } else if ((time < 60 * 60 * 24 * 30) && (time >= 60 * 60 * 24)) {
                //超过1天少于30天内
                s = Math.floor(time / 60 / 60 / 24);
                return s + "天前";
            } else {
                //超过30天ddd
                var date = new Date(parseInt(date));
                return date.getFullYear() + "/" + (date.getMonth() + 1) + "/" + date.getDate();
            }
        },
        // 添加初始化微信配置的方法
        async initWxConfig() {
            try {
                const wxRes = await getWxConfig(window.location.href.split('#')[0]);
                if (wxRes.code === 200) {
                    const config = wxRes.data;
                    wx.config({
                        debug: false,
                        appId: config.appId,
                        timestamp: config.timestamp,
                        nonceStr: config.nonceStr,
                        signature: config.signature,
                        jsApiList: [
                            'updateAppMessageShareData',
                            'updateTimelineShareData',
                            'onMenuShareTimeline',
                            'onMenuShareAppMessage'
                        ]
                    });
                }
            } catch (error) {
                console.error('获取微信配置失败:', error);
            }
        },
        /**
         * 处理微信授权和用户信息获取
         * @returns {Promise<void>}
         */
        async handleWxAuth() {
            try {
                const url = window.location.href;
                console.log('开始微信授权流程，当前URL:', url);

                // 判断是否已有授权码
                if (url.indexOf('code=') === -1) {
                    console.log('未获取授权码，开始授权流程');
                    const response = await getWxAuth(url);
                    console.log('授权响应:', response);

                    if (!response || response.code !== 200) {
                        throw new Error(response?.msg || '微信授权响应无效');
                    }

                    if (response.data?.redirectUrl) {
                        console.log('重定向到:', response.data.redirectUrl);
                        // 保存当前URL用于返回
                        localStorage.setItem('returnUrl', url);
                        window.location.href = response.data.redirectUrl;
                        return;
                    }

                    if (response.data) {
                        console.log('获取到用户信息:', response.data);
                        this.wxUserInfo = response.data;
                        localStorage.setItem('wxUserInfo', JSON.stringify(this.wxUserInfo));
                        this.formData.surname = this.wxUserInfo.nickname;
                        if (this.wxUserInfo.headimgurl) {
                            this.myHeader = this.wxUserInfo.headimgurl;
                        }
                        // 设置openid和sex
                        this.openid = this.wxUserInfo.openid || '';
                        this.sex = this.wxUserInfo.sex || '';
                        // 将 openid 存储到 localStorage
                        localStorage.setItem('userOpenid', this.openid);
                        console.log('设置openid和sex:', { openid: this.openid, sex: this.sex });
                        return;
                    }

                    throw new Error('微信授权失败：响应数据格式不正确');
                }

                // 有code，获取用户信息
                console.log('已获取授权码，开始获取用户信息');
                const response = await getWxAuth(url);
                console.log('用户信息响应:', response);

                if (!response || response.code !== 200) {
                    throw new Error(response?.msg || '获取用户信息响应无效');
                }

                if (response.data) {
                    console.log('获取到用户信息:', response.data);
                    this.wxUserInfo = response.data;
                    localStorage.setItem('wxUserInfo', JSON.stringify(this.wxUserInfo));
                    this.formData.surname = this.wxUserInfo.nickname;
                    if (this.wxUserInfo.headimgurl) {
                        this.myHeader = this.wxUserInfo.headimgurl;
                    }
                    // 设置openid和sex
                    this.openid = this.wxUserInfo.openid || '';
                    this.sex = this.wxUserInfo.sex || '';
                    // 将 openid 存储到 localStorage
                    localStorage.setItem('userOpenid', this.openid);
                    console.log('设置openid和sex:', { openid: this.openid, sex: this.sex });
                    return;
                }

                throw new Error('获取用户信息失败：响应数据格式不正确');
            } catch (error) {
                console.error('微信授权失败:', error);
                if (error.response) {
                    console.error('错误响应:', error.response);
                }
                // 清空授权信息，防止拒绝授权后还能评论
                this.wxUserInfo = null;
                this.openid = '';
                localStorage.removeItem('wxUserInfo');
                localStorage.removeItem('userOpenid');
                this.$message.error(error.message || '微信授权失败，请重试');
            }
        },
        /**
         * 插入微信表情图片到输入框，并记录最近使用
         * @param {string} url - 表情图片地址
         * @param {string} name - 表情名称
         */
        insertWechatEmoji(url, name) {
            // 保存当前滚动位置，防止插入表情时页面跳动
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            const replyInput = document.getElementById('replyInput');
            
            // 如果当前是回复状态且还是占位符文本，先清除占位符
            if (this.replyTarget && this.replyTarget.isPlaceholder) {
                const placeholderSpan = replyInput.querySelector('.reply-placeholder');
                if (placeholderSpan) {
                    // 清除占位符
                    replyInput.innerHTML = '';
                    this.replyTarget.isPlaceholder = false;
                }
            }
            
            replyInput.focus();
            const selection = window.getSelection();
            let range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
            if (!range) {
                range = document.createRange();
                range.selectNodeContents(replyInput);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
            }
            // 插入img节点
            const img = document.createElement('img');
            img.src = url;
            img.alt = name;
            img.style.width = '24px';
            img.style.height = '24px';
            img.style.verticalAlign = 'middle';
            range.insertNode(img);
            // 移动光标到img后
            range.setStartAfter(img);
            range.setEndAfter(img);
            selection.removeAllRanges();
            selection.addRange(range);
            // 更新内容
            this.replyComment = replyInput.innerHTML;
            
            // 插入表情后的处理
            this.$nextTick(() => {
                // 恢复滚动位置，防止页面跳动
                window.scrollTo(0, currentScrollTop);
                
                // 插入表情后，让输入框失去焦点，防止输入法弹出
                // 但保持表情弹窗打开状态，用户可以继续选择
                if (replyInput) {
                    replyInput.blur();
                }
            });
            
            // 记录最近使用
            const emoji = { url, name };
            this.recentEmojis = [emoji, ...this.recentEmojis.filter(e => e.url !== url)].slice(0, 8);
            localStorage.setItem('recentEmojis', JSON.stringify(this.recentEmojis));
        },
        /**
         * 全局点击事件处理，点击空白处关闭表情包
         * @param {MouseEvent} e
         */
        handleGlobalClick(e) {
            // 获取表情按钮、弹窗、输入框节点
            const emojiBtn = document.querySelector('.emoji-btn');
            const replyInput = document.getElementById('replyInput');
            const emojiPopover = document.querySelector('.custom-emoji-popover');
            // 如果点击在表情按钮、输入框、弹窗内容内，则不关闭
            if (
                (emojiBtn && emojiBtn.contains(e.target)) ||
                (replyInput && replyInput.contains(e.target)) ||
                (emojiPopover && emojiPopover.contains(e.target))
            ) {
                return;
            }
            
            // 如果表情弹窗是打开状态，关闭时重新聚焦输入框
            if (this.showEmoji) {
                this.showEmoji = false;
                this.$nextTick(() => {
                    if (replyInput) {
                        replyInput.focus();
                    }
                });
            }
        },
        // 添加获取访问次数的方法
        async getVisitorCount() {
            try {
                let params = { fileId: this.fileId };
                const res = await visitors(params);
                if (res.code === 200) {
                    this.toDayShareNum = res.data.toDayShareNum || 0;
                    this.visitorCount = res.data.shareTotalNum || 0;
                }
            } catch (error) {
                this.toDayShareNum = 0;
                this.visitorCount = 0;
            }
        },
        //获取访问次数的方法
        async getVisitorFind() {
            try {
                const res = await visitorsFind(this.fileId);
                if (res.code === 200) {
                    this.toDayShareNum = res.data.toDayShareNum || 0;
                    this.visitorCount = res.data.shareTotalNum || 0;
                }
            } catch (error) {
                this.toDayShareNum = 0;
                this.visitorCount = 0;
            }
        },
        handleSurnameInput(value) {
            // 只允许汉字和英文，过滤其他字符，并只保留第一个字符
            let filteredValue = value.replace(/[^\u4e00-\u9fa5a-zA-Z]/g, '');
            if (filteredValue.length > 1) {
                filteredValue = filteredValue.charAt(0);
            }
            
            // 如果过滤后的值与原值不同，需要更新输入框
            if (filteredValue !== value) {
                this.$nextTick(() => {
                    this.formData.surname = filteredValue;
                });
            } else {
                this.formData.surname = filteredValue;
            }
            
            // 如果当前处于回复状态，需要重新检查是否回复自己的评论
            if (this.replyTarget && filteredValue) {
                this.$nextTick(() => {
                    const currentUserName = this.wxUserInfo ? filteredValue : filteredValue + '老师';
                    const commentName = this.replyTarget.name;
                    const currentSurname = filteredValue;
                    
                    // 比较完整用户名或基础姓氏
                    const isSameUser = commentName === currentUserName || 
                                      commentName === currentSurname || 
                                      commentName === currentSurname + '老师' ||
                                      (currentUserName && commentName.includes(currentSurname)) ||
                                      (currentSurname && commentName.startsWith(currentSurname));
                    
                    if (isSameUser) {
                        // 设置自回复状态，禁用发送按钮
                        this.isSelfReply = true;
                        // 清空输入框内容，显示提示
                        const replyInput = document.getElementById('replyInput');
                        if (replyInput) {
                            replyInput.innerHTML = '';
                        }
                        this.replyComment = '';
                        return;
                    } else {
                        // 不是自回复，重置状态
                        this.isSelfReply = false;
                    }
                });
            }
            
            // 强制更新组件，确保按钮状态正确
            this.$forceUpdate();
        },
        /**
         * 开始回复指定评论
         * @param {Object} comment - 要回复的评论对象
         */
        startReply(comment) {
            // 如果评论输入区是收缩状态，先展开它
            if (this.collapsedReplyBar) {
                this.toggleReplyBarCollapse();
            }
            
            // 检查是否回复自己的评论 - 支持多种用户名格式的比较
            const currentUserName = this.currentUserName;
            const commentName = comment.name;
            const currentSurname = this.formData.surname;
            
            // 比较完整用户名或基础姓氏
            const isSameUser = commentName === currentUserName || 
                              commentName === currentSurname || 
                              commentName === currentSurname + '老师' ||
                              (currentUserName && commentName.includes(currentSurname)) ||
                              (currentSurname && commentName.startsWith(currentSurname));
            
            if (isSameUser) {
                // 设置自回复状态，不弹窗，在输入框显示提示
                this.isSelfReply = true;
                this.replyTarget = {
                    id: comment.shareId,
                    name: comment.name,
                    isPlaceholder: false
                };
                
                // 在输入框中显示自回复提示
                this.$nextTick(() => {
                    const replyInput = document.getElementById('replyInput');
                    if (replyInput) {
                        replyInput.innerHTML = '';
                        replyInput.focus();
                        this.scrollToInput();
                    }
                });
                return;
            }

            // 重置自回复状态
            this.isSelfReply = false;
            
            this.replyTarget = {
                id: comment.shareId,
                name: comment.name,
                isPlaceholder: true // 标记这是提示文本
            };

            // 直接在输入框中显示回复提示
            this.$nextTick(() => {
                const replyInput = document.getElementById('replyInput');
                if (replyInput) {
                    // 在输入框中显示回复提示，添加特殊样式
                    replyInput.innerHTML = `<span class="reply-placeholder" style="color: #999;">回复 ${comment.name}</span>`;
                    replyInput.focus();
                    
                    // 将光标移到末尾
                    const range = document.createRange();
                    const sel = window.getSelection();
                    range.selectNodeContents(replyInput);
                    range.collapse(false);
                    sel.removeAllRanges();
                    sel.addRange(range);
                    
                    // 滚动到输入框
                    this.scrollToInput();
                }
            });
        },

        /**
         * 取消回复
         */
        cancelReply() {
            this.replyTarget = null;
            this.isSelfReply = false; // 重置自回复状态
            // 清空输入内容
            this.replyComment = '';
            const replyInput = document.getElementById('replyInput');
            if (replyInput) {
                replyInput.innerHTML = '';
            }
        },

        /**
         * 处理来自CommentItem的回复请求
         * @param {Object} replyData - 回复数据 { content, toId, toName }
         */
        sendReply(replyData) {
            // 如果评论输入区是收缩状态，先展开它
            if (this.collapsedReplyBar) {
                this.toggleReplyBarCollapse();
            }
            
            // 调用统一的回复方法
            this.startReply({
                shareId: replyData.toId,
                name: replyData.toName
            });
        },

        /**
         * 实时检查敏感词（温和提示，不阻止输入）
         */
        checkRealtimeSensitive() {
            if (!this.replyComment) return;
            
            const result = this.checkSensitiveContent(this.replyComment);
            if (!result.isValid) {
                // 在输入框下方显示温和提示
                this.showSensitiveWarning(result.message);
            } else {
                this.hideSensitiveWarning();
            }
        },

        /**
         * 显示敏感词警告
         * @param {string} message - 警告信息
         */
        showSensitiveWarning(message) {
            const commentBox = document.querySelector('.comment-box');
            let warningDiv = document.querySelector('.sensitive-warning');
            
            if (!warningDiv) {
                warningDiv = document.createElement('div');
                warningDiv.className = 'sensitive-warning';
                commentBox.parentNode.appendChild(warningDiv);
            }
            
            warningDiv.textContent = message;
            warningDiv.style.display = 'block';
        },

        /**
         * 隐藏敏感词警告
         */
        hideSensitiveWarning() {
            const warningDiv = document.querySelector('.sensitive-warning');
            if (warningDiv) {
                warningDiv.style.display = 'none';
            }
        },

        /**
         * 检查评论内容是否包含敏感词
         * @param {string} content - 要检查的内容
         * @returns {Object} - 检查结果 {isValid: boolean, message: string}
         */
        checkSensitiveContent(content) {
            return this.sensitiveDetector.detect(content);
        },
        
        /**
         * 切换评论输入区收起/展开
         * @returns {void}
         */
        toggleReplyBarCollapse() {
            this.collapsedReplyBar = !this.collapsedReplyBar;
        },

        /**
         * 切换表情弹窗显示状态
         * 在输入法状态下优化处理，避免页面跳动
         */
        toggleEmoji() {
            // 阻止默认行为和事件冒泡
            event.preventDefault();
            event.stopPropagation();
            
            // 保存当前滚动位置
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            const replyInput = document.getElementById('replyInput');
            
            // 切换表情显示状态
            this.showEmoji = !this.showEmoji;
            
            // 在下一个微任务中处理焦点和滚动
            this.$nextTick(() => {
                if (this.showEmoji) {
                    // 表情打开时，让输入框失去焦点，防止输入法弹出
                    if (replyInput) {
                        replyInput.blur();
                    }
                    // 确保滚动位置不变
                    window.scrollTo(0, currentScrollTop);
                } else {
                    // 表情关闭时，重新聚焦输入框
                    if (replyInput) {
                        replyInput.focus();
                    }
                }
            });
        },

        /**
         * 初始化输入法适配
         */
        initKeyboardAdaptation() {
            // 记录原始视口高度
            this.originalViewportHeight = window.innerHeight;

            // 使用 visualViewport API (现代浏览器)
            if (window.visualViewport) {
                window.visualViewport.addEventListener('resize', this.handleVisualViewportResize);
            } else {
                // 降级处理：监听窗口大小变化
                window.addEventListener('resize', this.handleWindowResize);
            }

            // 监听输入框聚焦和失焦事件
            document.addEventListener('focusin', this.handleInputFocus);
            document.addEventListener('focusout', this.handleInputBlur);
        },

        /**
         * 处理 visualViewport 变化
         */
        handleVisualViewportResize() {
            const viewport = window.visualViewport;
            const heightDiff = this.originalViewportHeight - viewport.height;
            
            // 如果高度减少超过150px，认为是输入法弹出
            if (heightDiff > 150) {
                this.showKeyboard();
            } else {
                this.hideKeyboard();
            }
        },

        /**
         * 处理窗口大小变化（降级方案）
         */
        handleWindowResize() {
            const currentHeight = window.innerHeight;
            const heightDiff = this.originalViewportHeight - currentHeight;
            
            // 如果高度减少超过150px，认为是输入法弹出
            if (heightDiff > 150) {
                this.showKeyboard();
            } else {
                this.hideKeyboard();
            }
        },

        /**
         * 处理输入框聚焦
         */
        handleInputFocus(event) {
            // 检查是否是评论输入框
            if (event.target.id === 'replyInput' || event.target.classList.contains('comment-input')) {
                // 延迟检测输入法弹出
                setTimeout(() => {
                    this.checkKeyboardState();
                    this.scrollToInput();
                }, 300);
            }
        },

        /**
         * 处理输入框失焦
         */
        handleInputBlur() {
            // 延迟检测输入法收起
            setTimeout(() => {
                this.checkKeyboardState();
            }, 300);
        },

        /**
         * 检查输入法状态
         */
        checkKeyboardState() {
            const viewport = window.visualViewport || window;
            const currentHeight = viewport.height || window.innerHeight;
            const heightDiff = this.originalViewportHeight - currentHeight;
            
            if (heightDiff > 150) {
                this.showKeyboard();
            } else {
                this.hideKeyboard();
            }
        },

        /**
         * 显示输入法时的处理
         */
        showKeyboard() {
            if (this.isKeyboardVisible) return;
            
            this.isKeyboardVisible = true;
            document.body.classList.add('keyboard-visible');
            
            // 调整底部输入区样式
            const bottomBar = document.querySelector('.bottom-reply-bar');
            if (bottomBar) {
                bottomBar.classList.add('keyboard-mode');
            }

            // 调整评论列表底部间距
            const commentList = document.querySelector('.comment-list-wrapper');
            if (commentList) {
                commentList.classList.add('keyboard-mode');
            }
        },

        /**
         * 隐藏输入法时的处理
         */
        hideKeyboard() {
            if (!this.isKeyboardVisible) return;
            
            this.isKeyboardVisible = false;
            document.body.classList.remove('keyboard-visible');
            
            // 恢复底部输入区样式
            const bottomBar = document.querySelector('.bottom-reply-bar');
            if (bottomBar) {
                bottomBar.classList.remove('keyboard-mode');
            }

            // 恢复评论列表底部间距
            const commentList = document.querySelector('.comment-list-wrapper');
            if (commentList) {
                commentList.classList.remove('keyboard-mode');
            }
        },

        /**
         * 滚动到输入框
         */
        scrollToInput() {
            this.$nextTick(() => {
                const replyInput = document.getElementById('replyInput');
                if (replyInput) {
                    // 平滑滚动到输入框
                    replyInput.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            });
        },

        /**
         * 处理页面内容点击，收缩状态下点击内容区域自动展开评论框
         * @param {MouseEvent} e - 点击事件
         */
        handleContentClick(e) {
            // 只在评论框收缩状态下处理
            if (!this.collapsedReplyBar) {
                return;
            }

            // 获取点击目标元素
            const target = e.target;
            
            // 排除不应该触发展开的区域
            const excludeSelectors = [
                '.floating-comment-btn',      // 悬浮展开按钮
                '.bottom-reply-bar',          // 底部评论区
                '.comment-list-wrapper',      // 评论列表区域
                '.visitor-count',             // 访问人数
                '.share-tips',                // 分享提示
                '.weixin-tip',                // 微信提示
                'button',                     // 所有按钮
                'a',                          // 所有链接
                '.el-button',                 // Element UI按钮
                '.register-btn'               // 注册按钮
            ];

            // 检查点击的元素是否在排除列表中
            const shouldExclude = excludeSelectors.some(selector => {
                return target.closest(selector) !== null;
            });

            if (shouldExclude) {
                return;
            }

            // 检查是否点击的是主要内容区域（图片、表单等）
            const contentSelectors = [
                '.demo-image__preview',       // 图片预览区域
                '.image-container',           // 图片容器
                '.form-container',            // 表单容器
                '.logo-title',                // 标题区域
                '.el-image',                  // 图片元素
                '.el-form'                    // 表单元素
            ];

            const isContentArea = contentSelectors.some(selector => {
                return target.closest(selector) !== null;
            });

            // 如果点击的是内容区域，自动展开评论框
            if (isContentArea) {
                this.toggleReplyBarCollapse();
                
                // 展开后自动聚焦到输入框（延迟执行，确保动画完成）
                setTimeout(() => {
                    const replyInput = document.getElementById('replyInput');
                    if (replyInput) {
                        replyInput.focus();
                        // 平滑滚动到输入框
                        replyInput.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                    }
                }, 100);
            }
        },

    },
}
</script>
<style lang="scss" scoped>
.demo-image__preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 0 auto;
    padding: 0;
    box-sizing: border-box;
}

.image-container {
    width: 100%;
    max-width: 390px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding: 0;
}

.logo-title,
.logo-title-2,
.logo-title-3 {
    width: 100%;
    margin: 2px auto 8px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(90deg, #0b2a3a 60%, #0b2a3a 100%);
    color: #aaff4d;
    font-size: 14px;
    font-weight: bold;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.10);
    padding: 6px 12px 6px 12px;
    letter-spacing: 2px;
    user-select: none;
    white-space: nowrap;
}


.form-container {
    width: 100%;
    max-width: 390px;

    :deep(.el-form-item) {
        margin-bottom: 10px;

        .el-form-item__label {
            padding-right: 8px;
            font-size: 14px;
        }

        .el-input {
            width: 100%;
        }
    }
}

.my-reply {
    padding: 12px;
    background-color: #fafbfc;
    border-radius: 0;

    .avatar-name-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .header-img {
            margin-right: 12px;
        }

        .user-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            flex: 1;
        }

        .surname-input {
            width: 80px;
            margin-right: 10px;

            :deep(.el-input__inner) {
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                color: #333;
            }
        }

        .register-btn {
            margin-left: auto;
            flex-shrink: 0;
        }
    }

    .reply-info {
        display: block;
        width: 100%;

        @media screen and (max-width: 1200px) {
            width: 100%;
        }

        .top-actions {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;

            .surname-form {
                flex-grow: 1;
                margin-bottom: 0;

                :deep(.el-form-item) {
                    margin-bottom: 0;
                }

                .surname-input {
                    max-width: 140px;

                    :deep(.el-input__inner) {
                        border-radius: 4px;
                        text-align: center;

                        &:focus {
                            border-color: #409EFF;
                        }
                    }
                }
            }
        }

        .input-wrapper {
            display: flex;
            align-items: center;
            gap: 6px;
            width: 100%;
        }

        .reply-input {
            flex: 1 1 0%;
            min-width: 0;
            max-width: 320px;
            width: 100%;
            min-height: 20px;
            line-height: 22px;
            padding: 10px;
            color: #606266;
            background-color: #fff;
            border-radius: 4px;
            border: 1px solid #DCDFE6;
            box-sizing: border-box;
            font-size: 16px;
        }
    }

    .action-buttons {
        height: 40px;
        margin: 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;

        .register-btn,
        .reply-btn {
            margin: 0;
            float: none;
            min-width: 100px;
        }
    }
}

.my-comment-reply {
    margin-left: 50px;

    .reply-input {
        width: flex;
    }
}

.author-title:not(:last-child) {
    border-bottom: 1px solid rgba(178, 186, 194, 0.3);
}

.author-title {
    padding: 10px;

    .header-img {
        display: inline-block;
        vertical-align: top;
    }

    .author-info {
        display: inline-block;
        margin-left: 5px;
        width: 60%;
        height: 40px;
        line-height: 20px;

        >span {
            display: block;
            cursor: pointer;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .author-name {
            color: #000;
            font-size: 18px;
            font-weight: bold;
        }

        .author-time {
            font-size: 14px;
        }
    }

    .icon-btn {
        width: 30%;
        padding: 0 !important;
        float: right;

        @media screen and (max-width: 1200px) {
            width: 20%;
            padding: 7px;
        }

        >span {
            cursor: pointer;
        }

        .iconfont {
            margin: 0 5px;
        }
    }

    .talk-box {
        margin: 0 50px;

        >p {
            margin: 0;
        }

        .reply {
            word-break: break-all;
            overflow-wrap: anywhere;
            white-space: pre-wrap;
            font-size: 16px;
            color: #000;

            a {
                color: #1677ff !important;
                font-weight: bold !important;
                text-decoration: underline !important;
                cursor: pointer !important;
                word-break: break-all !important;
                transition: color 0.2s, background 0.2s !important;
                border-radius: 3px !important;
                padding: 0 2px !important;
                background: rgba(22, 119, 255, 0.06) !important;
            }

            a:hover {
                color: #0056b3 !important;
                background: rgba(22, 119, 255, 0.15) !important;
                text-decoration: underline !important;
            }
        }
    }

    .reply-box {
        margin: 10px 0 0 50px;
        background-color: #efefef;
    }
}

.negative {
    ::v-deep .el-input__inner {
        color: #67c23a !important;
    }
}

.positive {
    ::v-deep .el-input__inner {
        color: #f56c6c !important;
    }
}

// 添加HTTP警告样式
.http-warning {
    margin: 10px;

    .el-alert {
        margin-bottom: 20px;
    }
}

/* 分享提示样式 */
.share-tips {
    position: fixed;
    top: 40px;
    right: 15px;
    z-index: 9999;
    cursor: pointer;
    animation: pulse 2s infinite;

    .share-icon {
        display: flex;
        align-items: center;
        gap: 2px;
        padding: 6px 12px;
        background: linear-gradient(45deg, #409EFF, #36cfc9);
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        .text {
            color: #fff;
            font-size: 14px;
            font-weight: bold;
        }

        .dots {
            color: #fff;
            font-size: 18px;
            font-weight: bold;
            margin: 0 2px;
            animation: bounce 1.5s infinite;
        }
    }

    .arrow-container {
        position: absolute;
        top: -40px;
        right: 8px;
        animation: arrowFloat 2s infinite;
    }

    .arrow-icon {
        width: 45px;
        height: 45px;
        filter: drop-shadow(0 0 6px rgba(255, 77, 79, 0.5));
    }

    @keyframes arrowFloat {
        0% {
            transform: translateY(0) scale(1);
            opacity: 1;
        }

        50% {
            transform: translateY(-10px) scale(1.1);
            opacity: 0.9;
        }

        100% {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
    }

    @keyframes bounce {
        0%,
        100% {
            transform: translateY(0);
        }

        50% {
            transform: translateY(-5px);
        }
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }

        100% {
            transform: scale(1);
        }
    }
}

/* 新增：底部输入区容器样式 */
.bottom-reply-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10010;
    background: #fafbfc;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
    width: 100vw;
    max-width: 100vw;
    padding-bottom: env(safe-area-inset-bottom, 0);
    /* 兼容iOS安全区 */
    transition: box-shadow 0.35s;
    overflow: visible;
    min-height: 44px;
}



.my-reply {
    padding-bottom: 0;
    /* 避免和表情弹窗重叠 */
}

/* 优化表情弹窗样式，跟随输入框下方弹出 */
.custom-emoji-popover {
    position: static !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    margin: 0 auto;
    z-index: 10011;
    width: 100vw;
    max-width: 420px;
    background: #fff;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.12);
    padding-bottom: env(safe-area-inset-bottom, 8px);
    transition: none;
}

@media (min-width: 500px) {
    .bottom-reply-bar {
        left: 50%;
        transform: translateX(-50%);
        width: 390px;
        max-width: 390px;
    }

    .custom-emoji-popover {
        width: 390px;
        max-width: 390px;
    }
}

.ellipsis-input {
    :deep(.el-input__inner) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-text-fill-color: #606266;
    }
}

.ios-input {
    :deep(.el-input__inner) {
        -webkit-text-fill-color: #606266;
    }
}

.input-wrapper {
    display: flex;
    align-items: center;
    gap: 6px;
    width: 100%;
}

.comment-box {
    background: #fff;
    border: 2px solid #21c16c;
    border-radius: 10px;
    padding: 12px 14px;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(33, 193, 108, 0.08);
    transition: all 0.3s ease;
}

.comment-box.self-reply {
    border-color: #f56c6c;
    background: #fef5f5;
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.08);
}

.comment-input {
    flex: 1;
    min-height: 36px;
    max-height: 120px;
    outline: none !important;
    border: none !important;
    background: transparent;
    font-size: 16px;
    color: #333;
    resize: none;
    padding: 0;
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
    line-height: 1.6;
    border-radius: 0;
    box-shadow: none !important;
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
}

.comment-input:focus,
.comment-input:focus-visible,
.comment-input:-webkit-focus-ring {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
}

.comment-input:empty:before {
    content: attr(placeholder);
    color: #bbb;
    pointer-events: none;
}

.self-reply .comment-input:empty:before {
    color: #f56c6c;
    font-weight: 500;
}

/* 敏感词警告样式 */
:deep(.sensitive-warning) {
    display: none;
    background: #fef5e7;
    border: 1px solid #f0c78a;
    border-radius: 6px;
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 14px;
    color: #e6a23c;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.emoji-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    padding: 0;
    margin: 0;
    flex-shrink: 0;
}

.emoji-btn:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    transform: scale(1.05);
}

.el-icon {
    font-size: 20px;
    color: #bfbfbf;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn {
    background: #21c16c;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 0 14px;
    font-size: 15px;
    height: 32px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(33, 193, 108, 0.2);
    min-width: 54px;
    margin: 0;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: #1da55b;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(33, 193, 108, 0.3);
}

.send-btn:disabled {
    background: #f2f3f5;
    color: #aaa;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.cancel-btn {
    background: #f5f5f5;
    color: #666;
    border: none;
    border-radius: 6px;
    padding: 0 14px;
    font-size: 15px;
    height: 32px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 54px;
    margin: 0;
    flex-shrink: 0;
}

.cancel-btn:hover {
    background: #e9e9e9;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.emoji-section {
    background: linear-gradient(180deg, #f9f9f9, #f5f5f5);
    padding: 12px 0;
    border-top: 1px solid #e0e0e0;
}

.emoji-label {
    font-size: 14px;
    color: #666;
    padding: 8px 16px 6px 16px;
    font-weight: 600;
    letter-spacing: 0.5px;
    background: rgba(255, 255, 255, 0.6);
    margin: 0 12px 4px 12px;
    border-radius: 4px;
    text-align: center;
}

.emoji-container {
    display: flex;
    flex-wrap: wrap;
    padding: 0 12px 6px 12px;
    min-height: 32px;
    align-items: center;
    gap: 8px;
}

.emoji-img {
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.25s ease;
    padding: 4px;
    background: #fafafa;
    border: 1px solid #e6e6e6;
    object-fit: contain;
    filter: brightness(1) contrast(1.1);
}

.emoji-img:hover {
    background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
    border-color: #b3d9ff;
    transform: scale(1.15);
    box-shadow: 0 4px 12px rgba(0, 136, 255, 0.15);
    filter: brightness(1.1) contrast(1.2);
}

.emoji-img:active {
    transform: scale(1.05);
    transition: all 0.1s ease;
}

.emoji-empty {
    color: #bbb;
    font-size: 14px;
    padding: 8px 0;
}

.reply a,
.topic-link {
    color: #1677ff !important;
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
    word-break: break-all;
    transition: color 0.2s, background 0.2s;
    border-radius: 3px;
    padding: 0 2px;
    background: rgba(22, 119, 255, 0.06);
}

.reply a:hover,
.topic-link:hover {
    color: #0056b3 !important;
    background: rgba(22, 119, 255, 0.15);
    text-decoration: underline;
}

:deep(.reply a) {
    color: #1677ff !important;
    font-weight: bold !important;
    text-decoration: underline !important;
    cursor: pointer !important;
    word-break: break-all !important;
    transition: color 0.2s, background 0.2s !important;
    border-radius: 3px !important;
    padding: 0 2px !important;
    background: rgba(22, 119, 255, 0.06) !important;
}

:deep(.reply a:hover) {
    color: #0056b3 !important;
    background: rgba(22, 119, 255, 0.15) !important;
    text-decoration: underline !important;
}

/* 回复目标提示栏样式 */
.reply-target-bar {
    background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
    border: 1px solid #bbf7d0;
    border-radius: 10px;
    padding: 10px 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: slideInDown 0.3s ease-out;
    box-shadow: 0 1px 3px rgba(34, 197, 94, 0.1);
}

.reply-target-content {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
}

.reply-icon {
    color: #22c55e;
    font-size: 16px;
}

.reply-prefix {
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
}

.reply-target-name {
    color: #16a34a;
    font-size: 14px;
    font-weight: bold;
    padding: 4px 10px;
    border-radius: 16px;
    background-color: rgba(34, 197, 94, 0.12);
    border: 1px solid rgba(34, 197, 94, 0.2);
    display: inline-block;
}

.cancel-reply-btn {
    background: rgba(107, 114, 128, 0.08);
    border: 1px solid rgba(107, 114, 128, 0.2);
    color: #6b7280;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.2s;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.cancel-reply-btn:hover {
    background: rgba(107, 114, 128, 0.15);
    border-color: rgba(107, 114, 128, 0.4);
    color: #4b5563;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.2);
}

.cancel-text {
    font-size: 13px;
    line-height: 1;
}

.cancel-reply-btn i {
    font-size: 12px;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 访问人数样式 */
.visitor-count {
    position: fixed;
    top: 3px;
    left: 8px;
    color: #909399;
    padding: 3px 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 1000;
    font-weight: bold;
    opacity: 1;
    background: none;
    box-shadow: none;
    border-radius: 0;
    transition: color 0.2s;

    &:hover {
        color: #000;
    }

    i {
        font-size: 17px;
        color: #222;
        font-weight: bold;
    }

    .count-wrapper {
        display: flex;
        flex-direction: column;
        line-height: 1.2;
    }

    span {
        font-weight: bold;
        letter-spacing: 0;
    }
}

/**
 * 评论列表外层，底部加内边距，防止被底部输入区遮挡
 * padding-bottom 适当加大，确保最后一条评论完整显示
 */
.comment-list-wrapper {
    padding: 16px;
    padding-bottom: 150px;
    min-height: 100px;
}

.no-comments {
    padding: 40px 20px;
    text-align: center;
    color: #999;
    font-size: 16px;
    
    p {
        margin: 0;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px dashed #ddd;
    }
}

// 新增/优化微信环境提示样式
.weixin-tip {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    z-index: 9999;
}

.weixin-tip-inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.10);
    padding: 48px 32px 32px 32px;
    min-width: 320px;
    min-height: 220px;
    border: 1.5px solid #e6e6e6;
}

.weixin-logo {
    width: 56px;
    height: 56px;
    margin-bottom: 18px;
}

.weixin-title {
    font-size: 22px;
    font-weight: bold;
    color: #21c16c;
    margin-bottom: 10px;
    letter-spacing: 2px;
}

.weixin-desc {
    font-size: 16px;
    color: #888;
    text-align: center;
    line-height: 1.6;
}

/**
 * @description 让首页图片在手机端自适应宽度，完整显示
 */
.full-page-image {
    width: 100vw;
    /* 图片宽度等于视口宽度 */
    max-width: 100vw;
    /* 最大宽度不超过视口 */
    height: auto;
    /* 高度自适应，保持比例 */
    display: block;
    /* 去除底部空隙 */
    margin: 0 auto;
    /* 居中显示 */
}

.logo-title {
    width: 100%;
    height: 80%;
    margin: 0 auto 2px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(90deg, #0b2a3a 60%, #0b2a3a 100%);
    color: #aaff4d;
    font-size: 20px;
    font-weight: bold;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.10);
    padding: 6px 12px 6px 12px;
    cursor: pointer;
    letter-spacing: 2px;
    user-select: none;
    transition: background 0.2s;
    white-space: nowrap;

    .fluoro {
        color: #b6eaff;
        font-style: italic;
        margin-left: 8px;
        font-size: 20px;
        font-family: 'Arial Rounded MT Bold', 'Arial', sans-serif;
        text-shadow: 0 0 8px #1e90ff44;
    }

    small {
        color: #fff;
        margin: 0 4px;
        font-weight: normal;
        opacity: 0.8;
        font-size: 16px !important;
    }
}

.logo-title-2 {
    width: 100%;
    height: 80%;
    margin: 2px auto 8px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(90deg, #3784bb 40%, #3784bb 100%);
    color: #b6eaff;
    font-size: 20px;
    font-family: 'Microsoft YaHei', 'Arial', sans-serif;
    font-weight: bold;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.10);
    padding: 6px 12px 6px 12px;
    letter-spacing: 2px;
    user-select: none;
    white-space: nowrap;
}

.logo-title-3 {
    width: 100%;
    height: 80%;
    margin: 0 auto 8px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(90deg, #6cbaff 20%, #6cbaff 100%);
    color: #fff;
    font-size: 20px;
    font-family: 'Microsoft YaHei', 'Arial', sans-serif;
    font-weight: normal;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.10);
    padding: 6px 12px 6px 12px;
    letter-spacing: 1px;
    user-select: none;
    text-shadow: none;
    white-space: nowrap;
}

.ai-blue {
    color: #aaff4d;
    font-weight: normal;
    text-shadow: none;
    margin-left: 2px;
    font-family: inherit;
}

.ai-large {
    font-weight: bold;
    letter-spacing: 1px;
}



/**
 * 评论输入区容器
 */
.bottom-reply-bar-wrapper {
    position: relative;
}

/**
 * 收缩/展开图标按钮样式
 */
.collapse-icon-btn {
    position: absolute;
    top: 0;
    right: 10px;
    z-index: 100;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transform: translateY(-70%);
    background: transparent;
    transition: transform 0.2s;
    
    &:hover {
        transform: translateY(-70%) scale(1.1);
    }
    
    .collapse-arrow {
        transition: transform 0.3s;
    }
}

/**
 * 收缩时悬浮的评论按钮
 */
.floating-comment-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background: transparent;
    cursor: pointer;
    transition: transform 0.2s;
    
    &:hover {
        transform: scale(1.1);
    }
    
    &:active {
        transform: scale(0.95);
    }
}

/* 输入法适配样式 */
.keyboard-visible {
    /* 输入法弹出时的全局样式调整 */
}

.bottom-reply-bar.keyboard-mode {
    /* 输入法弹出时，改为相对定位，跟随页面内容 */
    position: relative !important;
    bottom: auto !important;
    left: auto !important;
    right: auto !important;
    transform: none !important;
    width: 100% !important;
    max-width: 100% !important;
    z-index: 1;
    margin-top: 10px;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
}

@media (min-width: 500px) {
    .bottom-reply-bar.keyboard-mode {
        left: auto !important;
        transform: none !important;
        width: 100% !important;
        max-width: 100% !important;
    }
}

.comment-list-wrapper.keyboard-mode {
    /* 输入法弹出时，减少底部间距，为输入区让出空间 */
    padding-bottom: 20px !important;
}

/* 输入法弹出时，确保页面可以正常滚动到输入框 */
.keyboard-visible .comment-input:focus {
    /* 确保输入框在视口中央 */
    scroll-margin: 20px;
}

/* 优化输入框在输入法模式下的显示 */
.keyboard-mode .comment-box {
    margin-bottom: 10px;
}

/* 输入法弹出时隐藏收缩按钮，避免干扰 */
.keyboard-mode .collapse-icon-btn {
    display: none;
}

/* 输入法模式下的容器样式 */
.bottom-reply-bar-wrapper.keyboard-wrapper {
    position: relative;
    z-index: 1000;
    background: #fafbfc;
    border-radius: 12px 12px 0 0;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
    margin: 10px;
    margin-bottom: 0;
    overflow: hidden;
}

/* 输入法模式下的表情弹窗样式 */
.custom-emoji-popover.keyboard-mode {
    position: static !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    margin: 0 !important;
    z-index: 1 !important;
    width: 100% !important;
    max-width: 100% !important;
    background: #fff !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    border-top: 1px solid #e0e0e0;
    padding-bottom: 10px !important;
    transition: none !important;
    /* 防止页面跳动的关键样式 */
    transform: none !important;
    overflow: hidden;
    will-change: auto;
}

/* 优化输入法模式下的评论列表容器 */
.comment-list-wrapper.keyboard-mode {
    margin-bottom: 0;
}

/* 输入法模式下确保输入框可见性 */
.keyboard-visible body {
    position: relative;
    overflow-x: hidden;
}

/* 防止输入法模式下页面出现横向滚动条 */
.keyboard-wrapper {
    width: 100vw;
    max-width: 100vw;
    left: 0;
    right: 0;
}

@media (min-width: 500px) {
    .keyboard-wrapper {
        width: 100%;
        max-width: 100%;
        left: auto;
        right: auto;
        margin: 10px auto 0 auto;
        max-width: 390px;
    }
}

/* 评论框收缩时，为内容区域添加点击提示 */
.collapsed-content-hint .demo-image__preview,
.collapsed-content-hint .image-container,
.collapsed-content-hint .form-container {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.collapsed-content-hint .demo-image__preview:hover,
.collapsed-content-hint .image-container:hover,
.collapsed-content-hint .form-container:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 20px rgba(33, 193, 108, 0.15);
}

/* 添加点击提示文本 */
.collapsed-content-hint .demo-image__preview::after {
    content: "💬 点击查看评论";
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(33, 193, 108, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    white-space: nowrap;
    z-index: 10;
}

.collapsed-content-hint .demo-image__preview:hover::after {
    opacity: 1;
}

</style>
