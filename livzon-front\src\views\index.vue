<template>
  <div class="page-container">
    <div class="image-container">
      <img alt="logo" class="full-page-image" src="..\assets\images\home-page.jpg">
    </div>
    <div class="nav-button" @click="goToMain">
      <span class="nav-btn-text">启动荧枢⊙FluoroCore AI模型</span>
    </div>
    <div class="total-visit">
      访问总数：{{ totalNum }}
    </div>
    <div class="today-visit">
      <div class="visit-item">{{ toDayNum }}</div>
      <div class="visit-item">{{ toDayHomeNum }}</div>
      <div class="visit-item">{{ shareTotalNum }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { visitorsNum, findHomeNum } from '@/api/system/logininfor';
import { publicRole } from "@/api/request/admin/tagging";
export default {
  name: "index",
  data() {
    return {
      // 版本号
      version: "1.0.0",
      /**
       * 总访问人数
       * @type {number|string}
       */
      totalNum: 0 as number | string,
      /**
       * 今日访问人数
       * @type {number|string}
       */
      toDayNum: 0 as number | string,
      /**
       * 当前用户角色是否普通
       * @type {string}
       */
      isPublic: false,
      /**
       * 首页访问量
       * @type {number|string}
       */
      toDayHomeNum: 0 as number | string,
      /**
      * 分享页总访问次数
      * @type {number|string}
      */
      shareTotalNum: 0 as number | string,
    };
  },
  mounted() {
    this.getRole();
    this.getVisitCount();
  },
  methods: {
    goToMain() {
      window.location.href = 'admin/tagging';
    },
    /**
     * 获取当前用户角色
     * @description 从localStorage读取userInfo.roleName
     */
    getRole() {
      publicRole().then((res: any) => {
        if (res.code == 200 && res.data != null) {
          this.isPublic = res.data;
        }
      })
    },
    /**
     * 获取访问人数
     * @description 调用 visitorsNum 接口获取今日和总访问人数
     * @returns {Promise<void>}
     */
    async getVisitCount() {
      try {
        await visitorsNum().then((res: any) => {
          if (res.code == 200) {
            this.totalNum = res.data.totalNum ?? res.data.total ?? 0;
            this.toDayNum = res.data.toDayNum ?? res.data.today ?? 0;
          } else {
            this.totalNum = '获取失败';
            this.toDayNum = '获取失败';
          }
        })
        await findHomeNum().then((res: any) => {
          if (res.code == 200) {
            this.toDayHomeNum = res.data.toDayHomeNum ?? res.data.today ?? 0;
            this.shareTotalNum = res.data.shareTotalNum ?? res.data.total ?? 0;
          } else {
            this.toDayHomeNum = '获取失败';
            this.shareTotalNum = '获取失败';
          }
        })
      } catch (e) {
        this.totalNum = '获取失败';
        this.toDayNum = '获取失败';
        this.toDayHomeNum = '获取失败';
        this.shareTotalNum = '获取失败';
      }
    }
  },
};
</script>

<style scoped lang="scss">
.page-container {
  position: fixed;
  top: 50px;
  left: 0;
  width: 100%;
  height: calc(100vh - 50px);
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: #000;
}

.image-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.full-page-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  margin: 0;
  padding: 0;

  @media screen and (max-width: 768px) {
    object-position: center center;
  }

  @media screen and (orientation: portrait) and (max-width: 768px) {
    height: 100vh;
    width: 100%;
    object-fit: cover;
  }

  @media screen and (orientation: landscape) and (max-width: 768px) {
    width: 100%;
    height: 100vh;
    object-fit: cover;
  }
}

@supports (padding: max(0px)) {
  .page-container {
    padding-top: max(0px, env(safe-area-inset-top));
    padding-bottom: max(0px, env(safe-area-inset-bottom));
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
    height: calc(100vh - max(0px, env(safe-area-inset-top)) - max(0px, env(safe-area-inset-bottom)));
  }
}

.nav-button {
  position: fixed;
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #6fd6ff 0%, #7e9fff 100%) !important;
  backdrop-filter: blur(8px);
  border: 1.5px solid #fff !important;
  padding: 8px 16px;
  border-radius: 20px;
  color: #fff !important;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  box-shadow: 0 4px 20px rgba(111, 214, 255, 0.15) !important;
  animation: float 3s ease-in-out infinite;
  min-width: 140px;

  .nav-btn-text {
    flex: 1;
    white-space: nowrap;
    font-size: 15px;
    font-weight: 500;
    letter-spacing: 1px;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.10);
  }

  &:active {
    transform: translateX(-50%) scale(0.98);
    background: linear-gradient(90deg, #43e8e1 0%, #7e9fff 100%) !important;
    color: #fff !important;
    animation: none;
  }

  @media screen and (max-width: 768px) {
    bottom: 20%;
    padding: 7px 10px;
    font-size: 14px;
    min-width: 100px;

    .nav-btn-text {
      font-size: 14px;
    }
  }
}

@keyframes float {
  0% {
    transform: translateX(-50%) translateY(0px);
  }

  50% {
    transform: translateX(-50%) translateY(-6px);
  }

  100% {
    transform: translateX(-50%) translateY(0px);
  }
}

@media (hover: hover) {
  .nav-button:hover {
    animation: none;
    background: linear-gradient(90deg, #43e8e1 0%, #7e9fff 100%) !important;
    color: #fff !important;
    transform: translateX(-50%) translateY(-3px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);

    .nav-btn-text {
      transform: scale(1.02);
    }
  }
}


.total-visit {
  position: fixed;
  bottom: calc(10% - 90px);
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  color: #fff;
  font-size: 14px;
  background: rgba(0, 0, 0, 0.18);
  padding: 6px 16px;
  border-radius: 12px;
  user-select: none;
  font-weight: 500;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  text-align: center;

  @media screen and (max-width: 768px) {
    bottom: calc(20% - 40px);
    font-size: 12px;
    padding: 4px 12px;
  }
}

.today-visit {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 10;
  color: rgba(255, 255, 255, 1);
  font-size: 12px;
  user-select: none;
  font-weight: 400;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  mix-blend-mode: soft-light;

  .visit-item {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &:hover {
    color: rgba(255, 255, 255, 0.5);
  }
}
</style>
