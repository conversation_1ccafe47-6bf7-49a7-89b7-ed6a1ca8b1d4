<template>
  <div>
    <!-- 极简悬浮按钮 -->
    <div class="notice-fab" @click="isNoticeOpen = true">
      <svg width="22" height="22" viewBox="0 0 22 22" fill="none">
        <circle cx="11" cy="11" r="11" fill="#222" fill-opacity="0.13"/>
        <path d="M11 17c.9 0 1.7-.8 1.7-1.7h-3.4c0 .9.8 1.7 1.7 1.7zm5.1-2.6v-4.2c0-2.6-1.4-4.8-4.1-5.4V4.7a.9.9 0 1 0-1.8 0v.1c-2.7.6-4.1 2.8-4.1 5.4v4.2l-.8.8v.8h13.6v-.8l-.8-.8zm-1.7.8H7.6v-4.2c0-2.1 1.3-3.8 3.4-3.8s3.4 1.7 3.4 3.8v4.2z" fill="#666"/>
      </svg>
    </div>
    <!-- 公告弹窗 -->
    <div v-if="isNoticeOpen" class="notice-popup-mask" @click.self="isNoticeOpen = false">
      <div class="notice-popup">
        <div class="notice-popup-header">
          <span>公告</span>
          <button class="close-btn" @click="isNoticeOpen = false">×</button>
        </div>
        <div class="notice-popup-list-roller"
          @mouseenter="onRollerMouseEnter"
          @mouseleave="onRollerMouseLeave"
          @wheel="onRollerWheel"
          ref="roller"
        >
          <div
            class="roller-inner"
            :style="rollerStyle"
            ref="rollerInner"
          >
            <div
              class="notice-popup-card"
              v-for="(item, idx) in rollerList"
              :key="item.noticeId + '-' + idx"
            >
              <div class="notice-title" :title="item.noticeTitle">{{ item.noticeTitle }}</div>
              <div
                class="notice-content"
                :class="{ 'hover-expanded': isHovering }"
                v-if="item.noticeContent"
                v-html="item.noticeContent"
              ></div>
              <div class="notice-meta">
                <span class="notice-time">{{ parseTime(item.createTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { listNotice } from "@/api/system/notice";

/**
 * 公告类型声明
 * @typedef {Object} Notice
 * @property {string} noticeId
 * @property {string} noticeTitle
 * @property {string} noticeContent
 * @property {string} createTime
 * @property {string} createBy
 */
type Notice = {
  noticeId: string;
  noticeTitle: string;
  noticeContent: string;
  createTime: string;
  createBy: string;
};

export default {
  name: "GlobalNotice",
  data() {
    return {
      /** @type {Notice[]} */
      noticeList: [] as Notice[],
      /**
       * 公告弹窗展开状态
       * @type {boolean}
       */
      isNoticeOpen: false,
      /**
       * 定时器ID
       * @type {number | undefined}
       */
      timer: undefined as number | undefined,
      /**
       * 当前滚动到第几条
       * @type {number}
       */
      currentIndex: 0,
      /**
       * 是否启用过渡动画
       * @type {boolean}
       */
      isTransitioning: true,
      /**
       * 每条公告的高度（px）
       * @type {number}
       */
      cardHeight: 80,
      /**
       * 滚动定时器ID
       * @type {number|undefined}
       */
      rollerTimer: undefined as number | undefined,
      isHovering: false, // 是否鼠标悬停
    };
  },
  computed: {
    /**
     * 用于无缝滚动的公告列表
     * @returns {Notice[]}
     */
    rollerList(): Notice[] {
      if ((this.noticeList as Notice[]).length > 3) {
        // 无限重复列表内容
        const list = this.noticeList as Notice[];
        return Array(10).fill(list).flat(); // 重复10次，确保有足够的内容滚动
      }
      return this.noticeList as Notice[];
    },
    /**
     * 滚动区域样式
     */
    rollerStyle() {
      return {
        transform: `translateY(-${this.currentIndex * this.cardHeight}px)`
      };
    },
  },
  mounted() {
    this.getList();
    // 检查是否是首次登录
    const hasShownNotice = localStorage.getItem('hasShownNotice');
    if (!hasShownNotice) {
      this.isNoticeOpen = true;
      localStorage.setItem('hasShownNotice', 'true');
    }
    // 每5分钟刷新一次公告列表
    this.timer = window.setInterval(() => {
      this.getList();
    }, 5 * 60 * 1000) as unknown as number;
    // 打开时自动滚动
    if (this.isNoticeOpen) this.startRoller();
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = undefined;
    }
    this.stopRoller();
  },
  methods: {
    /**
     * 查询公告列表
     * @returns {void}
     */
    getList() {
      let queryParams = {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        createBy: undefined,
        status: undefined,
      };
      listNotice(queryParams).then((res: any) => {
        if (res.code == 200 && Array.isArray(res.rows) && res.rows.length > 0) {
          this.noticeList = res.rows as Notice[];
        }
      }).catch((error: any) => {
        // 错误处理
      });
    },
    
    /**
     * 格式化时间为年月日格式
     * @param {string} time - 需要格式化的时间字符串
     * @returns {string} 格式化后的时间字符串 (YYYY-MM-DD)
     */
    parseTime(time: string): string {
      if (!time) {
        return '';
      }
      const date = new Date(time);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    /**
     * 启动公告自动滚动
     * @returns {void}
     */
    startRoller() {
      if (this.rollerTimer) clearInterval(this.rollerTimer);
      if (this.noticeList.length <= 3) return; // 不足三条不滚动
      
      this.rollerTimer = window.setInterval(() => {
        this.currentIndex++;
      }, 3000) as unknown as number;
    },
    /**
     * 停止公告自动滚动
     * @returns {void}
     */
    stopRoller() {
      if (this.rollerTimer) clearInterval(this.rollerTimer);
      this.rollerTimer = undefined;
    },
    /**
     * 鼠标移入时暂停滚动并展开内容
     */
    onRollerMouseEnter() {
      this.isHovering = true;
      this.stopRoller();
    },
    /**
     * 鼠标移出时恢复自动滚动
     */
    onRollerMouseLeave() {
      this.isHovering = false;
      this.startRoller();
    },
    /**
     * 鼠标滚轮事件，悬停时不做任何处理
     */
    onRollerWheel() {
      // 悬停时不做任何处理
    },
  },
  watch: {
    /**
     * 弹窗开关时启动/停止滚动
     */
    isNoticeOpen(val) {
      if (val) {
        this.startRoller();
      } else {
        this.stopRoller();
      }
    },
    /**
     * 公告列表变化时重置滚动
     */
    noticeList(newList) {
      this.currentIndex = 0;
      this.startRoller();
    },
  }
};
</script>

<style scoped lang="scss">
.notice-fab {
  position: fixed;
  bottom: 32px;
  right: 32px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(0,0,0,0.13);
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1002;
  transition: background 0.2s;
  &:hover {
    background: rgba(0,0,0,0.22);
  }
}

.notice-popup-mask {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.13);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.notice-popup {
  background: rgba(20, 24, 32, 0.98);
  border-radius: 12px 12px 0 0;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  width: 320px;
  max-width: 96vw;
  margin: 0 32px 48px 0;
  padding: 0 0 12px 0;
  animation: popupIn 0.18s cubic-bezier(0.4,0,0.2,1);
}

@keyframes popupIn {
  from { transform: translateY(40px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.notice-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 18px 6px 18px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.close-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  line-height: 1;
  padding: 0 4px;
}

.notice-popup-list-roller {
  height: 240px; /* 3条公告的高度，假设每条80px，可根据实际调整 */
  overflow: hidden; /* 改为 hidden */
  position: relative;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
}

.notice-popup-list-roller::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

.roller-inner {
  will-change: transform;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.notice-popup-card {
  background: rgba(255,255,255,0.04);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.notice-popup-card .notice-title {
  font-size: 15px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.notice-popup-card .notice-content {
  font-size: 13px;
  color: rgba(255,255,255,0.85);
  margin-bottom: 8px;
  line-height: 1.5;
  transition: all 0.3s ease-out;
  word-break: break-all;
  white-space: pre-wrap;

  /* 悬停时的展开样式 */
  &.hover-expanded {
    font-size: 14px;
    line-height: 1.6;
    color: rgba(255,255,255,0.95);
    background: rgba(255,255,255,0.05);
    padding: 8px;
    border-radius: 4px;
  }

  /* Quill编辑器内容样式 */
  :deep(p) {
    margin: 0;
    padding: 0;
  }
  
  :deep(u) {
    text-decoration: underline;
  }

  /* 标题样式 */
  :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
    margin: 8px 0;
    color: #fff;
    font-weight: 600;
  }

  :deep(h1) { font-size: 1.5em; }
  :deep(h2) { font-size: 1.3em; }
  :deep(h3) { font-size: 1.2em; }
  :deep(h4) { font-size: 1.1em; }
  :deep(h5) { font-size: 1em; }
  :deep(h6) { font-size: 0.9em; }

  /* 文本样式 */
  :deep(strong) {
    font-weight: 600;
    color: #fff;
  }

  :deep(em) {
    font-style: italic;
  }

  :deep(s) {
    text-decoration: line-through;
  }

  /* 引用和代码块 */
  :deep(blockquote) {
    margin: 8px 0;
    padding: 8px 12px;
    border-left: 3px solid #a0cfff;
    background: rgba(255,255,255,0.05);
    color: rgba(255,255,255,0.9);
  }

  :deep(pre.ql-syntax) {
    background: rgba(0,0,0,0.2);
    padding: 8px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 8px 0;
    font-family: monospace;
  }

  /* 列表样式 */
  :deep(ul), :deep(ol) {
    margin: 8px 0;
    padding-left: 20px;
  }

  :deep(li) {
    margin: 4px 0;
  }

  /* 缩进样式 */
  :deep(.ql-indent-1) { padding-left: 3em; }
  :deep(.ql-indent-2) { padding-left: 6em; }
  :deep(.ql-indent-3) { padding-left: 9em; }
  :deep(.ql-indent-4) { padding-left: 12em; }
  :deep(.ql-indent-5) { padding-left: 15em; }
  :deep(.ql-indent-6) { padding-left: 18em; }
  :deep(.ql-indent-7) { padding-left: 21em; }
  :deep(.ql-indent-8) { padding-left: 24em; }

  /* 字体大小 */
  :deep(.ql-size-small) { font-size: 0.75em; }
  :deep(.ql-size-large) { font-size: 1.5em; }
  :deep(.ql-size-huge) { font-size: 2.5em; }

  /* 对齐方式 */
  :deep(.ql-align-center) { text-align: center; }
  :deep(.ql-align-right) { text-align: right; }
  :deep(.ql-align-justify) { text-align: justify; }

  /* 链接样式 */
  :deep(a) {
    color: #a0cfff;
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }

  /* 图片样式 */
  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
  }

  /* 视频样式 */
  :deep(video) {
    max-width: 100%;
    border-radius: 4px;
    margin: 8px 0;
  }

  /* 表格样式 */
  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 8px 0;
  }

  :deep(th), :deep(td) {
    border: 1px solid rgba(255,255,255,0.1);
    padding: 6px;
    text-align: left;
  }

  :deep(th) {
    background: rgba(255,255,255,0.05);
    font-weight: 600;
  }

  /* 清除格式后的样式 */
  :deep(.ql-clean) {
    font-family: inherit;
    font-size: inherit;
    font-weight: normal;
    color: inherit;
    background: none;
    text-decoration: none;
  }
}

.notice-popup-card .notice-meta {
  font-size: 12px;
  color: #b7d6ff;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.notice-popup-card .notice-time {
  font-style: italic;
  color: #a0cfff;
  display: flex;
  align-items: center;
  &::before {
    content: '🕒';
    font-size: 11px;
    margin-right: 3px;
    opacity: 0.7;
  }
}

@media screen and (max-width: 768px) {
  .notice-fab {
    bottom: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
  }
  .notice-popup {
    width: 98vw;
    margin: 0 1vw 12px 0;
    border-radius: 12px 12px 0 0;
    max-height: 60vh;
    overflow-y: auto;
  }
  .notice-popup-header {
    font-size: 14px;
    padding: 8px 10px 4px 10px;
  }
  .notice-popup-list-roller {
    height: 180px; /* 3条公告的高度，假设每条80px，可根据实际调整 */
  }
}
</style> 