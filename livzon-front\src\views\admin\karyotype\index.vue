<template>
	<div class="app-container">
		<el-form ref="queryRef" :model="queryParams" :inline="true" v-show="showSearch">
			<el-form-item label="核型名称" prop="name">
				<el-input v-model="queryParams.name" placeholder="请输入核型名称" clearable @keyup.enter="handleQuery()"
					@clear="handleQuery()" />
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-select v-model="queryParams.status" placeholder="状态" clearable @change="handleQuery()">
					<el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label"
						:value="dict.value" />
				</el-select>
			</el-form-item>
			<el-form-item>
				<!-- prettier-ignore -->
				<el-button icon="Refresh" @click="resetQuery()">重置</el-button>
				<!-- prettier-ignore -->
				<el-button type="primary" icon="Search" @click="handleQuery()">搜索</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="primary" plain size="small" icon="Plus" @click="handleAdd"
					v-hasPermi="['livzon:karyotype:add']">新增</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button type="info" plain size="small" icon="Sort" @click="toggleExpandAll()">展开/折叠</el-button>
			</el-col>
			<el-col :span="1.5" v-if="!single && ids.length === 1">
				<el-button type="success" plain icon="edit" size="small" v-if="!single" :disabled="single"
					@click="handleUpdate" v-hasPermi="['livzon:karyotype:edit']">修改</el-button>
			</el-col>
			<el-col :span="1.5" v-if="!multiple && ids.length >= 1">
				<el-button type="danger" plain icon="delete" size="small" v-if="!multiple" :disabled="multiple"
					@click="batchDelete" v-hasPermi="['livzon:karyotype:remove']">删除</el-button>
			</el-col>
			<!-- prettier-ignore -->
			<right-toolbar v-model:showSearch="showSearch" @queryTable="handleQuery()" />
		</el-row>

		<el-table border stripe v-if="refreshTable" v-loading="loading" :data="karyotypeList" row-key="karyotypeId"
			:default-expand-all="isExpandAll" :tree-props="{ children: 'child', hasChildren: 'hasChild' }">
			<el-table-column prop="name" label="核型名称" />
			<el-table-column prop="karyotypeId" label="编号" align="left" width="200" />
			<!-- prettier-ignore -->
			<el-table-column prop="status" label="状态">
				<template #default="scope">
					<!-- prettier-ignore -->
					<data-single-tag :single-data.sync="scope.row.status" :status-options="statusOptions" />
				</template>
			</el-table-column>
			<el-table-column prop="remark" label="备注" />
			<el-table-column prop="createUserName" label="创建人" />
			<el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
				<template #default="scope">
					<el-link class="table_link_btn" :underline="false" type="primary" icon="edit"
						@click="handleUpdate(scope.row)" v-hasPermi="['livzon:karyotype:edit']"><span
							class="table_link_text">修改</span></el-link>
					<el-link class="table_link_btn" :underline="false" type="primary" icon="plus"
						@click="handleAdd(scope.row)" v-hasPermi="['livzon:karyotype:add']"><span
							class="table_link_text">新增</span></el-link>
					<el-link class="table_link_btn" :underline="false" v-if="scope.row.parentId != 0" type="danger"
						icon="delete" @click="handleDelete(scope.row)" v-hasPermi="['livzon:karyotype:remove']"><span
							class="table_link_text">删除</span></el-link>
				</template>
			</el-table-column>
		</el-table>

		<!-- 添加或修改核型对话框 -->
		<el-dialog :title="title" v-model="open" width="30%" append-to-body @closed="cleanSelect()">
			<el-form ref="karyotypeRef" :model="form" :rules="rules" label-width="100px">
				<el-row>
					<el-col :span="24" v-if="form.parentId !== 0">
						<el-form-item label="上级核型" prop="parentId">
							<el-tree-select v-model="form.parentId" :data="karyotypeOptions"
								:props="{ value: 'karyotypeId', label: 'name', children: 'child' }"
								value-key="karyotypeId" placeholder="选择上级核型" check-strictly filterable
								:render-after-expand="false" style="width: 100%;" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="核型名称" prop="name">
							<el-input v-model="form.name" placeholder="请输入核型名称" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="核型状态">
							<el-radio-group v-model="form.status">
								<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{
									dict.label
								}}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="form.remark" type="textarea" :autosize="{ minRows: 4 }"
								placeholder="请输入备注" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<!-- prettier-ignore -->
					<el-button type="primary" @click="submitForm()">确 定</el-button>
					<el-button @click="cancel()">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" name="karyotype" setup>
import karyotype from "@/api/request/admin/karyotype/index";
// prettier-ignore
const {
	loading, open, showSearch, title, karyotypeOptions, karyotypeList, isExpandAll, refreshTable, queryParams, form, rules, sys_normal_disable, queryRef,
	statusOptions, karyotypeRef, single, multiple, cancel,
	batchDelete, handleQuery, resetQuery, handleAdd, toggleExpandAll, handleUpdate, submitForm, handleDelete, ids,
	cleanSelect,
} = karyotype();
</script>
