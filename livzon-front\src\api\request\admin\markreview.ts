import request from "@/utils/request";

/**
 * 新增
 *
 * @returns
 */
export const addOrEdit = async (data: any) => {
	return await request({
		url: "/markReview/addOrEdit",
		method: "post",
		data: data,
	});
};


/**
 * 详情
 *
 * @returns
 */
export const markDetails = async (query?: any) => {
	return await request({
		url: "/markReview/details",
		method: "get",
		params: query,
	});
};

/**
 * 删除
 *
 * @returns
 */
export const markReviewDel = async (markReviewId: any) => {
	return await request({
		url: "/markReview/del/" + markReviewId,
		method: "delete",
	});
};

/**
 * 文件ID删除
 *
 * @returns
 */
export const fileByDel = async (fileId: any, type: any) => {
	return await request({
		url: "/markReview/file/del/" + fileId + "/" + type,
		method: "delete",
	});
};


