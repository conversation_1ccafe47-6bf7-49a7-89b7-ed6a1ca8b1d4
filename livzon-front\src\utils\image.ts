/**
 * 预加载图片
 * @param {string} url - 图片URL
 * @returns {Promise<HTMLImageElement>} - 返回加载完成的图片对象
 */
export const preloadImage = (url: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = url;
  });
};

/**
 * 预加载多个图片
 * @param {string[]} urls - 图片URL数组
 * @returns {Promise<HTMLImageElement[]>} - 返回所有加载完成的图片对象数组
 */
export const preloadImages = (urls: string[]): Promise<HTMLImageElement[]> => {
  return Promise.all(urls.map(url => preloadImage(url)));
}; 