<template>
	<div v-if="!item.hidden">
		<template
			v-if="
				hasOneShowingChild(item.children, item) &&
				(!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
				!item.alwaysShow
			"
		>
			<app-link
				v-if="onlyOneChild.meta && !isDisabled(onlyOneChild)"
				:to="resolvePath(onlyOneChild.path, onlyOneChild.query)"
			>
				<el-tooltip
					:disabled="!isDisabled(onlyOneChild)"
					content="功能暂未开放"
					placement="right"
					effect="dark"
				>
					<el-menu-item
						:index="resolvePath(onlyOneChild.path)"
						:class="{ 'submenu-title-noDropdown': !isNest }"
						:disabled="isDisabled(onlyOneChild)"
						@click="handleMenuClick(onlyOneChild, $event)"
					>
						<template #title>
							<span class="menu-flex">
								<svg-icon v-if="onlyOneChild.meta && onlyOneChild.meta.icon" :icon-class="onlyOneChild.meta.icon" class="menu-left-icon" />
								<span class="menu-title" :title="hasTitle(onlyOneChild.meta.title)">{{ onlyOneChild.meta.title }}</span>
								<i class="divider-line"></i>
							</span>
						</template>
					</el-menu-item>
				</el-tooltip>
			</app-link>
			<el-tooltip
				v-else-if="onlyOneChild.meta && isDisabled(onlyOneChild)"
				content="功能暂未开放"
				placement="right"
				effect="dark"
			>
				<el-menu-item
					:index="resolvePath(onlyOneChild.path)"
					:class="{ 'submenu-title-noDropdown': !isNest }"
					:disabled="true"
					@click="handleMenuClick(onlyOneChild, $event)"
				>
					<template #title>
						<span class="menu-flex">
							<svg-icon v-if="onlyOneChild.meta && onlyOneChild.meta.icon" :icon-class="onlyOneChild.meta.icon" class="menu-left-icon" />
							<span class="menu-title" :title="hasTitle(onlyOneChild.meta.title)">{{ onlyOneChild.meta.title }}</span>
							<i class="divider-line"></i>
						</span>
					</template>
				</el-menu-item>
			</el-tooltip>
		</template>

		<el-sub-menu
			v-else
			ref="subMenu"
			:index="resolvePath(item.path)"
			popper-append-to-body
		>
			<template v-if="item.meta" #title>
				<span class="menu-flex">
					<svg-icon :icon-class="item.meta && item.meta.icon" class="menu-left-icon" />
					<span class="menu-title" :title="hasTitle(item.meta.title)">{{
						item.meta.title
					}}</span>
					<i class="divider-line"></i>
				</span>
			</template>

			<sidebar-item
				v-for="child in item.children"
				:key="child.path"
				:is-nest="true"
				:item="child"
				:base-path="resolvePath(child.path)"
				class="nest-menu"
			/>
		</el-sub-menu>
	</div>
</template>

<script setup>
import { ref } from "vue";
import { isExternal } from "@/utils/validate";
import AppLink from "./Link.vue";
import { getNormalPath } from "@/utils/livzon";
import { ElMessage } from 'element-plus';

const props = defineProps({
	// route object
	item: {
		type: Object,
		required: true,
	},
	isNest: {
		type: Boolean,
		default: false,
	},
	basePath: {
		type: String,
		default: "",
	},
});

const onlyOneChild = ref({});

function hasOneShowingChild(children = [], parent) {
	if (!children) {
		children = [];
	}
	const showingChildren = children.filter((item) => {
		if (item.hidden) {
			return false;
		} else {
			// Temp set(will be used if only has one showing child)
			onlyOneChild.value = item;
			return true;
		}
	});

	// When there is only one child router, the child router is displayed by default
	if (showingChildren.length === 1) {
		return true;
	}

	// Show parent if there are no child router to display
	if (showingChildren.length === 0) {
		onlyOneChild.value = { ...parent, path: "", noShowingChildren: true };
		return true;
	}

	return false;
}

function resolvePath(routePath, routeQuery) {
	if (isExternal(routePath)) {
		return routePath;
	}
	if (isExternal(props.basePath)) {
		return props.basePath;
	}
	if (routeQuery) {
		let query = JSON.parse(routeQuery);
		return {
			path: getNormalPath(props.basePath + "/" + routePath),
			query: query,
		};
	}
	return getNormalPath(props.basePath + "/" + routePath);
}

function hasTitle(title) {
	if (title.length > 5) {
		return title;
	} else {
		return "";
	}
}

function isDisabled(route) {
	return route.meta?.title === 'dsDNA' || route.meta?.title === 'ANCA' || route.meta?.title === 'AMA\\SMA' || route.meta?.title === 'AKA\\APF';
}

function handleMenuClick(route, event) {
	if (isDisabled(route)) {
		event.preventDefault();
		ElMessage.warning('该功能暂未开放');
		return false;
	}
}
</script>

<style lang="scss" scoped>
.el-menu-item.is-disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

/**
 * 主菜单图标显示在文字左侧，且有合适间距
 */
.menu-flex {
	display: flex;
	align-items: center;
	min-width: 0;
	height: 40px;
	line-height: 40px;
	position: relative;
	padding-right: 20px;  /* 为分隔线留出空间 */

	.menu-left-icon {
		margin-right: 8px;
	}

	.menu-title {
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.divider-line {
		position: absolute;
		right: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 1px;
		height: 20px;
		background-color: #dcdfe6;
		margin: 0 10px;
	}
}

/* 最后一个菜单项不显示分隔线 */
.el-menu-item:last-child,
.el-sub-menu:last-child {
	.menu-flex .divider-line {
		display: none;
	}
}

/**
 * 菜单标题自适应宽度，防止图标被挤压
 * @description 使用flex布局，图标和标题分开，标题自适应剩余空间
 */
.menu-title {
	display: block;
	max-width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	vertical-align: middle;
	height: 40px;
	line-height: 40px;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title),
:deep(.el-menu--popup .el-menu-item) {
	color: #222 !important;
	font-weight: 500 !important;
	font-size: 16px !important;
}

:deep(.el-menu-item.is-active),
:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover),
:deep(.el-menu--popup .el-menu-item:hover),
:deep(.el-menu--popup .el-menu-item.is-active) {
	color: #1765d5 !important;
	font-weight: 600 !important;
	background: #f5f7fa !important;
}

:deep(.el-sub-menu__icon-arrow) {
	position: static !important;
	margin-left: auto;
	font-size: 16px;
	color: inherit;
	vertical-align: middle;
	margin-top: 2px;
}
</style>
