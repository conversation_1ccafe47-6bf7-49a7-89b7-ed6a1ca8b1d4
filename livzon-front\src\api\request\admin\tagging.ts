import request from "@/utils/request";

/**
 * 查询
 *
 * @returns
 */
export const query = async (query?: any) => {
	return await request({
		url: "/file/query",
		method: "get",
		params: query,
	});
};

/**
 * 新增
 *
 * @returns
 */
export const add = async (data: any) => {
	return await request({
		url: "/file/add",
		method: "post",
		data: data,
	});
};


/**
 * 详情
 *
 * @returns
 */
export const details = async (fileId: any, isTiter: boolean) => {
	return await request({
		url: "/file/details/" + fileId + "/" + isTiter,
		method: "get",
	});
};

/**
 * 编辑
 *
 * @returns
 */
export const edit = async (data: any) => {
	return await request({
		url: "/file/edit",
		method: "put",
		data: data,
	});
};

/**
 * 删除
 *
 * @returns
 */
export const del = async (fileIds: any) => {
	return await request({
		url: "/file/del/" + fileIds,
		method: "delete",
	});
};

/**
 * 上传
 *
 * @returns
 */
export const upload = async (data: any, uploadForm: any) => {
	return await request({
		url: "/file/upload/" + uploadForm.reagentManufacturer + "/" + uploadForm.imagingDevice,
		method: "post",
		data: data,
		withCredentials: true,
	});
};

/**
 * 导出参数
 *
 * @param {object} query 查询Obj
 * @returns
 */
export const exportFile = async (query: any) => {
	return await request({
		url: "/file/export",
		method: "get",
		params: query,
	});
};

/**
 * 清理参数缓存
 *
 * @returns
 */
export const clearCache = async () => {
	return await request({
		url: "/system/config/clearCache",
		method: "delete",
	});
};

/**
 * 识别
 *
 * @returns
 */
export const recognition = async (fileIds: any) => {
	return await request({
		url: "/file/recognition/" + fileIds,
		method: "put",
	});
};

/**
 * 下拉分配人员数据
 * @returns
 */
export const dropDown = async () => {
	return await request({
		url: "/file/dropDown",
		method: "get",
	});
};

/**
 * 下拉分配人员数据
 * @returns
 */
export const downReviewers = async () => {
	return await request({
		url: "/file/downReviewers",
		method: "get",
	});
};


/**
 * 分配
 *
 * @returns
 */
export const distribution = async (data: any) => {
	return await request({
		url: "/file/distribution",
		method: "put",
		data: data,
	});
};


/**
 * 人工标注-提交
 *
 * @returns
 */
export const submit = async (data: any) => {
	return await request({
		url: "/file/manualAnnotation/submit",
		method: "post",
		data: data,
	});
};

/**
 * 删除
 *
 * @returns
 */
export const manualAnnotationDel = async (manualAnnotationId: any) => {
	return await request({
		url: "/file/manualAnnotation/del/" + manualAnnotationId,
		method: "delete",
	});
};

/**
 * 文化ID删除
 *
 * @returns
 */
export const shouldManualAnnotationDel = async (fileId: any) => {
	return await request({
		url: "/file/should/manualAnnotation/del/" + fileId,
		method: "delete",
	});
};


/**
 * 下拉分配人员数据
 * @returns
 */
export const poster = async (fileId: any) => {
	return await request({
		url: "/file/poster/" + fileId,
		method: "get",
		headers: {
			isToken: false // 不携带token
		},
		withCredentials: false // 不携带cookie
	});
};



/**
 * 结果判定查询
 *
 * @returns
 */
export const resultJudgmentFind = async (query?: any) => {
	return await request({
		url: "/file/resultJudgmentFind",
		method: "get",
		params: query,
	});
};


/**
 * 标注、复核-复制
 *
 * @returns
 */
export const copy = async (data: any) => {
	return await request({
		url: "/file/copy",
		method: "post",
		data: data,
	});
};

/**
 * 复核-AI复制
 *
 * @returns
 */
export const copyAI = async (data: any) => {
	return await request({
		url: "/file/copyAI",
		method: "post",
		data: data,
	});
};


/**
 * 标注详情
 *
 * @returns
 */
export const manualAnnotationDetails = async (fileId: any) => {
	return await request({
		url: "/file/manualAnnotation/details/" + fileId,
		method: "get",
	});
};

/**
 * 大众角色
 *
 * @returns
 */
export const publicRole = async () => {
	return await request({
		url: "/file/publicRole",
		method: "get",
	});
};

/**
 * 大众角色
 *
 * @returns
 */
export const imagesDownload = async (filesIds: any) => {
	return await request({
		url: "/file/imagesDownload/" + filesIds,
		method: "get",
	});
};

/**
 * 获取有数据的日期统计
 * @param startDate 开始日期 (格式: YYYY-MM-DD)
 * @param endDate 结束日期 (格式: YYYY-MM-DD)
 * @returns 有数据的日期列表
 */
export const getDateStatistics = async (startDate?: string, endDate?: string) => {
	return await request({
		url: "/file/dateStatistics",
		method: "get",
		params: {
			startDate,
			endDate
		}
	});
};


