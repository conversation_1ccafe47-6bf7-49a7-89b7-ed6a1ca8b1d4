
import { setTableRowSelected } from '../../../../utils/livzon';
import { ref, getCurrentInstance, nextTick, onMounted, } from "vue";
import { ElForm, ElTable, } from "element-plus";
//核型分类树
import { karyotypeTree, add, details, edit, del } from "@/api/request/admin/karyotype";
export default () => {
	const { proxy } = getCurrentInstance() as any;
	const { sys_normal_disable } = proxy.useDict("sys_normal_disable");
	const statusOptions = ref<any>();
	const karyotypeList = ref<any>();
	const open = ref<boolean>(false);
	const loading = ref<boolean>(true);
	const showSearch = ref<boolean>(true);
	const title = ref<string>("");
	const karyotypeOptions = ref<any>();
	const isExpandAll = ref<boolean>(true);
	const refreshTable = ref<boolean>(true);
	const form = ref<any>();
	const queryParams = ref<any>({
		name: undefined,
		status: undefined,
	});

	//校验表单数据
	const rules = ref<any>({
		name: [
			{ required: true, message: "核型名称不能为空", trigger: "blur" },
		],
	});
	// 选中数组
	const ids = ref<any>();
	// 非单个禁用
	const single = ref<boolean>(true);
	// 非多个禁用
	const multiple = ref<boolean>(true);

	const karyotypeRef = ref<InstanceType<typeof ElForm>>();
	const queryRef = ref<InstanceType<typeof ElForm>>();
	const pageTableRef = ref<InstanceType<typeof ElTable>>();
	/** 查询核型列表 */
	const getList = async () => {
		loading.value = true;
		await karyotypeTree(queryParams.value).then((response: any) => {
			if (response.code === 200) {
				karyotypeList.value = proxy.handleTree(response.data, "karyotypeId");
				loading.value = false;
			}
		});
	};

	const cleanSelect = () => {
		proxy.cleanTableSelection(pageTableRef);
	};

	/** 取消按钮 */
	const cancel = () => {
		reset();
		cleanSelect();
		open.value = false;
	};
	/** 表单重置 */
	const reset = () => {
		form.value = {
			karyotypeId: undefined,
			parentId: undefined,
			name: undefined,
			status: "0",
			remark: undefined,
		};
		proxy.resetForm(karyotypeRef);
	};
	/** 搜索按钮操作 */
	const handleQuery = () => {
		getList();
	};
	/** 重置按钮操作 */
	const resetQuery = () => {
		proxy.resetForm(queryRef);
		handleQuery();
	};
	/** 新增按钮操作 */
	const handleAdd = async (row: any) => {
		reset();
		await karyotypeTree().then((response: any) => {
			if (response.code === 200) {
				karyotypeOptions.value = proxy.handleTree(response.data, "karyotypeId");
			}
		});
		if (row != undefined) {
			form.value.parentId = row.karyotypeId;
		}
		title.value = "添加核型";
		open.value = true;
	};
	/** 展开/折叠操作 */
	const toggleExpandAll = () => {
		refreshTable.value = false;
		isExpandAll.value = !isExpandAll.value;
		nextTick(() => {
			refreshTable.value = true;
		});
	};
	/** 修改按钮操作 */
	const handleUpdate = async (row: any) => {
		const karyotypeId = row.karyotypeId || ids.value[0];
		reset();
		await karyotypeTree(karyotypeId).then((response: any) => {
			if (response.code === 200) {
				karyotypeOptions.value = proxy.handleTree(response.data, "karyotypeId");
			}
		});
		await details(karyotypeId).then((response: any) => {
			if (response.code === 200) {
				const data = response.data;
				data.orderNum = parseInt(data.orderNum);
				// 修复顶级父核型显示为0的问题
				if (data.parentId === "0") {
					data.parentId = parseInt(data.parentId);
				}
				form.value = data;
				title.value = "修改核型";
				proxy.setTableRowSelected(pageTableRef, row, true);
				open.value = true;
			}
		});
	};
	/** 提交按钮 */
	const submitForm = async () => {
		await karyotypeRef.value?.validate((valid: boolean) => {
			if (valid) {
				if (form.value.karyotypeId !== undefined) {
					edit(form.value).then((response: any) => {
						if (response.code === 200) {
							proxy.$modal.msgSuccess("修改成功");
							open.value = false;
							handleQuery();
						}
					});
				} else {
					add(form.value).then((response: any) => {
						if (response.code === 200) {
							proxy.$modal.msgSuccess("新增成功");
							open.value = false;
							handleQuery();
						}
					});
				}
			}
		});
	};
	/** 删除按钮操作 */
	const handleDelete = (row: any) => {
		// 设置当前行被选中
		proxy.setTableRowSelected(pageTableRef, row, true);
		proxy.$modal
			.confirm('是否确认删除名称为"' + row.name + '"的数据项?')
			.then(() => {
				return del(row.karyotypeId);
			})
			.then((response: any) => {
				if (response.code === 200) {
					proxy.$modal.msgSuccess("删除成功");
					getList();
				}
			})
			.catch(() => {
				// 取消当前行选中
				proxy.setTableRowSelected(pageTableRef, row, false);
				console.log("取消了删除");
			});
	};

	/** 删除按钮操作 */
	const batchDelete = () => {
		const karyotypeIds = ids.value;
		// prettier-ignore
		proxy.$modal.confirm('是否确认删除编号为【"' + karyotypeIds + '"】的数据?')
			.then(() => {
				return del(karyotypeIds);
			})
			.then((response: any) => {
				if (response.code === 200) {
					proxy.$modal.msgSuccess("批量删除成功");
				}
			})
			.catch(() => {
				// 取消表格选中项
				cleanSelect();
				console.log("取消了批量删除");
			});
	};

	/**字典状态 */
	onMounted(() => {
		getList();
		proxy.getDicts("sys_normal_disable").then((response: any) => {
			statusOptions.value = response.data;
		});
	});
	// prettier-ignore
	return {
		loading, open, showSearch, title, karyotypeOptions, karyotypeList, isExpandAll, refreshTable, queryParams, form, rules, sys_normal_disable, queryRef,
		statusOptions, karyotypeRef, single, multiple, cancel,
		batchDelete, handleQuery, resetQuery, handleAdd, toggleExpandAll, handleUpdate, submitForm, handleDelete, ids,
		cleanSelect,
	};
};
