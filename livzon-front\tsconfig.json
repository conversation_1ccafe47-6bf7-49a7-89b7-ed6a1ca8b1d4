{
	"compilerOptions": {
		"allowJs": true,
		"target": "ESNext",
		"useDefineForClassFields": true,
		"module": "ESNext",
		"moduleResolution": "Node",
		"strict": true,
		"jsx": "preserve",
		"sourceMap": true,
		"resolveJsonModule": true,
		"isolatedModules": true,
		"esModuleInterop": true,
		"lib": ["ESNext", "DOM"],
		"skipLibCheck": true, // 不对第三方依赖类型检查 ，element-plus 生产打包报错
		"allowSyntheticDefaultImports": true, // 默认导入
		"suppressImplicitAnyIndexErrors": true, //加入这段代码解决main.ts报错问题
		"baseUrl": ".",
		"paths": {
			"@/*": ["src/*"]
		},
		"types": ["vite/client", "element-plus/global"]
	},

	"include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
	"exclude": ["node_modules", "dist", "script"],
	"references": [{ "path": "./tsconfig.node.json" }]
}
