import { ref, getCurrentInstance, onMounted } from "vue";
import { commentQuery, open, closed, del, commentReplyQuery } from "@/api/request/admin/share";
import { ElForm } from "element-plus";
import dayjs from 'dayjs';
import { debounce } from "lodash";

/**
 * @typedef {Object} QueryParams
 * @property {number} pageNum
 * @property {number} pageSize
 * @property {string=} fileName
 * @property {string=} name
 * @property {string=} comment
 * @property {string=} startTime
 * @property {string=} endTime
 * @property {number=} status
 */

interface QueryParams {
	pageNum: number;
	pageSize: number;
	fileName?: string;
	name?: string;
	comment?: string;
	startTime?: string;
	endTime?: string;
	status?: number;
}

/**
 * @typedef {Object} ReplyItem
 * @property {number} shareId
 * @property {string} name
 * @property {string} headImg
 * @property {string} comment
 * @property {string} time
 * @property {ReplyItem[]} child - 嵌套的子回复
 */
interface ReplyItem {
	shareId: number;
	name: string;
	headImg: string;
	comment: string;
	time: string;
	child?: ReplyItem[];
}

export default () => {
	const { proxy } = getCurrentInstance() as any;
	// 遮罩层
	const loading = ref<boolean>(true);
	// 总条数
	const total = ref<number>(0);
	// 表格数据
	const tablelist = ref<any>([]);
	// 表单查询ref
	const queryFormRef = ref<InstanceType<typeof ElForm>>();
	// 表单查询参数
	const queryParams = ref<QueryParams>({
		pageNum: 1,
		pageSize: 10,
		fileName: undefined,
		name: undefined,
		comment: undefined,
		startTime: undefined,
		endTime: undefined,
		status: undefined,
	});

	/** 查询列表 */
	const getList = () => {
		loading.value = true;
		// 拷贝一份参数，避免直接修改响应式对象
		const params = { ...queryParams.value };
		if (params.startTime) {
			params.startTime = dayjs(params.startTime).format('YYYY-MM-DD HH:mm:ss');
		}
		if (params.endTime) {
			params.endTime = dayjs(params.endTime).format('YYYY-MM-DD HH:mm:ss');
		}
		commentQuery(params).then((response: any) => {
			tablelist.value = response.data.list;
			total.value = parseInt(response.data.total);
			loading.value = false;
		});
	};

	/**
	 * @description 获取评论回复数据，支持递归子回复结构
	 * @param {number} fileId - 文件ID
	 * @returns {Promise<ReplyItem[]>} 回复数据数组
	 */
	const getReplyList = async (fileId: number): Promise<ReplyItem[]> => {
		try {
			const response = await commentReplyQuery({ fileId });
			return response.data?.list || [];
		} catch (error) {
			console.error('获取回复数据失败:', error);
			return [];
		}
	};

	/**
	 * @description 递归展平回复数据，将嵌套结构转换为扁平列表用于显示
	 * @param {ReplyItem[]} replies - 回复数据
	 * @param {number} level - 嵌套层级
	 * @returns {Array} 展平后的回复列表
	 */
	const flattenReplies = (replies: ReplyItem[], level: number = 0): any[] => {
		const result: any[] = [];
		
		replies.forEach(reply => {
			// 添加当前回复
			result.push({
				...reply,
				level, // 添加层级信息用于缩进显示
				id: reply.shareId // 确保有id字段
			});
			
			// 递归处理子回复
			if (reply.child && reply.child.length > 0) {
				result.push(...flattenReplies(reply.child, level + 1));
			}
		});
		
		return result;
	};

	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.value.pageNum = 1;
		getList();
	};
	/** 重置按钮操作 */
	const resetQuery = () => {
		proxy.resetForm(queryFormRef);
		handleQuery();
	};
	/**
	 * @description 切换状态按钮操作，根据当前行 status 调用 open 或 closed 接口
	 * @param row 当前行数据
	 */
	const handleToggleStatus = debounce((row: any) => {
		if (row.status == 0) {
			// 当前为开放，点击后关闭
			closed(row).then(() => {
				getList();
				proxy.$modal.msgWarning("已关闭！");
			});
		} else {
			// 当前为关闭，点击后开放
			open(row).then(() => {
				getList();
				proxy.$modal.msgSuccess("已开放！");
			});
		}
	}, 300);
	/** 删除按钮操作 */
	const handleDelete = (row: any) => {
		proxy.$modal.confirm('是否确认删除数据项?', "警告")
			.then(() => {
				return del(row);
			})
			.then((response: any) => {
				if (response.code === 200) {
					getList();
					proxy.$modal.msgSuccess("删除成功");
				}
			})
			.catch(() => {
				console.log("取消了删除");
			});
	};
	/**
	 * @description 处理分页事件
	 * @param {Object} data - 分页数据
	 * @param {number} data.page - 当前页码
	 * @param {number} data.limit - 每页条数
	 */
	const handlePagination = (data: { page: number; limit: number }) => {
		queryParams.value.pageNum = data.page;
		queryParams.value.pageSize = data.limit;
		getList();
	};

	onMounted(() => {
		getList();
	});

	// prettier-ignore
	return {
		loading, total, tablelist, queryParams, queryFormRef, handleQuery, resetQuery, handleToggleStatus, handleDelete, handlePagination, getReplyList, flattenReplies
	};
};
