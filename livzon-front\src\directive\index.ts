export { hasPermi, hasRole } from "./permission";
// import dialogDrag from "./dialog/drag";
// import dialogDragWidth from "./dialog/dragWidth";
// import dialogDragHeight from "./dialog/dragHeight";
// // import clipboard from "./module/clipboard";
// import highlight from "./module/highlight";

// export default function directive(app: any) {
// 	app.directive("hasRole", hasRole);
// 	app.directive("hasPermi", hasPermi);
// 	// 引入了vue-clipboard2，暂时注释
// 	// app.directive("clipboard", clipboard);
// 	app.directive("dialogDrag", dialogDrag);
// 	app.directive("dialogDragWidth", dialogDragWidth);
// 	app.directive("dialogDragHeight", dialogDragHeight);
// 	app.directive("highlight", highlight);
// }
