<template>
	<el-card>
		<el-tabs v-model="activeName">
			<el-tab-pane label="基本信息" name="basic">
				<basic-info-form ref="basicInfo" :info="info" />
			</el-tab-pane>
			<el-tab-pane label="字段信息" name="cloum">
				<el-table
					ref="dragTable"
					:data="cloumns"
					row-key="columnId"
					:max-height="tableHeight"
				>
					<el-table-column
						label="序号"
						type="index"
						width="50"
						class-name="allowDrag"
					/>
					<el-table-column
						label="字段列名"
						prop="columnName"
						width="200"
						:show-overflow-tooltip="true"
					/>
					<el-table-column label="字段描述" min-width="10%" style: `position： ${123}`>
						<template #default="scope">
							<el-input
								v-model="scope.row.columnComment"
							></el-input>
						</template>
					</el-table-column>
					<el-table-column
						label="物理类型"
						prop="columnType"
						min-width="10%"
						:show-overflow-tooltip="true"
					/>
					<el-table-column label="Java类型" min-width="11%">
						<template #default="scope">
							<el-select v-model="scope.row.javaType">
								<el-option label="Long" value="Long" />
								<el-option label="String" value="String" />
								<el-option label="Integer" value="Integer" />
								<el-option label="Double" value="Double" />
								<el-option
									label="BigDecimal"
									value="BigDecimal"
								/>
								<el-option label="Date" value="Date" />
							</el-select>
						</template>
					</el-table-column>
					<el-table-column label="java属性" min-width="10%">
						<template #default="scope">
							<el-input v-model="scope.row.javaField"></el-input>
						</template>
					</el-table-column>

					<el-table-column label="插入" min-width="5%">
						<template #default="scope">
							<el-checkbox
								true-label="1"
								v-model="scope.row.isInsert"
							></el-checkbox>
						</template>
					</el-table-column>
					<el-table-column label="编辑" min-width="5%">
						<template #default="scope">
							<el-checkbox
								true-label="1"
								v-model="scope.row.isEdit"
							></el-checkbox>
						</template>
					</el-table-column>
					<el-table-column label="列表" min-width="5%">
						<template #default="scope">
							<el-checkbox
								true-label="1"
								v-model="scope.row.isList"
							></el-checkbox>
						</template>
					</el-table-column>
					<el-table-column label="查询" min-width="5%">
						<template #default="scope">
							<el-checkbox
								true-label="1"
								v-model="scope.row.isQuery"
							></el-checkbox>
						</template>
					</el-table-column>
					<el-table-column label="查询方式" min-width="10%">
						<template #default="scope">
							<el-select v-model="scope.row.queryType">
								<el-option label="=" value="EQ" />
								<el-option label="!=" value="NE" />
								<el-option label=">" value="GT" />
								<el-option label=">=" value="GTE" />
								<el-option label="<" value="LT" />
								<el-option label="<=" value="LTE" />
								<el-option label="LIKE" value="LIKE" />
								<el-option label="BETWEEN" value="BETWEEN" />
							</el-select>
						</template>
					</el-table-column>
					<el-table-column label="必填" min-width="5%">
						<template #default="scope">
							<el-checkbox
								true-label="1"
								v-model="scope.row.isRequired"
							></el-checkbox>
						</template>
					</el-table-column>
					<el-table-column label="显示类型" min-width="12%">
						<template #default="scope">
							<el-select v-model="scope.row.htmlType">
								<el-option label="文本框" value="input" />
								<el-option label="文本域" value="textarea" />
								<el-option label="下拉框" value="select" />
								<el-option label="单选框" value="radio" />
								<el-option label="复选框" value="checkbox" />
								<el-option label="日期控件" value="datetime" />
								<!-- prettier-ignore -->
								<el-option label="图片上传" value="imageUpload"/>
								<!-- prettier-ignore -->
								<el-option label="文件上传" value="fileUpload"/>
								<el-option label="富文本控件" value="editor" />
							</el-select>
						</template>
					</el-table-column>
					<el-table-column label="字典类型" min-width="12%">
						<template #default="scope">
							<el-select
								v-model="scope.row.dictType"
								clearable
								filterable
								placeholder="请选择"
							>
								<el-option
									v-for="dict in dictOptions"
									:key="dict.dictType"
									:label="dict.dictName"
									:value="dict.dictType"
								>
									<!-- prettier-ignore -->
									<span style="float: left">
                                        {{ dict.dictName }}
                                    </span>
									<!-- prettier-ignore -->
									<span style="float: right; color: #8492a6; font-size: 13px">
                                        {{ dict.dictType }}
                                    </span>
								</el-option>
							</el-select>
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
			<el-tab-pane label="生成信息" name="genInfo">
				<!-- prettier-ignore -->
				<gen-info-form ref="genInfo" :info="info" :tables="tables" :menus="menus" />
			</el-tab-pane>
		</el-tabs>
		<!-- prettier-ignore -->
		<div v-show="isRouter" style="text-align: center;margin-left: -100px;margin-top: 20px;">
            <el-button type="primary" @click="submitForm()">提交</el-button>
            <el-button @click="close()">返回</el-button>
        </div>
	</el-card>
</template>
<script lang="ts" name="GenEditTable" setup>
// prettier-ignore
import basicInfoForm from "./basicInfoForm.vue";
import genInfoForm from "./genInfoForm.vue";
import EditTable from "@/api/request/system/tool/gen/editTable";
// prettier-ignore
const  { activeName, tableHeight, tables, cloumns, dictOptions, menus, info, submitForm, close, initTabsData, isRouter } = EditTable();
let props = defineProps(['tableId']);
console.log("111111", props.tableId);

// const emit = defineEmits(["cleanTableSelect"]);
        
// emit("cleanTableSelect");
// console.log("执行了清除选中");
    



// 暴露方法
defineExpose({
    submitForm, initTabsData
});


</script>

