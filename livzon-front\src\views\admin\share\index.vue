<template>
	<div class="app-container">
		<!-- 搜索表单：响应式布局 -->
		<el-form ref="queryFormRef" :model="queryParams" :inline="false" label-width="80px" label-position="top" 
			class="search-form mobile-form desktop-inline-form">
			<el-row :gutter="16">
				<el-col :xs="24" :sm="12" :md="8" :lg="6">
					<el-form-item label="文件名称" prop="fileName">
						<el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable maxlength="256"
							show-word-limit @keyup.enter.native="handleQuery" />
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="8" :lg="6">
					<el-form-item label="微信名" prop="name">
						<el-input v-model="queryParams.name" placeholder="请输入微信名" clearable maxlength="32"
							show-word-limit @keyup.enter.native="handleQuery" />
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="8" :lg="6">
					<el-form-item label="开始时间" prop="startTime">
						<el-date-picker v-model="queryParams.startTime" type="datetime" placeholder="请选择开始时间" 
							style="width: 100%" />
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="8" :lg="6">
					<el-form-item label="结束时间" prop="endTime">
						<el-date-picker v-model="queryParams.endTime" type="datetime" placeholder="请选择结束时间" 
							style="width: 100%" />
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="8" :lg="6">
					<el-form-item label="评论内容" prop="comment">
						<el-input v-model="queryParams.comment" placeholder="请输入评论内容" clearable maxlength="512"
							show-word-limit @keyup.enter.native="handleQuery" />
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="8" :lg="6">
					<el-form-item label="开放状态" prop="status">
						<el-select v-model="queryParams.status" placeholder="请选择开放状态" clearable style="width: 100%">
							<el-option label="开放" :value="0" />
							<el-option label="关闭" :value="1" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :xs="24" :sm="12" :md="8" :lg="6">
					<el-form-item label=" " class="button-form-item">
						<form-search @reset="resetQuery" @search="handleQuery" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<!-- 桌面端表格展示 -->
		<div class="desktop-table">
			<!-- prettier-ignore -->
			<el-table v-loading="loading"
				:data="tablelist"
				style="width: 100%;" max-height="610"
				row-key="shareId">
				<el-table-column label="序号" type="index" align="center" width="55">
					<template #default="scope">
						<!-- prettier-ignore -->
						<span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
					</template>
				</el-table-column>
				<el-table-column label="文件名称" align="center" prop="fileName" :show-overflow-tooltip="true" />
				<el-table-column label="头像" align="center" width="80" prop="headImg">
					<template #default="scope">
						<!--
							/**
							 * @description 渲染用户头像：有图片显示图片，无图片则显示微信名首字母的圆形背景色头像
							 * @param {Object} scope - 当前行作用域
							 */
						-->
						<template v-if="scope.row.headImg && scope.row.headImg.trim()">
							<img :src="scope.row.headImg.trim()" alt="头像" class="avatar-img"
								@error="e => { const target = e.target as HTMLImageElement; if (target) target.src = require('@/assets/default-avatar.png'); }" />
						</template>
						<template v-else>
							<div class="avatar-img custom-avatar" :title="scope.row.name">
								{{ scope.row.name && scope.row.name[0] ? scope.row.name[0] : '' }}
							</div>
						</template>
					</template>
				</el-table-column>
				<el-table-column label="微信名" align="center" prop="name" :show-overflow-tooltip="true">
					<template #default="scope">
						<!--
							/**
							 * @description 微信名显示：如果有toName则显示"name回复toName"格式，否则直接显示name
							 * @param {Object} scope - 当前行作用域
							 */
						-->
						<span v-if="scope.row.toName">
							{{ scope.row.name }}回复{{ scope.row.toName }}
						</span>
						<span v-else>
							{{ scope.row.name }}
						</span>
					</template>
				</el-table-column>
				<el-table-column label="评论内容" align="center" prop="comment" width="300">
					<template #default="scope">
						<!-- JSDoc: 渲染评论内容为HTML，自动换行 -->
						<div class="comment-html" v-html="scope.row.comment"></div>
					</template>
				</el-table-column>
				<el-table-column label="评论时间" align="center" prop="time" :show-overflow-tooltip="true">
					<template #default="scope">
						<span>{{ parseTime(scope.row.time) }}</span>
					</template>
				</el-table-column>
				<el-table-column label="开放状态" align="center" prop="status" width="100" :show-overflow-tooltip="true">
					<template #default="scope">
						<span>{{ scope.row.status == 0 ? '开放' : '关闭' }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
					<template #default="scope">
						<el-link :underline="false" class="table_link_btn" size="small"
							:type="scope.row.status == 0 ? 'warning' : 'success'" style="margin-left: 8px"
							@click="handleToggleStatus(scope.row)" v-hasPermi="['livzon:share:status']">
							<span>{{ scope.row.status == 0 ? '关闭' : '开放' }}</span>
						</el-link>
						<el-link :underline="false" class="table_link_btn" size="small" type="danger"
							style="margin-left: 8px" @click="handleDelete(scope.row)" v-hasPermi="['livzon:share:delete']">
							<span>删除</span>
						</el-link>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 移动端卡片展示 -->
		<div class="mobile-cards" v-loading="loading">
			<div v-for="(item, index) in tablelist" :key="item.id || index" class="share-card">
				<div class="card-header">
					<div class="user-info">
						<div class="avatar-wrapper">
							<!--
								/**
								 * @description 移动端头像显示：有图片显示图片，无图片则显示微信名首字母的圆形背景色头像
								 * @param {Object} item - 当前数据项
								 */
							-->
							<template v-if="item.headImg && item.headImg.trim()">
								<img :src="item.headImg.trim()" alt="头像" class="avatar-img-mobile" />
							</template>
							<template v-else>
								<div class="avatar-img-mobile custom-avatar-mobile" :title="item.name || '未知用户'">
									{{ (item.name && item.name[0]) ? item.name[0].toUpperCase() : '?' }}
								</div>
							</template>
						</div>
						<div class="user-details">
							<!--
								/**
								 * @description 移动端微信名显示：如果有toName则显示"name回复toName"格式，否则直接显示name
								 * @param {Object} item - 当前数据项
								 */
							-->
							<div class="username">
								<span v-if="item.toName">
									{{ item.name || '未知用户' }}回复{{ item.toName }}
								</span>
								<span v-else>
									{{ item.name || '未知用户' }}
								</span>
							</div>
							<div class="comment-time">{{ item.time ? parseTime(item.time) : '时间未知' }}</div>
						</div>
					</div>
					<div class="status-badge" :class="{ 'status-open': item.status == 0, 'status-closed': item.status == 1 }">
						{{ item.status == 0 ? '开放' : '关闭' }}
					</div>
				</div>
				
				<div class="card-content">
					<div class="info-row" v-if="item.fileName">
						<div class="info-item">
							<span class="label">文件名称</span>
							<span class="value">{{ item.fileName }}</span>
						</div>
					</div>
					
					<div class="info-row" v-if="item.comment">
						<div class="info-item full-width">
							<span class="label">评论内容</span>
							<!-- JSDoc: 移动端评论内容渲染为HTML -->
							<div class="comment-html-mobile" v-html="item.comment"></div>
						</div>
					</div>
					
					<!-- 序号显示 -->
					<div class="info-row">
						<div class="info-item">
							<span class="label">序号</span>
							<span class="value">{{ (queryParams.pageNum - 1) * queryParams.pageSize + index + 1 }}</span>
						</div>
					</div>
				</div>

				<div class="card-actions">
					<el-button 
						:type="item.status == 0 ? 'warning' : 'success'" 
						size="small" 
						plain
						@click="handleToggleStatus(item)" 
						v-hasPermi="['livzon:share:status']">
						{{ item.status == 0 ? '关闭' : '开放' }}
					</el-button>
					<el-button 
						type="danger" 
						size="small" 
						plain
						@click="handleDelete(item)" 
						v-hasPermi="['livzon:share:delete']">
						删除
					</el-button>
				</div>
			</div>
			
			<!-- 移动端无数据提示 -->
			<div v-if="!loading && tablelist.length === 0" class="no-data-mobile">
				<el-empty description="暂无数据" />
			</div>
		</div>

		<!-- 分页组件 -->
		<div class="pagination-wrapper">
			<pagination v-if="total > 0" :total="total" v-model:page="queryParams.pageNum"
				v-model:limit="queryParams.pageSize" @pagination="handlePagination" />
		</div>
	</div>
</template>

<script lang="ts" name="index" setup>
import share from "@/api/request/admin/share/index";
const { loading, total, tablelist, queryParams, queryFormRef, handleQuery, resetQuery, handleToggleStatus, handleDelete, handlePagination } = share();
</script>

<style scoped>
/**
 * @description 移动端优先的响应式样式
 */

/* 基础样式 */
.app-container {
	padding: 12px;
	background-color: #f5f5f5;
}

/* 搜索表单样式 */
.search-form {
	background: #fff;
	padding: 16px;
	border-radius: 8px;
	margin-bottom: 16px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.mobile-form .el-form-item {
	margin-bottom: 16px;
}

.mobile-form .el-form-item__label {
	padding-bottom: 8px !important;
	font-weight: 500;
	color: #333;
	font-size: 14px;
}

.button-form-item .el-form-item__label {
	display: none !important;
}

.button-form-item .el-form-item__content {
	margin-left: 0 !important;
}

/* 响应式显示控制 */
.desktop-table {
	display: none;
}

.mobile-cards {
	display: block;
}

/* 移动端卡片样式 */
.share-card {
	background: #fff;
	border-radius: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
	margin-bottom: 12px;
	padding: 16px;
	border: 1px solid #f0f0f0;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16px;
	padding-bottom: 12px;
	border-bottom: 1px solid #f5f5f5;
}

.user-info {
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 0;
}

.avatar-wrapper {
	margin-right: 12px;
	flex-shrink: 0;
}

.avatar-img-mobile {
	width: 44px;
	height: 44px;
	border-radius: 50%;
	object-fit: cover;
	border: 2px solid #f0f0f0;
}

.custom-avatar-mobile {
	width: 44px;
	height: 44px;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	font-size: 18px;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
	user-select: none;
	border: 2px solid #f0f0f0;
}

.user-details {
	flex: 1;
	min-width: 0;
}

.username {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 4px;
	word-break: break-all;
	line-height: 1.3;
}

.comment-time {
	font-size: 12px;
	color: #999;
	line-height: 1.2;
}

.status-badge {
	padding: 6px 12px;
	border-radius: 16px;
	font-size: 12px;
	font-weight: 500;
	flex-shrink: 0;
	margin-left: 8px;
}

.status-open {
	background: #e8f5e8;
	color: #52c41a;
	border: 1px solid #b7eb8f;
}

.status-closed {
	background: #fff1f0;
	color: #ff4d4f;
	border: 1px solid #ffccc7;
}

.card-content {
	margin-bottom: 16px;
}

.info-row {
	margin-bottom: 12px;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-item {
	display: flex;
	align-items: flex-start;
	gap: 8px;
}

.info-item.full-width {
	flex-direction: column;
	gap: 6px;
}

.label {
	font-size: 13px;
	color: #666;
	font-weight: 500;
	min-width: 60px;
	flex-shrink: 0;
}

.full-width .label {
	min-width: auto;
}

.value {
	font-size: 14px;
	color: #333;
	flex: 1;
	word-break: break-all;
	line-height: 1.4;
}

.comment-html-mobile {
	word-break: break-all;
	white-space: pre-wrap;
	font-size: 14px;
	color: #333;
	line-height: 1.5;
	background: #fafafa;
	padding: 8px 12px;
	border-radius: 6px;
	border-left: 3px solid #409eff;
}

/**
 * @description 移动端评论内容中的链接样式
 */
:deep(.comment-html-mobile a) {
	color: #409EFF !important;
	text-decoration: underline;
	word-break: break-all;
}

.card-actions {
	display: flex;
	gap: 8px;
	justify-content: flex-end;
	padding-top: 12px;
	border-top: 1px solid #f5f5f5;
}

.card-actions .el-button {
	flex: 1;
	max-width: 80px;
}

.no-data-mobile {
	text-align: center;
	padding: 60px 20px;
	background: #fff;
	border-radius: 8px;
}

/* 分页组件样式 */
.pagination-wrapper {
	margin-top: 16px;
}

/* 移动端显示跳转功能 */
@media (max-width: 768px) {
	.pagination-wrapper :deep(.el-pagination__jump) {
		display: inline-block !important;
		margin-left: 8px;
		font-size: 13px;
	}
	
	.pagination-wrapper :deep(.el-pagination__jump .el-input) {
		width: 50px !important;
	}
	
	.pagination-wrapper :deep(.el-pagination__jump .el-input__inner) {
		height: 28px !important;
		line-height: 28px !important;
		font-size: 12px !important;
		padding: 0 8px !important;
		text-align: center !important;
	}
	
	/* 保持每页条数隐藏，只显示跳转 */
	.pagination-wrapper :deep(.el-pagination__sizes) {
		display: none !important;
	}
}

/* 桌面端样式 */
@media (min-width: 768px) {
	.app-container {
		padding: 20px;
		background-color: #fff;
	}
	
	.desktop-table {
		display: block;
	}
	
	.mobile-cards {
		display: none;
	}
	
	.search-form {
		margin-bottom: 20px;
		background: transparent;
		padding: 0;
		box-shadow: none;
		border-radius: 0;
	}
	
	/* 桌面端恢复原有表单样式 */
	.desktop-inline-form {
		display: block !important;
		width: 100% !important;
	}
	
	.desktop-inline-form .el-row {
		display: block !important;
	}
	
	.desktop-inline-form .el-col {
		display: inline-block !important;
		width: auto !important;
		vertical-align: top !important;
		margin-right: 16px !important;
		margin-bottom: 0 !important;
	}
	
	.desktop-inline-form .el-form-item {
		display: inline-block !important;
		margin-bottom: 18px !important;
		margin-right: 0 !important;
		vertical-align: top !important;
	}
	
	.desktop-inline-form .el-form-item__label {
		padding-bottom: 0 !important;
		font-size: 14px !important;
		text-align: right !important;
		width: 68px !important;
		padding-right: 12px !important;
		display: inline-block !important;
		vertical-align: middle !important;
		position: static !important;
		line-height: 32px !important;
	}
	
	.desktop-inline-form .el-form-item__content {
		display: inline-block !important;
		vertical-align: middle !important;
		margin-left: 0 !important;
		line-height: 32px !important;
	}
	
	.desktop-inline-form .button-form-item .el-form-item__label {
		display: none !important;
	}
	
	.desktop-inline-form .button-form-item .el-form-item__content {
		margin-left: 0 !important;
	}
	
	/* 确保日期选择器宽度正常 */
	.desktop-inline-form .el-date-editor {
		width: 180px !important;
	}
	
	/* 确保下拉选择器宽度正常 */
	.desktop-inline-form .el-select {
		width: 140px !important;
	}
	
	/* 确保输入框宽度正常 */
	.desktop-inline-form .el-input {
		width: 200px !important;
	}
	
	.desktop-inline-form .el-input__inner {
		width: 100% !important;
	}
}

/* 平板端样式优化 */
@media (min-width: 768px) and (max-width: 1024px) {
	.share-card {
		margin-bottom: 12px;
		padding: 12px;
	}
	
	.card-actions {
		justify-content: center;
	}
}

/* 桌面端原有样式保持 */
@media (min-width: 768px) {
	.comment-html {
		word-break: break-all;
		white-space: pre-wrap;
		text-align: left;
		max-height: 120px;
		overflow-y: auto;
		line-height: 1.6;
		scroll-behavior: smooth;
	}

	/**
	 * @description 桌面端评论内容中的链接样式
	 */
	:deep(.comment-html a) {
		color: #409EFF !important;
		text-decoration: underline;
		word-break: break-all;
	}

	/**
	 * @description 桌面端头像样式
	 */
	.avatar-img {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		object-fit: cover;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #fff;
		font-size: 16px;
		font-weight: 600;
		user-select: none;
		overflow: hidden;
		flex-shrink: 0;
		box-sizing: border-box;
	}

	.custom-avatar {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #fff;
	}
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
	.app-container {
		padding: 8px;
	}
	
	.search-form {
		padding: 12px;
		margin-bottom: 12px;
	}
	
	.mobile-form .el-form-item {
		margin-bottom: 12px;
	}
	
	.share-card {
		padding: 12px;
		margin-bottom: 10px;
		border-radius: 8px;
	}
	
	.card-header {
		margin-bottom: 12px;
		padding-bottom: 8px;
	}
	
	.avatar-img-mobile, .custom-avatar-mobile {
		width: 36px;
		height: 36px;
		font-size: 16px;
	}
	
	.avatar-wrapper {
		margin-right: 10px;
	}
	
	.username {
		font-size: 15px;
	}
	
	.comment-time {
		font-size: 11px;
	}
	
	.status-badge {
		padding: 4px 8px;
		font-size: 11px;
	}
	
	.card-content {
		margin-bottom: 12px;
	}
	
	.info-row {
		margin-bottom: 8px;
	}
	
	.label {
		font-size: 12px;
		min-width: 50px;
	}
	
	.value {
		font-size: 13px;
	}
	
	.comment-html-mobile {
		font-size: 13px;
		padding: 6px 8px;
	}
	
	.card-actions {
		flex-direction: row;
		gap: 6px;
		padding-top: 8px;
	}
	
	.card-actions .el-button {
		flex: 1;
		max-width: none;
		font-size: 12px;
		padding: 6px 8px;
	}
	
	/* 小屏幕跳转功能优化 */
	.pagination-wrapper :deep(.el-pagination__jump) {
		font-size: 12px;
		margin-left: 6px;
	}
	
	.pagination-wrapper :deep(.el-pagination__jump .el-input) {
		width: 45px !important;
	}
	
	.pagination-wrapper :deep(.el-pagination__jump .el-input__inner) {
		height: 26px !important;
		line-height: 26px !important;
		font-size: 11px !important;
		padding: 0 6px !important;
	}
}
</style>
