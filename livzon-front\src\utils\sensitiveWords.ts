/**
 * 敏感词检测工具
 * @description 提供完整的敏感词检测功能，包含丰富的敏感词库
 * <AUTHOR> Assistant
 */

/**
 * 敏感词类别接口
 */
interface SensitiveWordCategories {
    political: string[];
    adult: string[];
    gambling: string[];
    drugs: string[];
    violence: string[];
    fraud: string[];
    illegal_drugs: string[];
    other_illegal: string[];
    cybercrime: string[];
}

/**
 * 检测结果接口
 */
interface DetectionResult {
    isValid: boolean;
    message: string;
    sensitiveWords: string[];
    filteredText: string;
}

/**
 * 敏感词匹配结果接口
 */
interface MatchResult {
    word: string;
    index: number;
    length: number;
}

/**
 * Trie树节点接口
 */
interface TrieNode {
    [key: string]: TrieNode | boolean | string | undefined;
    isEnd?: boolean;
    pattern?: string;
}

/**
 * 统计信息接口
 */
interface Statistics {
    totalWords: number;
    categories: Record<string, number>;
}

/**
 * 完整的敏感词库
 */
const SENSITIVE_WORDS: SensitiveWordCategories = {
    // 政治敏感词
    political: [
        '法轮功', '法轮大法', '轮子功', '李洪志', '真善忍', '转法轮', '明慧网',
        '六四', '89学潮', '64事件', '天安门事件', '学生运动', '戒严',
        '达赖', '达赖喇嘛', '班禅', '西藏独立', '藏独', '自由西藏',
        '新疆独立', '疆独', '东突', '世维会', '热比娅',
        '台湾独立', '台独', '中华民国', '两国论', '一边一国', '民进党',
        '香港独立', '港独', '占中', '雨伞革命', '反送中', '光复香港',
        '民运', '民主运动', '异议人士', '政治犯', '良心犯', '人权活动家',
        '颠覆国家政权', '煽动颠覆', '反政府', '推翻政权', '政治改革',
        '多党制', '三权分立', '军队国家化', '新闻自由', '司法独立',
        '共产党下台', '一党专政', '专制', '独裁', '威权', '极权',
        '平反六四', '结束一党专政', '打倒共产党', '退党', '三退',
        '九评共产党', '大纪元', '新唐人', '自由亚洲', 'BBC中文网',
        '刘晓波', '艾未未', '陈光诚', '高智晟', '王丹', '吾尔开希',
        '魏京生', '王军涛', '胡佳', '郭飞雄', '浦志强', '许志永'
    ],

    // 黄色内容
    adult: [
        '色情', '淫秽', '淫荡', '淫乱', '淫威', '淫欲', '淫水',
        '裸体', '裸照', '裸聊', '裸奔', '全裸', '半裸', '脱光',
        '性爱', '做爱', '性交', '交配', '啪啪', '爱爱', 'ML', 'sex',
        '强奸', '强暴', '轮奸', '迷奸', '诱奸', '乱伦', '兽交',
        '卖淫', '嫖娼', '妓女', '鸡店', '红灯区', '性服务', '援交',
        '约炮', '一夜情', '419', '3P', '4P', '群P', '换妻',
        '包养', '小三', '二奶', '情人', '偷情', '出轨', '婚外情',
        '性感', '诱惑', '勾引', '调戏', '猥亵', '性侵', '性骚扰',
        '春药', '催情', '壮阳', '伟哥', '性药', '情趣用品', '成人用品',
        '阴茎', '阳具', '阴道', '阴部', '私处', '下体', '生殖器',
        '胸部', '乳房', '乳头', '屁股', '臀部', '大腿', '丝袜',
        '内裤', '胸罩', '避孕套', '安全套', '套套', 'TT',
        '手淫', '自慰', '撸管', '打飞机', '自撸', 'SY',
        '高潮', '射精', '精液', '白浊', '爽死', '床上', '床戏',
        '激情', '诱人', '性欲', '肉欲', '欲火', '骚货', '荡妇',
        '黄片', 'AV', '成人电影', '毛片', 'A片', '色片', '情色'
    ],

    // 赌博内容
    gambling: [
        '赌博', '赌场', '赌钱', '赌球', '赌马', '赌注', '下注', '押注',
        '博彩', '彩票', '福彩', '体彩', '刮刮乐', '双色球', '大乐透',
        '六合彩', '香港六合彩', '马会', '特码', '波色', '生肖彩',
        '时时彩', '快三', '11选5', '排列三', '排列五', 'PK10',
        '北京赛车', '幸运飞艇', '幸运28', '加拿大28', 'PC蛋蛋',
        '百家乐', '21点', '德州扑克', '梭哈', '炸金花', '斗地主',
        '老虎机', '水果机', '游戏机', '电玩城', '街机',
        '庄家', '闲家', '赔率', '开盘', '封盘', '盘口', '让球',
        '大小球', '单双', '龙虎', '和值', '跨度', '胆码',
        '投注', '下单', '竞猜', '竞彩', '外围', '黑彩', '私彩',
        '抽奖', '摇奖', '开奖', '中奖', '奖金', '返水', '反水',
        '洗码', '代理', '上线', '下线', '会员', '充值', '提现',
        '澳门赌场', '拉斯维加斯', '蒙特卡洛', '金沙', '威尼斯人',
        '筹码', '赌桌', '荷官', '发牌', '洗牌', '切牌',
        '必胜法', '包赢', '稳赚', '回本', '翻倍', '加倍',
        '网赌', '网络赌博', '线上赌博', '手机赌博', 'APP赌博'
    ],

    // 毒品相关
    drugs: [
        '毒品', '吸毒', '贩毒', '制毒', '运毒', '藏毒', '毒贩',
        '海洛因', '白粉', '四号', '杜冷丁', '吗啡', '鸦片', '大烟',
        '冰毒', '冰', '甲基苯丙胺', '安非他明', '麻古', '摇头丸',
        '可卡因', '古柯碱', '快克', '雪花', '白雪', '粉末',
        '大麻', '草', '叶子', '印度大麻', '大麻烟', '大麻油',
        'K粉', '氯胺酮', '凯他敏', '特殊K', '维他命K',
        'LSD', '迷幻药', '致幻剂', '蘑菇', '仙人掌', '天使粉',
        '神仙水', '液体摇头丸', 'GHB', '迷奸药', '听话水',
        '止咳水', '联邦止咳露', '可待因', '泰勒宁', '曲马多',
        '麻醉品', '精神药品', '管制药品', '兴奋剂', '镇静剂',
        '注射器', '针头', '静脉注射', '烫吸', '溜冰', '飞叶子',
        '戒毒', '戒毒所', '美沙酮', '替代疗法', '脱毒',
        '毒瘾', '瘾君子', '毒虫', '道友', '嗨', '飞', '爽'
    ],

    // 暴力恐怖
    violence: [
        '恐怖主义', '恐怖分子', '恐怖袭击', '恐怖活动', 'ISIS', 'IS',
        '基地组织', '拉登', '塔利班', '圣战', '殉教', '自杀式',
        '爆炸', '炸弹', '爆炸物', '炸药', 'TNT', '雷管', '引爆',
        '暗杀', '刺杀', '谋杀', '杀害', '杀人', '杀死', '弄死',
        '屠杀', '大屠杀', '灭绝', '种族清洗', '血洗', '血腥',
        '暴力', '武力', '打砸抢', '破坏', '砸毁', '烧毁',
        '仇杀', '报复', '复仇', '报复社会', '同归于尽', '鱼死网破',
        '极端', '激进', '原教旨', '狂热', '疯狂', '丧心病狂',
        '邪教', '膜拜', '洗脑', '精神控制', '末日', '世界末日',
        '枪支', '手枪', '步枪', '冲锋枪', '机关枪', '狙击枪',
        '子弹', '弹药', '火药', '军火', '武器', '兵器',
        '刀具', '管制刀具', '匕首', '砍刀', '菜刀', '剑',
        '绑架', '劫持', '人质', '勒索', '敲诈', '威胁',
        '自残', '自杀', '自杀式袭击', '人体炸弹', '跳楼', '割腕'
    ],

    // 诈骗相关
    fraud: [
        '诈骗', '骗钱', '骗局', '陷阱', '套路', '忽悠', '欺骗',
        '传销', '直销', '多层次销售', '金字塔', '老鼠会', '庞氏骗局',
        '非法集资', '集资诈骗', '资金盘', '拆东墙补西墙', '击鼓传花',
        '高利贷', '套路贷', '校园贷', '裸贷', '网贷', '现金贷',
        '放贷', '催收', '讨债', '暴力催收', '软暴力', '威胁恐吓',
        '洗钱', '漂白', '黑钱', '赃款', '转账', '代收代付',
        '虚假投资', '投资陷阱', '理财骗局', '炒股骗局', '外汇骗局',
        '数字货币', '虚拟货币', '比特币', '以太坊', '区块链', 'ICO',
        '微商', '三级分销', '拉人头', '发展下线', '分红', '返利',
        '刷单', '刷信誉', '刷好评', '刷流量', '刷粉', '点赞',
        '兼职', '打字员', '网络兼职', '在家赚钱', '轻松赚钱',
        '免费领取', '限时优惠', '秒杀', '抢购', '特价', '清仓',
        '中奖', '幸运用户', '恭喜您', '获得', '奖品', '大奖',
        '充值', '激活', '解冻', '保证金', '手续费', '认证费',
        '身份证', '银行卡', '信用卡', '密码', '验证码', '支付宝'
    ],

    // 违法药品
    illegal_drugs: [
        '假药', '劣药', '三无产品', '黑诊所', '游医', '庸医',
        '毒药', '迷药', '蒙汗药', '安眠药', '麻醉药', '精神药',
        '春药', '催情药', '兴奋剂', '性药', '壮阳药', '延时药',
        '堕胎药', '打胎药', '流产药', '避孕药', '紧急避孕',
        '减肥药', '瘦身药', '泻药', '利尿剂', '脱水剂',
        '特效药', '神药', '灵丹妙药', '包治百病', '药到病除',
        '秘方', '祖传秘方', '偏方', '土方', '验方', '民间偏方',
        '保健品', '营养品', '补品', '滋补品', '养生产品',
        '治疗', '根治', '彻底治愈', '永不复发', '立竿见影',
        '医院', '诊所', '卫生所', '医疗机构', '无证行医',
        '处方药', '抗生素', '激素', '麻醉剂', '管制药品'
    ],

    // 其他违法
    other_illegal: [
        '走私', '偷渡', '非法入境', '非法出境', '蛇头', '人贩子',
        '人口贩卖', '拐卖人口', '拐卖妇女', '拐卖儿童', '收养',
        '器官买卖', '器官移植', '活体器官', '肾脏', '肝脏',
        '假币', '伪币', '假钞', '美元', '欧元', '人民币',
        '洗钱', '漂白', '地下钱庄', '外汇', '换汇', '汇率',
        '逃税', '偷税', '漏税', '避税', '税务', '发票',
        '行贿', '受贿', '贪污', '挪用', '侵占', '职务犯罪',
        '腐败', '贪腐', '权钱交易', '权色交易', '官商勾结',
        '黑社会', '黑恶势力', '保护伞', '黑老大', '老大',
        '高仿', '山寨', '仿冒', '盗版', '侵权', '假冒',
        '偷盗', '盗窃', '扒手', '小偷', '贼', '抢劫',
        '敲诈', '勒索', '绑架', '撕票', '要挟', '威胁'
    ],

    // 网络安全
    cybercrime: [
        '黑客', '骇客', '网络攻击', '入侵', '破解', 'DDoS',
        '病毒', '木马', '蠕虫', '后门', '漏洞', '0day',
        '钓鱼', '钓鱼网站', '钓鱼邮件', '社会工程', '欺骗',
        '账号', '密码', '破解密码', '撞库', '暴力破解',
        '个人信息', '隐私', '泄露', '窃取', '盗用', '冒用',
        '信用卡', '网银', '支付', '转账', '盗刷', '套现',
        'VPN', '翻墙', '代理', '匿名', 'TOR', '暗网',
        '比特币', '数字货币', '挖矿', '矿机', '算力',
        '论坛', '网站', '域名', '服务器', '数据库', 'SQL注入'
    ]
};

/**
 * 所有敏感词的扁平化数组
 */
const ALL_SENSITIVE_WORDS: string[] = Object.values(SENSITIVE_WORDS).flat();

/**
 * AC自动机算法实现敏感词检测
 */
class AhoCorasick {
    private root: TrieNode;
    private failure: Map<TrieNode, TrieNode>;
    private output: Map<TrieNode, string[]>;

    constructor() {
        this.root = {};
        this.failure = new Map();
        this.output = new Map();
    }

    /**
     * 构建Trie树
     * @param patterns 敏感词数组
     */
    buildTrie(patterns: string[]): void {
        // 构建Trie树
        for (const pattern of patterns) {
            let node = this.root;
            for (const char of pattern) {
                if (!node[char]) {
                    node[char] = {};
                }
                node = node[char] as TrieNode;
            }
            node.isEnd = true;
            node.pattern = pattern;
        }

        // 构建失败函数
        this.buildFailure();
    }

    /**
     * 构建失败函数
     */
    private buildFailure(): void {
        const queue: TrieNode[] = [];

        // 初始化根节点的子节点
        for (const char in this.root) {
            if (char !== 'isEnd' && char !== 'pattern') {
                this.failure.set(this.root[char] as TrieNode, this.root);
                queue.push(this.root[char] as TrieNode);
            }
        }

        while (queue.length > 0) {
            const current = queue.shift()!;

            for (const char in current) {
                if (char === 'isEnd' || char === 'pattern') continue;

                const child = current[char] as TrieNode;
                queue.push(child);

                let failure = this.failure.get(current);
                while (failure && !failure[char]) {
                    failure = this.failure.get(failure);
                }

                this.failure.set(child, failure && failure[char] ? failure[char] as TrieNode : this.root);

                // 构建输出函数
                const failureNode = this.failure.get(child)!;
                if (failureNode.isEnd) {
                    if (!this.output.has(child)) {
                        this.output.set(child, []);
                    }
                    this.output.get(child)!.push(failureNode.pattern!);
                }

                if (this.output.has(failureNode)) {
                    if (!this.output.has(child)) {
                        this.output.set(child, []);
                    }
                    this.output.get(child)!.push(...this.output.get(failureNode)!);
                }
            }
        }
    }

    /**
     * 搜索敏感词
     * @param text 要检测的文本
     * @returns 找到的敏感词信息
     */
    search(text: string): MatchResult[] {
        const results: MatchResult[] = [];
        let current: TrieNode | undefined = this.root;

        for (let i = 0; i < text.length; i++) {
            const char = text[i];

            while (current && !current[char]) {
                current = this.failure.get(current);
            }

            if (!current) {
                current = this.root;
                continue;
            }

            current = current[char] as TrieNode;

            if (current.isEnd) {
                results.push({
                    word: current.pattern!,
                    index: i - current.pattern!.length + 1,
                    length: current.pattern!.length
                });
            }

            if (this.output.has(current)) {
                for (const pattern of this.output.get(current)!) {
                    results.push({
                        word: pattern,
                        index: i - pattern.length + 1,
                        length: pattern.length
                    });
                }
            }
        }

        return results;
    }
}

// 创建全局AC自动机实例
const acMachine = new AhoCorasick();
acMachine.buildTrie(ALL_SENSITIVE_WORDS);

/**
 * 敏感词检测主类
 */
class SensitiveWordDetector {
    private acMachine: AhoCorasick;

    constructor() {
        this.acMachine = acMachine;
    }

    /**
     * 检测文本中的敏感词
     * @param text 要检测的文本
     * @returns 检测结果
     */
    detect(text: string): DetectionResult {
        if (!text || typeof text !== 'string') {
            return {
                isValid: false,
                message: '内容不能为空',
                sensitiveWords: [],
                filteredText: ''
            };
        }

        // 检查是否主要是表情符号或图片内容
        const isEmojiOrImageContent = this.isEmojiOrImageContent(text);

        if (isEmojiOrImageContent) {
            // 表情内容直接通过，不做任何限制
            return {
                isValid: true,
                message: '表情内容检查通过',
                sensitiveWords: [],
                filteredText: text
            };
        }

        // 预处理文本：移除HTML标签、转小写、去除特殊字符
        const cleanText = this.preprocessText(text);

        if (!cleanText.trim()) {
            return {
                isValid: false,
                message: '内容不能为空',
                sensitiveWords: [],
                filteredText: ''
            };
        }

        // 使用AC自动机检测敏感词
        const foundWords = this.acMachine.search(cleanText);

        // 去重
        const uniqueWords = [...new Set(foundWords.map(item => item.word))];

        if (uniqueWords.length > 0) {
            return {
                isValid: false,
                message: `检测到违规词汇：${uniqueWords.slice(0, 3).join('、')}${uniqueWords.length > 3 ? '等' : ''}`,
                sensitiveWords: uniqueWords,
                filteredText: this.filterSensitiveWords(text, foundWords)
            };
        }

        // 其他检查
        const otherChecks = this.performOtherChecks(cleanText);
        if (!otherChecks.isValid) {
            return otherChecks;
        }

        return {
            isValid: true,
            message: '内容检查通过',
            sensitiveWords: [],
            filteredText: text
        };
    }

    /**
     * 预处理文本
     * @param text 原始文本
     * @returns 处理后的文本
     */
    private preprocessText(text: string): string {
        // 检查是否主要是表情符号或图片内容
        const isEmojiOrImageContent = this.isEmojiOrImageContent(text);

        if (isEmojiOrImageContent) {
            // 如果主要是表情或图片内容，只移除HTML标签但保留表情符号
            return text.replace(/<(?!img)[^>]*>/g, ''); // 保留img标签，移除其他HTML标签
        }

        return text
            .replace(/<(?!img)[^>]*>/g, '') // 保留img标签，移除其他HTML标签
            .replace(/\s+/g, '') // 移除空格
            .toLowerCase() // 转小写
            .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\u1F600-\u1F64F\u1F300-\u1F5FF\u1F680-\u1F6FF\u1F1E0-\u1F1FF\u2600-\u26FF\u2700-\u27BF]/g, ''); // 保留中英文数字和常见表情符号
    }

    /**
     * 检查内容是否主要是表情符号或图片
     * @param text 文本内容
     * @returns 是否主要是表情内容
     */
    private isEmojiOrImageContent(text: string): boolean {
        // 检查是否包含img标签（微信表情图片）
        if (/<img[^>]*>/i.test(text)) {
            return true;
        }

        // 检查是否包含表情符号
        const textWithoutHtml = text.replace(/<[^>]*>/g, '');
        const emojiRegex = /[\u1F600-\u1F64F\u1F300-\u1F5FF\u1F680-\u1F6FF\u1F1E0-\u1F1FF\u2600-\u26FF\u2700-\u27BF\u1F900-\u1F9FF\u1FA70-\u1FAFF]/g;
        const emojis = textWithoutHtml.match(emojiRegex) || [];

        // 只要包含表情符号就认为是表情内容，不再限制文字数量
        return emojis.length > 0;
    }

    /**
     * 过滤敏感词
     * @param text 原始文本
     * @param foundWords 找到的敏感词
     * @returns 过滤后的文本
     */
    private filterSensitiveWords(text: string, foundWords: MatchResult[]): string {
        let filtered = text;
        for (const item of foundWords) {
            const replacement = '*'.repeat(item.word.length);
            filtered = filtered.replace(new RegExp(item.word, 'gi'), replacement);
        }
        return filtered;
    }

    /**
     * 执行其他检查
     * @param text 文本内容
     * @returns 检查结果
     */
    private performOtherChecks(text: string): DetectionResult {
        // 长度检查
        if (text.length > 500) {
            return {
                isValid: false,
                message: '内容过长，请控制在500字以内',
                sensitiveWords: [],
                filteredText: text.substring(0, 500)
            };
        }

        // 对于表情内容，跳过重复字符检查
        const isEmojiContent = this.isEmojiOrImageContent(text);
        if (!isEmojiContent) {
            // 重复字符检查（仅对非表情内容进行）
            if (/(.)\1{4,}/.test(text)) {
                return {
                    isValid: false,
                    message: '请避免使用过多重复字符',
                    sensitiveWords: [],
                    filteredText: text
                };
            }
        }

        // 垃圾内容检查
        if (/^[0-9\W]+$/.test(text) && text.length > 10) {
            return {
                isValid: false,
                message: '请输入有意义的内容',
                sensitiveWords: [],
                filteredText: text
            };
        }

        // 可疑链接检查
        if (/(\.tk|\.ml|\.ga|\.cf|bit\.ly|tinyurl|短链|点击领取|免费领取|限时优惠)/i.test(text)) {
            return {
                isValid: false,
                message: '不得包含可疑链接',
                sensitiveWords: [],
                filteredText: text
            };
        }

        return {
            isValid: true,
            message: '检查通过',
            sensitiveWords: [],
            filteredText: text
        };
    }

    /**
     * 获取敏感词库统计信息
     * @returns 统计信息
     */
    getStatistics(): Statistics {
        return {
            totalWords: ALL_SENSITIVE_WORDS.length,
            categories: Object.keys(SENSITIVE_WORDS).reduce((acc, key) => {
                acc[key] = SENSITIVE_WORDS[key as keyof SensitiveWordCategories].length;
                return acc;
            }, {} as Record<string, number>)
        };
    }
}

// 导出
export default SensitiveWordDetector;
export { SENSITIVE_WORDS, ALL_SENSITIVE_WORDS };
export type { DetectionResult, MatchResult, Statistics, SensitiveWordCategories }; 