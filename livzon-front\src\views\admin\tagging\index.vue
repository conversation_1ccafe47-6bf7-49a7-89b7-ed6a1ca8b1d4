<template>
  <div class="app-container tagging">
    <el-row :gutter="10">

      <!-- 上传对话框 -->
      <el-dialog :title="title" v-model="open" :width="isMobile ? '90%' : '50%'" append-to-body
        :close-on-click-modal="false" :class="{ 'mobile-dialog': isMobile }">
        <div class="dialog-content-scroll">
          <el-form ref="fileRef" :model="uploadForm" :rules="rules" label-width="120px" label-position="right">
            <el-row>

              <el-col :span="isMobile ? 24 : 8">
                <el-form-item label="试剂厂家" prop="reagentManufacturer">
                  <el-select v-model="uploadForm.reagentManufacturer" placeholder="请选择" clearable style="width: 100%"
                    @change="handleReagentChange">
                    <el-option v-for="dict in reagent_manufacturer" :key="dict.dictValue" :label="dict.dictLabel"
                      :value="dict.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="isMobile ? 24 : 8">
                <el-form-item label="显微镜型号" prop="imagingDevice">
                  <el-select v-model="uploadForm.imagingDevice" placeholder="请选择" clearable style="width: 100%">
                    <el-option v-for="dict in filteredImagingDevice" :key="dict.dictValue" :label="dict.dictLabel"
                      :value="dict.dictLabel" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="isMobile ? 24 : 8">
                <el-form-item label="物镜" prop="objective" label-width="85px">
                  <el-select v-model="uploadForm.objective" placeholder="请选择" clearable
                    style="width: 100%; max-width: 180px;">
                    <el-option v-for="dict in objective" :key="dict.dictValue" :label="dict.dictLabel"
                      :value="dict.dictLabel" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div v-if="open">
              <upload ref="uploadComponent" :reagentManufacturer="uploadForm.reagentManufacturer"
                :imagingDevice="uploadForm.imagingDevice" :objective="uploadForm.objective"
                @start-upload="handleUploadRequest" @upload-complete="getList1" v-model:isUploading="isUploading">
              </upload>
            </div>
          </el-form>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="confirm()" type="primary" :disabled="isUploading">{{ isUploading ? '正在上传中...' : '关 闭'
            }}</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 分配对话框 -->
      <el-dialog :title="title" v-model="openDistribution" width="25%" append-to-body>
        <el-form ref="distributionRef" :model="formDistribution" :rules="rules" label-width="100px">
          <el-col :span="17">
            <el-form-item label="复核人员" prop="taggingPeopleId">
              <el-select v-model="formDistribution.taggingPeopleId" placeholder="请选复核人员" style="width: 100%;">
                <el-option v-for="downReviewers in downReviewersSelect" :key="downReviewers.userId"
                  :label="downReviewers.userName" :value="downReviewers.userId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitDistribution">确 定</el-button>
            <el-button @click="openDistribution = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <el-col :xs="24" :sm="24" :md="14" :lg="14">
        <el-card class="update-log" v-loading="loading">
          <template #header>
            <div class="proportion-header">
              <el-button type="primary" link :icon="showProportionImage ? 'Minus' : 'Plus'"
                @click="toggleProportionImage" class="proportion-toggle-btn no-bold">
                {{ showProportionImage ? '滴度示意图' : '滴度示意图' }}
              </el-button>
              <div v-show="showProportionImage">
                <img :src="proportion" class="proportion" />
                <div class="proportionSpan">
                  <template v-if="isTiter">
                    <!-- 倍比滴度 -->
                    <span>1:80</span>
                    <span>1:160</span>
                    <span>1:320</span>
                    <span>1:640</span>
                    <span>1:1280</span>
                  </template>
                  <template v-else>
                    <!-- √10滴度 -->
                    <span>1:100</span>
                    <span>1:320</span>
                    <span>1:1000</span>
                    <span>1:3200</span>
                    <span>1:10000</span>
                  </template>
                </div>
              </div>
            </div>
          </template>
          <div class="main-toolbar" style="margin-bottom: 10px;">
            <div class="image-info"></div>
            <div :class="isMobile ? 'mobile-btn-group' : ''" class="toolbar-btns">
              <el-popover v-if="qrcodeShow" placement="bottom" :width="200" trigger="hover">
                <template #reference>
                  <div style="display: flex; align-items: center; gap: 5px;">
                    <span style="color: #409EFF; font-weight: bold;">分享二维码</span>
                    <svg t="1723703603333" class="icon" viewBox="0 0 1024 1024" version="1.1"
                      xmlns="http://www.w3.org/2000/svg" p-id="15871" width="32" height="32">
                      <path
                        d="M480 544H368v-64h112V368h64v288h-64V544z m368 304V592h64v320H592v-64h256zM656 656v144h-64V592h208v64H656zM176 176v192h192V176H176z m-64-64h320v320H112V112z m544 64v192h192V176H656z m-64-64h320v320H592V112zM112 480h160v64H112v-64z m640 0h160v64H752v-64zM544 112v160h-64V112h64z m0 640v160h-64V752h64z m-368-96v192h192V656H176z m-64-64h320v320H112V592z m112-368h96v96h-96v-96z m0 480h96v96h-96v-96z m480-480h96v96h-96v-96z"
                        fill="#5090F1" p-id="15872"></path>
                    </svg>
                  </div>
                </template>
                <vue-qr :text="config" :size="170" :margin="5" ref="Qrcode" class="vue-qr-img">
                </vue-qr>
              </el-popover>
              <el-button type="primary" @click="adjustBrightness(true)">增加亮度</el-button>
              <el-button type="primary" @click="adjustBrightness(false)" style="margin-left: 2px;">还原亮度</el-button>
              <el-popover v-model:visible="showTip" placement="bottom" :width="200" trigger="manual" effect="light"
                popper-class="image-tip-popover">
                <template #reference>
                  <el-button type="primary" @click="handleResetImage" style="margin-left: 2px;">
                    还原显示位置
                    <el-icon class="mouse-icon">
                      <Mouse />
                    </el-icon>
                  </el-button>
                </template>
                <div>支持鼠标滚轮缩放、按住左键拖动</div>
              </el-popover>
            </div>
          </div>
          <div class="controls">
            <canvas ref="imageCanvasRef" :width="`${width}`" :height="showProportionImage ? 500 : 635" class="highlight"
              :style="canvasStyle" :key="newTime"></canvas>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="5" :lg="5">
        <!-- style="background-color:darkgray;" -->
        <el-card class="update-log">
          <!-- 搜索栏 -->
          <template #header>
            <div class="search-wrapper">
              <el-form :model="queryParams" ref="queryFormRef" :inline="true" v-show="showSearch"
                :label-width="isMobile ? 'auto' : '80px'" label-position="left">
                <div class="search-form">
                  <el-form-item label="文件名称" prop="fileName" class="search-item">
                    <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" maxlength="256" show-word-limit
                      clearable @keyup.enter.native="handleQuery()" @change="handleQuery()" style="width: 100%" />
                  </el-form-item>
                  <el-form-item v-if="!oneCheck && !twoCheck" label="状态" prop="fileStatus" class="search-item">
                    <el-select v-model="queryParams.fileStatus" placeholder="请选择状态" clearable @change="handleQuery()"
                      style="width: 100%" multiple collapse-tags collapse-tags-tooltip>
                      <el-option v-for="dict in fileStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                    </el-select>
                  </el-form-item>
                  <el-form-item v-if="oneCheck" label="状态" prop="oneFileStatus" class="search-item">
                    <el-select v-model="queryParams.oneFileStatus" placeholder="请选择状态" clearable @change="handleQuery()"
                      style="width: 100%" multiple collapse-tags collapse-tags-tooltip>
                      <el-option v-for="dict in oneFileStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                    </el-select>
                  </el-form-item>
                  <el-form-item v-if="twoCheck" label="状态" prop="twoFileStatus" class="search-item">
                    <el-select v-model="queryParams.twoFileStatus" placeholder="请选择状态" clearable @change="handleQuery()"
                      style="width: 100%" multiple collapse-tags collapse-tags-tooltip>
                      <el-option v-for="dict in twoFileStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="开始时间" class="search-item">
                    <el-date-picker v-model="queryParams.startTime" type="datetime" placeholder="开始日期"
                      format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" style="width: 100%" />
                  </el-form-item>
                  <el-form-item label="结束时间" class="search-item">
                    <el-date-picker v-model="queryParams.endTime" type="datetime" placeholder="结束日期"
                      format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" style="width: 100%"
                      :disabledDate="(time: Date) => time.getTime() > Date.now()" />
                  </el-form-item>
                </div>
                <div class="search-buttons">
                  <el-button type="primary" @click="handleQuery()">搜索 </el-button>
                  <el-button @click="resetQuery()">重置</el-button>
                  <el-popover placement="bottom" :width="340" trigger="click" popper-class="calendar-popover-custom"
                    @show="loadCalendarData">
                    <template #reference>
                      <el-button type="primary" icon="Calendar">数据日历</el-button>
                    </template>
                    <div class="calendar-header">
                      <div class="header-title">
                        <el-icon>
                          <Calendar />
                        </el-icon>
                        <span>✓ 数据上传日历</span>
                      </div>
                      <div class="header-actions">
                        <el-tooltip content="✓ 日期表示当天有上传数据" placement="top">
                          <el-icon class="info-icon">
                            <InfoFilled />
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                    <div class="compact-data-calendar has-data-highlighting">
                      <el-calendar v-model="calendarValue" @calendar-change="handleCalendarChange" ref="calendarRef">
                        <template #date-cell="{ data }">
                          <div class="compact-calendar-cell" :class="{ 'has-data': hasDataOnDate(data.day) }"
                            @click="handleDateClick(data.day)" :title="hasDataOnDate(data.day) ? '该日期有数据' : data.day">
                            <div class="date-text">
                              {{ data.day.split('-').pop().replace(/^0/, '') }}
                            </div>
                          </div>
                        </template>
                      </el-calendar>
                      <!-- 数据统计信息 -->
                      <div style="margin-top: 8px; font-size: 12px; color: #666; text-align: center;">
                        <el-tag v-if="datesWithData.length > 0" type="primary" size="small">
                          📊 {{ datesWithData.length }} 天有数据
                        </el-tag>
                        <el-tag v-else type="info" size="small">
                          📅 暂无数据
                        </el-tag>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </el-form>
            </div>
          </template>

          <!-- 按钮 -->
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
              <el-button type="primary" size="default" @click="handleUpload" v-hasPermi="['livzon:tagging:upload']">
                <el-icon class="el-icon--right">
                  <Upload />
                </el-icon>上 传
              </el-button>
            </div>
            <div>
              <el-button-group>
                <el-button @click="showSearch = !showSearch">
                  <el-icon class="search-icon">
                    <Search />
                    <div v-if="!showSearch" class="search-disabled-line"></div>
                  </el-icon>
                </el-button>
                <el-button @click="getList()">
                  <el-icon>
                    <Refresh />
                  </el-icon>
                </el-button>
              </el-button-group>
            </div>
          </div>
          <div class="button-group">
            <el-button type="primary" icon="circle-check" size="small" @click="handleRecognition"
              v-hasPermi="['livzon:tagging:recognition']">AI 识 别</el-button>
            <!-- 图片下载按钮：显示选中的条数 -->
            <el-button type="primary" icon="download" size="small" @click="handleDownload"
              v-hasPermi="['livzon:tagging:imageDownload']" :disabled="loadingDownload">
              {{ loadingDownload ? `下载中 (${downloadedCount}/${totalCount})` : `图片下载${ids && ids.length > 0 ? `
              (已选${ids.length}条)`
                : ''}` }}
            </el-button>
            <el-button type="primary" icon="download" size="small" @click="handleExport"
              v-hasPermi="['livzon:tagging:export']">xlsx导出</el-button>
            <el-button type="primary" icon="User" size="small" @click="handleDistribution"
              v-hasPermi="['livzon:tagging:distribution']">分配</el-button>
            <el-button type="danger" icon="refresh" size="small" @click="handleClearCache"
              v-hasPermi="['system:config:remove']">清理缓存</el-button>
            <el-button type="danger" icon="delete" size="small" @click="handleDelete"
              v-hasPermi="['livzon:tagging:remove']">删除</el-button>
          </div>

          <!-- 表单 -->
          <!--
            动态设置表格高度：
            1. 监听showSearch，动态调整表格高度。
            2. 通过tableHeight变量控制el-table高度。
            3. 具体高度可根据实际UI微调。
          -->
          <el-table border class="table-container" stripe ref="pageTableRef" :height="tableHeight" v-loading="loading"
            highlight-current-row :data="fileList" @selection-change="handleSelectionChange" @row-click="handleRowClick"
            @sort-change="handleSortChange" :virtual-scroll="true" :item-size="50" :buffer="10">
            <el-table-column type="selection" max-width="100px" align="center" fixed />
            <el-table-column label="序号" type="index" align="center" width="55px">
              <template #default="scope">
                <!-- prettier-ignore -->
                <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column label="操作" align="center" max-width="150px" v-hasPermi="['livzon:markreview:operate']"
              class-name="small-padding fixed-width">
              <template #default="scope">
                <el-link class="table_link_btn" :underline="false" size="small" type="primary" icon="circle-check"
                  @click.stop="handleRowDblClick(scope.row)">
                  <span class="table_link_text">详情</span></el-link>
              </template>
            </el-table-column>
            <el-table-column label="文件名称" align="center" width="135px" prop="fileName" :show-overflow-tooltip="true"
              sortable="custom" />
            <el-table-column v-if="!oneCheck && !twoCheck" label="状态" align="center" width="110px" prop="fileStatus"
              :show-overflow-tooltip="true">
              <!-- 直接用span显示状态，AI_FAILED为红色，其它为黑色 -->
              <template #default="scope">
                <span :style="{ color: getFileStatus(scope.row.fileStatus) }">
                  {{ selectDictLabel(fileStatusOptions, scope.row.fileStatus) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column v-if="oneCheck" label="状态" align="center" max-width="110px" prop="oneFileStatus"
              :show-overflow-tooltip="true">
              <!-- 直接用span显示状态 -->
              <template #default="scope">
                <span :style="{ color: getFileStatus(scope.row.fileStatus) }">
                  {{ selectDictLabel(oneFileStatusOptions, scope.row.oneFileStatus) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column v-if="twoCheck" label="状态" align="center" max-width="110px" prop="twoFileStatus"
              :show-overflow-tooltip="true">
              <!-- 直接用span显示状态 -->
              <template #default="scope">
                <span :style="{ color: getFileStatus(scope.row.fileStatus) }">
                  {{ selectDictLabel(twoFileStatusOptions, scope.row.twoFileStatus) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="上传时间" align="center" width="160px" prop="createDate" sortable="custom">
              <template #default="scope">
                <span>{{ scope.row.createDate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="上传人员" max-width="150px" align="center" prop="createUserName"
              v-hasPermi="['livzon:markreview:uploadPersonnel']" :show-overflow-tooltip="true" />
            <el-table-column label="复核人员" max-width="150px" align="center" prop="taggingPeopleName"
              v-hasPermi="['livzon:markreview:recognition']" :show-overflow-tooltip="true" />
          </el-table>
          <div class="demo-pagination-block">
            <!-- 分页 -->
            <el-pagination v-model:current-page="queryParams.pageNum" v-model:page-size="queryParams.pageSize"
              :page-sizes="[10, 20, 50, 100, 200, 500]" layout="sizes, prev, pager, next, jumper, total"
              :background="true" :total="total" @size-change="handleSizeChange" @current-change="getList()" small
              :pager-count="3" />
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="5" :lg="5">
        <!-- AI预测 -->
        <div class="boby">
          <el-card class="update-log">
            <div class="clearfix">
              <span class="ai-prediction-header">
                <span class="header-text">AI 预测</span>
              </span>
              <span class="header-text prediction-probability">预测概率</span>
            </div>
            <div class="aiDiv">
              <el-form ref="aiFormRef" label-width="60px" :model="aiForm" :rules="rules">
                <el-row :gutter="1">
                  <el-col :span="17" v-if="aiForm.predictType">
                    <el-form-item label="定性" prop="predictType">
                      <el-input class="negative" v-if="aiForm.predictType === 'negative'" value="阴性" disabled />
                      <el-input class="positive" v-if="aiForm.predictType === 'positive'" value="阳性" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="7" v-if="aiForm.type">
                    <el-form-item label=" " label-width="0">
                      <el-input prop="type" :value="formatNumber(Number(aiForm.type))" disabled />
                    </el-form-item>
                  </el-col>
                  <!-- 分割线：定性与第一组核型+滴度之间 -->
                  <el-col :span="24"
                    v-if="aiForm.aiPredictKaryotypeDetailsVOS && aiForm.aiPredictKaryotypeDetailsVOS.length">
                    <el-divider class="domain-divider" />
                  </el-col>
                  <template v-for="(items, index) in aiForm.aiPredictKaryotypeDetailsVOS">
                    <el-col :span="17">
                      <el-form-item :label="`核型 ${index + 1}`" prop="predictKaryotype">
                        <el-input v-model="items.predictKaryotype" disabled />
                      </el-form-item>
                    </el-col>
                    <el-col :span="7">
                      <el-form-item label=" " label-width="0">
                        <el-input prop="karyotype" :value="formatNumber(Number(items.karyotype))" disabled />
                      </el-form-item>
                    </el-col>
                    <el-col :span="17">
                      <el-form-item :label="`滴度 ${index + 1}`" prop="predictTiters">
                        <el-input v-model="items.predictTiters" disabled />
                      </el-form-item>
                    </el-col>
                    <el-col :span="7">
                      <el-form-item label=" " label-width="0">
                        <el-input prop="titers" :value="formatNumber(Number(items.titers))" disabled />
                      </el-form-item>
                    </el-col>
                    <!-- 分割线：每组后（不是最后一组） -->
                    <el-col :span="24" v-if="index < aiForm.aiPredictKaryotypeDetailsVOS.length - 1">
                      <el-divider class="domain-divider" />
                    </el-col>
                  </template>
                </el-row>
              </el-form>
            </div>
          </el-card>
          <!-- 人工标注 -->

          <el-card class="update-log">
            <div class="control-bar">
              <div class="filelist-guide">
                <span class="guide-text">
                  <b>↑</b>/<b>↓</b> 快捷键切换条目，提升工作效率
                </span>
              </div>
            </div>
            <div class="image-info">{{ imageInfo }}</div>
            <div>
              <el-tabs class="demo-tabs" v-model="activeName" @tab-click="onChangeChecked">
                <el-tab-pane label="人工标注" name="one">
                  <!-- 标注 -->
                  <el-form ref="formItemRef" style="max-width: 600px" :model="dynamicValidateForm" class="demo-dynamic">
                    <!-- 只显示AI结果相同 -->
                    <div>
                      <template v-if="resultShow.showSameOnly">
                        <div style="color: #409EFF; font-weight: bold;">AI结果相同</div>
                      </template>
                      <template v-else>
                        <el-col v-show="oneQualitativeShow">
                          <el-form-item label="定性" prop="selectedType" :rules="{
                            required: true,
                            message: '请选择阴阳性',
                            trigger: 'change'
                          }">
                            <div style="display: flex; align-items: center; gap: 10px;">
                              <el-select :disabled="oneDisabled" v-model="dynamicValidateForm.selectedType"
                                placeholder="请选择" style="width: 95px"
                                @change="(val: String) => handleChange(val, 'one')" class="type-select" :class="{
                                  'negative-option': dynamicValidateForm.selectedType === NEGATIVE,
                                  'positive-option': dynamicValidateForm.selectedType === POSITIVE
                                }" clearable>
                                <el-option label="阴性" :value="NEGATIVE" />
                                <el-option label="阳性" :value="POSITIVE" />
                                <el-option label="可疑" :value="SUOICIOUS" />
                              </el-select>
                              <div v-if="dynamicValidateForm.domains.length > 0 || !resultShow">
                                <span style="color: #67c23a;" v-show="resultShow">AI结果相同</span>
                                <span style="color: #f56c6c;" v-show="!resultShow">AI结果不同</span>
                              </div>
                            </div>
                          </el-form-item>
                        </el-col>


                        <el-col v-for="(domain, index) in dynamicValidateForm.domains">
                          <el-form-item :karyotype="domain.karyotype" :label="'核型' + `${index + 1}`"
                            :prop="'domains.' + index + '.karyotype'" :rules="{
                              required: true,
                              message: '核型不能为空',
                              trigger: 'blur',
                            }">
                            <el-tree-select :disabled="oneDisabled" v-model="domain.karyotype" style="width: 100%"
                              clearable :data="karyotypeOptions" placeholder="选择核型" value-key="karyotypeId"
                              :props="{ value: 'karyotypeId', label: 'name', children: 'child' }" check-strictly
                              filterable :render-after-expand="false" :default-expand-all="true"
                              @node-click="() => handleDomainsChange(index)" :ref="`treeDomains_${index}`"
                              @visible-change="highlightFirstLevelWithChildren" />
                          </el-form-item>
                          <el-form-item :titer="domain.titer" :label="'滴度' + `${index + 1}`"
                            :prop="'domains.' + index + '.titer'" :rules="{
                              required: true,
                              message: '滴度不能为空',
                              trigger: 'blur',
                            }">
                            <div style="display: flex; align-items: center;">
                              <el-select :disabled="oneDisabled" v-model="domain.titer" placeholder="选择滴度" clearable
                                style="flex: 1;" filterable :render-after-expand="false" :ref="`titerDomains_${index}`">
                                <el-option v-for="dict in titerStatusOptions" :key="dict.dictCode"
                                  :label="dict.dictLabel" :value="dict.dictValue" />
                              </el-select>
                              <el-button v-if="!oneDisabled" type="danger" plain icon="delete"
                                @click.prevent="removeDomain(domain)" style="margin-left: 8px;" />
                            </div>
                          </el-form-item>
                          <!-- 分割线：每组核型+滴度后（不是最后一组） -->
                          <el-divider v-if="index < dynamicValidateForm.domains.length - 1" class="domain-divider" />
                        </el-col>
                        <el-col>
                          <el-form-item v-show="oneQualitativeShow" label="备注" prop="remark">
                            <el-input :disabled="oneDisabled" v-model="dynamicValidateForm.remark" maxlength="512"
                              :autosize="{ minRows: 4 }" placeholder="请输入备注" show-word-limit type="textarea" />
                          </el-form-item>
                        </el-col>
                      </template>
                    </div>
                  </el-form>
                  <div v-if="!twoCheck && !oneCheck" class="dialog-footer">
                    <el-button v-show="addShow" type="primary" size="small" @click="addDomain('one')">{{ oneTitle
                      }}</el-button>
                    <el-button v-show="oneCopyShow" type="primary" @click="resultCopy(formItemRef, 'one')"
                      v-hasPermi="['livzon:tagging:copyAi']">认可AI结果</el-button>
                    <el-button v-show="buttonShow" type="primary" @click="submitForm(formItemRef, 'one')"
                      v-hasPermi="['livzon:tagging:edit']">确认</el-button>
                    <el-button v-show="buttonShow" @click="resetForm(formItemRef)">取消</el-button>
                  </div>
                </el-tab-pane>
                <!-- 一级复核 -->
                <el-tab-pane v-if="twoCheck || oneCheck" label="一级复核" name="two">

                  <el-form ref="formItemRef" style="max-width: 600px" :model="dynamicValidateForm" class="demo-dynamic">
                    <el-col v-show="twoQualitativeShow">
                      <el-form-item label="定性" prop="selectedOneType" :rules="{
                        required: true,
                        message: '请选择阴阳性',
                        trigger: 'change'
                      }">
                        <div style="display: flex; align-items: center; gap: 10px;">
                          <el-select :disabled="twoDisabled" v-model="dynamicValidateForm.selectedOneType"
                            placeholder="请选择" style="width: 95px" @change="(val: String) => handleChange(val, 'two')"
                            class="type-select" :class="{
                              'negative-option': dynamicValidateForm.selectedOneType === NEGATIVE,
                              'positive-option': dynamicValidateForm.selectedOneType === POSITIVE
                            }" clearable>
                            <el-option label="阴性" :value="NEGATIVE" />
                            <el-option label="阳性" :value="POSITIVE" />
                            <el-option label="可疑" :value="SUOICIOUS" />
                          </el-select>
                          <div v-if="dynamicValidateForm.markReviewDetails.length > 0 || !resultShow">
                            <span style="color: #67c23a;" v-show="resultShow">人工标注结果相同</span>
                            <span style="color: #f56c6c;" v-show="!resultShow">人工标注结果不同</span>
                          </div>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col v-for="(markReview, index) in dynamicValidateForm.markReviewDetails">
                      <el-form-item :karyotype="markReview.karyotype" :label="'核型' + `${index + 1}`"
                        :prop="'markReviewDetails.' + index + '.karyotype'" :rules="{
                          required: true,
                          message: '核型不能为空',
                          trigger: 'blur',
                        }">
                        <el-tree-select :disabled="twoDisabled" v-model="markReview.karyotype" style="width: 100%"
                          clearable :data="karyotypeOptions" placeholder="选择核型" value-key="karyotypeId"
                          :props="{ value: 'karyotypeId', label: 'name', children: 'child' }" check-strictly filterable
                          :default-expand-all="true" :render-after-expand="false"
                          @node-click="() => handleMarkReviewChange(index)" :ref="`treeMarkReview_${index}`"
                          @visible-change="highlightFirstLevelWithChildren" />
                      </el-form-item>
                      <el-form-item :titer="markReview.titer" :label="'滴度' + `${index + 1}`"
                        :prop="'markReviewDetails.' + index + '.titer'" :rules="{
                          required: true,
                          message: '滴度不能为空',
                          trigger: 'blur',
                        }">
                        <div style="display: flex; align-items: center;">
                          <el-select :disabled="twoDisabled" v-model="markReview.titer" placeholder="选择滴度" clearable
                            style="flex: 1;" filterable :render-after-expand="false" :ref="`titerMarkReview_${index}`">
                            <el-option v-for="dict in titerStatusOptions" :key="dict.dictCode" :label="dict.dictLabel"
                              :value="dict.dictValue" />
                          </el-select>
                          <el-button v-if="!twoDisabled" type="danger" plain icon="delete"
                            @click.prevent="removeMarkReview(markReview, 'two')" style="margin-left: 8px;" />
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col>
                      <el-form-item v-show="twoQualitativeShow" label="备注" prop="remark">
                        <el-input :disabled="twoDisabled" v-model="dynamicValidateForm.remark" maxlength="512"
                          :autosize="{ minRows: 4 }" placeholder="请输入备注" show-word-limit type="textarea" />
                      </el-form-item>
                    </el-col>
                  </el-form>
                  <div v-if="oneCheck" class="dialog-footer">
                    <el-button v-show="addShow" type="primary" @click="addDomain('two')">{{ twoTitle }}</el-button>
                    <el-button v-show="oneCopyShow" type="primary" @click="reviewCopyAI(formItemRef, 'two')"
                      v-hasPermi="['livzon:tagging:reviewCopyAI']">认可AI结果</el-button>
                    <el-button v-show="oneCopyShow" type="primary" @click="resultCopy(formItemRef, 'two')"
                      v-hasPermi="['livzon:tagging:copyDimension']">认可标注结果</el-button>
                    <el-button v-show="buttonShow" type="primary" @click="submitForm(formItemRef, 'two')">确认</el-button>
                    <el-button v-show="buttonShow" @click="resetForm(formItemRef)">取消</el-button>
                  </div>
                </el-tab-pane>
                <!-- 二级复核 -->
                <el-tab-pane v-if="twoCheck" label="二级复核" name="three">
                  <el-form ref="formItemRef" style="max-width: 600px" :model="dynamicValidateForm" class="demo-dynamic">
                    <el-col v-show="threeQualitativeShow">
                      <el-form-item label="定性" prop="selectedTwoType" :rules="{
                        required: true,
                        message: '请选择阴阳性',
                        trigger: 'change'
                      }">
                        <div style="display: flex; align-items: center; gap: 10px;">
                          <el-select :disabled="threeDisabled" v-model="dynamicValidateForm.selectedTwoType"
                            placeholder="请选择" style="width: 95px" @change="(val: String) => handleChange(val, 'three')"
                            class="type-select" :class="{
                              'negative-option': dynamicValidateForm.selectedTwoType === NEGATIVE,
                              'positive-option': dynamicValidateForm.selectedTwoType === POSITIVE
                            }" clearable>
                            <el-option label="阴性" :value="NEGATIVE" />
                            <el-option label="阳性" :value="POSITIVE" />
                            <el-option label="可疑" :value="SUOICIOUS" />
                          </el-select>
                          <div v-if="dynamicValidateForm.markReviewDetails.length > 0 || !resultShow">
                            <span style="color: #67c23a;" v-show="resultShow">一级复核结果相同</span>
                            <span style="color: #f56c6c;" v-show="!resultShow">一级复核结果不同</span>
                          </div>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col v-for="(markReview, index) in dynamicValidateForm.markReviewDetails">
                      <el-form-item :karyotype="markReview.karyotype" :label="'核型' + `${index + 1}`"
                        :prop="'markReviewDetails.' + index + '.karyotype'" :rules="{
                          required: true,
                          message: '核型不能为空',
                          trigger: 'blur',
                        }">
                        <el-tree-select :disabled="threeDisabled" v-model="markReview.karyotype" style="width: 100%"
                          clearable :data="karyotypeOptions" placeholder="选择核型" value-key="karyotypeId"
                          :props="{ value: 'karyotypeId', label: 'name', children: 'child' }" check-strictly filterable
                          :render-after-expand="false" :default-expand-all="true"
                          @node-click="() => handleMarkReviewChangeThree(index)" :ref="`treeMarkReviewThree_${index}`"
                          @visible-change="highlightFirstLevelWithChildren" />
                      </el-form-item>
                      <el-form-item :titer="markReview.titer" :label="'滴度' + `${index + 1}`"
                        :prop="'markReviewDetails.' + index + '.titer'" :rules="{
                          required: true,
                          message: '滴度不能为空',
                          trigger: 'blur',
                        }">
                        <div style="display: flex; align-items: center;">
                          <el-select :disabled="threeDisabled" v-model="markReview.titer" placeholder="选择滴度" clearable
                            style="flex: 1;" filterable :render-after-expand="false"
                            :ref="`titerMarkReviewThree_${index}`">
                            <el-option v-for="dict in titerStatusOptions" :key="dict.dictCode" :label="dict.dictLabel"
                              :value="dict.dictValue" />
                          </el-select>
                          <el-button v-if="!threeDisabled" type="danger" plain icon="delete"
                            @click.prevent="removeMarkReview(markReview, 'three')" style="margin-left: 8px;" />
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col>
                      <el-form-item v-show="threeQualitativeShow" label="备注" prop="remark">
                        <el-input :disabled="threeDisabled" v-model="dynamicValidateForm.remark" maxlength="512"
                          :autosize="{ minRows: 4 }" placeholder="请输入备注" show-word-limit type="textarea" />
                      </el-form-item>
                    </el-col>
                  </el-form>
                  <div v-if="twoCheck" class="dialog-footer">
                    <el-button v-show="addShow" type="primary" @click="addDomain('three')">{{ threeTitle }}</el-button>
                    <el-button v-show="oneCopyShow" type="primary" @click="reviewCopyAI(formItemRef, 'three')"
                      v-hasPermi="['livzon:tagging:reviewCopyAI']">认可AI结果</el-button>
                    <el-button v-show="oneCopyShow" type="primary" @click="resultCopy(formItemRef, 'three')"
                      v-hasPermi="['livzon:tagging:copyCheck']">认可复核结果</el-button>
                    <el-button v-show="buttonShow" @click="submitForm(formItemRef, 'three')">确认</el-button>
                    <el-button v-show="buttonShow" @click="resetForm(formItemRef)">取消</el-button>
                  </div>
                </el-tab-pane>
              </el-tabs>

            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 详情 -->
    <el-dialog title="详情" v-model="openView" :width="isMobile ? '90%' : '30%'" append-to-body>
      <el-form ref="form" :model="formDetails" :inline="!isMobile">
        <el-row>
          <el-col :span="isMobile ? 24 : 20">
            <el-form-item label="文件名称：" :label-width="isMobile ? '80px' : 'auto'">{{ formDetails.fileName
              }}</el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 20">
            <el-form-item label="状态：" :label-width="isMobile ? '80px' : 'auto'">
              <!-- 直接用span显示状态，AI_FAILED为红色，其它为黑色 -->
              <span :style="{ color: formDetails.fileStatus === 'AI_FAILED' ? '#f56c6c' : '#222' }">
                {{ selectDictLabel(fileStatusOptions, formDetails.fileStatus) }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="AI预测类型：" :label-width="isMobile ? '80px' : 'auto'">
              <el-input :class="{
                'negative': formDetails.predictType === 'negative',
                'positive': formDetails.predictType === 'positive'
              }" v-model="formDetails.predictType" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="AI类型估值：" :label-width="isMobile ? '80px' : 'auto'">
              <el-input v-model="formDetails.type" disabled />
            </el-form-item>
          </el-col>
          <template v-for="(items, index) in formDetails.aiPredictKaryotypeDetailsVOS">
            <el-col :span="isMobile ? 24 : 12">
              <el-form-item :label="`核型${index + 1}：`" :label-width="isMobile ? '80px' : 'auto'">
                <el-input v-model="items.predictKaryotype" disabled />
              </el-form-item>
              <el-form-item :label="`估值${index + 1}：`" :label-width="isMobile ? '80px' : 'auto'">
                <el-input v-model="items.karyotype" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="isMobile ? 24 : 12">
              <el-form-item :label="`滴度${index + 1}：`" :label-width="isMobile ? '80px' : 'auto'">
                <el-input v-model="items.predictTiters" disabled />
              </el-form-item>
              <el-form-item :label="`估值${index + 1}：`" :label-width="isMobile ? '80px' : 'auto'">
                <el-input v-model="items.titers" disabled />
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="试剂厂家：" :label-width="isMobile ? '80px' : 'auto'">
              <el-select v-model="formDetails.reagentManufacturer" clearable style="width: 100%" filterable
                :render-after-expand="false" disabled>
                <el-option v-for="dict in reagent_manufacturer" :key="dict.dictCode" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="显微镜型号：" :label-width="isMobile ? '80px' : 'auto'">
              <el-input v-model="formDetails.imagingDevice" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="物镜：" :label-width="isMobile ? '80px' : 'auto'">
              <el-input v-model="formDetails.objective" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 20">
            <el-form-item label="人工标注：" :label-width="isMobile ? '80px' : 'auto'"
              :style="`${formDetails.manualAnnotationType}` === 'POSITIVE' ? `color: #f56c6c;` : `color: #409EFF;`">
              {{ formDetails.manualAnnotationType === 'POSITIVE' ? '阳性' : '阴性' }}
            </el-form-item>
          </el-col>
          <template v-for="(items, index) in formDetails.manualAnnotationDetailsVOList">
            <el-col :span="isMobile ? 24 : 12">
              <el-form-item :label="`核型${index + 1}：`" :label-width="isMobile ? '80px' : 'auto'">
                <el-tree-select v-model="items.karyotype" :data="karyotypeOptions"
                  :props="{ value: 'karyotypeId', label: 'name', children: 'child' }" value-key="karyotypeId"
                  placeholder="选择核型" check-strictly filterable :render-after-expand="false" style="width: 100%;"
                  clearable show-checkbox disabled />
              </el-form-item>
            </el-col>
            <el-col :span="isMobile ? 24 : 12">
              <el-form-item :label="`滴度${index + 1}：`" :label-width="isMobile ? '80px' : 'auto'">
                <el-select v-model="items.titer" clearable style="width: 100%" filterable :render-after-expand="false"
                  disabled>
                  <el-option v-for="dict in titerStatusOptions" :key="dict.dictCode" :label="dict.dictLabel"
                    :value="dict.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
          </template>

          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="创建时间：" :label-width="isMobile ? '80px' : 'auto'">{{ dateTimeSub(formDetails.createDate)
              }}</el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="创建人名字：" :label-width="isMobile ? '80px' : 'auto'">{{ formDetails.createUserName
              }}</el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="图片信息：" :label-width="isMobile ? '80px' : 'auto'">{{ formDetails.imageInfo
              }}</el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 20">
            <el-form-item label="异常信息：" :label-width="isMobile ? '80px' : 'auto'">{{ formDetails.errorInfo
              }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openView = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <GlobalNotice />
    <!-- 快捷键引导 -->
  </div>
</template>

<script lang="ts" name="index" setup>
import tagging from "@/api/request/admin/tagging/index";
import VueQr from 'vue-qr/src/packages/vue-qr.vue' //生成二维码
import proportion from "@assets/images/proportion.jpg";
import upload from "@/views/admin/tagging/upload.vue";
import GlobalNotice from "@/components/GlobalNotice.vue";
import { Calendar, InfoFilled, Upload, Search, Refresh, Mouse } from '@element-plus/icons-vue';

//入参和方法
const {
  loading, open, showSearch, total, fileList, title, queryParams, queryFormRef, form, rules,
  getList, getList1, handleQuery, resetQuery, handleSelectionChange, handleDelete, handleExport,
  handleClearCache, pageTableRef,
  handleUpload, fileStatusOptions, oneFileStatusOptions, twoFileStatusOptions, reagent_manufacturer, fileRef, uploadForm, uploadComponent,
  karyotypeOptions, aiFormRef, aiForm, handleChange, oneDisabled, twoDisabled, threeDisabled,
  handleRecognition,
  openView, formDetails,
  handleDistribution, openDistribution, formDistribution, downReviewersSelect, submitDistribution, distributionRef,
  titerStatusOptions,
  imageCanvasRef, width,
  removeDomain, addDomain, submitForm, resetForm, dynamicValidateForm, formItemRef, removeMarkReview,
  onChangeChecked, activeName, handleDomainsChange, handleMarkReviewChange, handleMarkReviewChangeThree,
  oneCheck, twoCheck, config, qrcodeShow, resultShow, resultCopy,
  imageInfo, confirm, handleUploadRequest, handleDownload,
  loadingDownload, downloadedCount, totalCount, ids,
  handleSortChange,
  oneQualitativeShow, twoQualitativeShow, threeQualitativeShow, buttonShow, reviewCopyAI,
  addShow,
  oneCopyShow,
  oneTitle, twoTitle, threeTitle, newTime, getFileStatus,
  handleSizeChange,
  NEGATIVE, POSITIVE, SUOICIOUS,
  objective, isTiter,
  calendarValue, datesWithData, hasDataOnDate, handleCalendarChange,
  loadCalendarData, handleDateClick,
  // 从tagging.ts导入的UI相关功能
  showProportionImage, showTip, isMobile,
  filteredImagingDevice, formatNumber, toggleProportionImage, canvasStyle, adjustBrightness, tableHeight, handleReagentChange, highlightFirstLevelWithChildren,
  // 新增的组件级别功能
  handleResetImage, isUploading, handleRowClick, handleRowDblClick
} = tagging();

</script>


<style scoped lang="scss">
/**
 * 主图片下方工具栏响应式布局
 * PC端：imageInfo和按钮同一行两端对齐
 * 手机端：imageInfo单独一行，按钮组下一行靠右
 */
.main-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;


  .toolbar-btns {
    display: flex;
    align-items: center;
    gap: 2px;
  }
}

.image-info {
  word-break: break-all; // 长字符串自动换行
  white-space: pre-line; // 保留文本中的换行
  color: #606266;
}

@media (max-width: 768px) {
  .main-toolbar {
    flex-direction: column;
    align-items: stretch;

    .image-info {
      margin-bottom: 8px;
      text-align: left;
    }

    .toolbar-btns {
      justify-content: flex-end;
      width: 100%;
    }
  }
}

.app-container {
  padding: 10px;
  background: rgba(0, 0, 0, 0.2);
  height: calc(100vh - 84px);
  overflow: auto;
  display: flex;
  flex-direction: column;

  .el-row {
    height: auto;
    margin: 0 !important;
    min-height: 100%;

    .el-col {
      height: auto;
      padding: 0 5px !important;
    }
  }
}

/* ========== 卡片样式统一设置 ========== */
:deep(.el-card) {
  height: auto;
  display: flex;
  flex-direction: column;
  background-color: transparent !important;
  border: none !important;
  margin: 0;

  .el-card__header {
    padding: 10px;
    background-color: transparent !important;
    border-bottom: none !important;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .el-card__body {
    flex: 1;
    padding: 10px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    background-color: transparent !important;
    border: none !important;
  }
}

.el-card.is-always-shadow {
  margin: 0 0 10px 0;
}

/* 移除卡片内部所有分隔线 */
:deep(.el-card) > div,
:deep(.el-card__body) > div,
:deep(.el-card__header) > div {
  border-bottom: none !important;
  border-top: none !important;
}

/* 搜索区域背景色 */
.search-wrapper {
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

/* AI预测区域背景色 */
.aiDiv {
  background-color: rgba(40, 203, 128, 0.1);
  border-radius: 4px;
  padding: 10px;
  overflow-y: auto;
  flex: 1;
}

/* 表格背景色 */
.table-container {
  :deep(.el-table) {
    background-color: transparent !important;

    &::before {
      display: none;
    }

    .el-table__header-wrapper {
      position: sticky;
      top: 0;
      z-index: 2;
      background-color: transparent !important;
    }

    .el-table__body-wrapper {
      overflow: auto;
      background-color: transparent !important;
    }

    tr {
      background-color: transparent !important;
    }

    th.el-table__cell {
      background-color: transparent !important;
    }

    td.el-table__cell {
      background-color: transparent !important;
    }
  }
}

/* 确保所有弹窗内容区域也保持透明背景 */
:deep(.el-dialog) {
  background-color: transparent !important;

  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    background-color: transparent !important;
  }
}

.tagging {
  blockquote {
    padding: 5px 10px;
    margin: 0 0 10px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }



  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans",
  "Helvetica Neue",
  Helvetica,
  Arial,
  sans-serif;
  font-size: 13px;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;

    }


  }

  :deep(.el-table__header-wrapper tr th),
  :deep(.el-table__body tr td) {
    background-color: rgba(0, 0, 0, 0.2) !important;
  }

  // 结果上传区域按钮样式
  .dialog-footer {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: flex-end !important;
    gap: 8px; // 按钮间距
    margin-top: 10px !important;

    .el-button {
      margin: 0 !important;
      height: 32px !important;
      font-size: 13px !important;
      padding: 0 12px !important;

      &.el-button--primary {
        background-color: #409EFF;
        border-color: #409EFF;
        color: #fff;

        &.is-plain {
          background-color: #ecf5ff;
          border-color: #b3d8ff;
          color: #409EFF;
        }
      }
    }
  }

  // 不认可AI结果按钮样式
  .el-button--primary.is-plain {
    &.el-button--small {
      padding: 0 12px;
      line-height: 26px;
      background-color: #fff;
      border-color: #dcdfe6;

      .el-icon {
        margin-right: 4px;
      }

      &:hover {
        color: #409EFF;
        border-color: #409EFF;
      }
    }
  }

  // 清空按钮样式
  .el-button--default {
    &.el-button--small {
      padding: 0 12px;
      height: 28px;
      line-height: 26px;
      background-color: #fff;
      border-color: #dcdfe6;
      color: #606266;

      &:hover {
        color: #409EFF;
        border-color: #409EFF;
      }
    }
  }

  // 添加按钮组样式
  .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    padding: 8px 0 0 0 !important;
    margin-bottom: 2px !important;
    align-items: center;
    border: none !important;

    .el-button {
      margin: 0;
    }
  }
}



@media (max-width: 768px) {
  .el-table {
    width: auto;
    /* 或者其他适合小屏幕的宽度 */
  }
}



/* 确保表单和按钮区域之间没有边框 */
.search-wrapper {
  border-bottom: none !important;
}



.demo-image__preview {
  height: 500px;
  overflow-y: auto;
}

.demo-image__preview .el-image {
  display: block;
  min-height: 200px;
  margin-bottom: 100px;
}

.demo-image__preview .el-image:last-child {
  margin-bottom: 0;
}

/* 右侧容器样式 */
.boby {
  height: calc(100vh - 104px); /* 与app-container高度协调，减去页面头部和边距 */
  overflow-y: auto; /* 只保留一个主滚动条 */
  padding-right: 8px; /* 为滚动条留出空间 */
}

/* AI预测区域样式 */
.aiDiv {
  background-color: rgba(40, 203, 128, 0.1);
  border-radius: 4px;
  padding: 10px;
  /* 移除内部滚动，让内容自然展开 */
}

/* 人工标注卡片样式 */
.boby .update-log:last-child {
  margin-top: 16px;
  /* 移除flex布局，让内容自然展开 */
}

/* 移除所有内部滚动，让内容在主容器中自然滚动 */
.boby .update-log:last-child .demo-tabs :deep(.el-tabs__content) {
  overflow: visible; /* 移除内部滚动 */
}

/* 标注表单区域样式 */
.boby .update-log:last-child .demo-dynamic {
  max-height: none; /* 移除最大高度限制 */
}



/* ========== 表单和选择器样式 ========== */

/* 隐藏内部滚动条 */
.aiDiv::-webkit-scrollbar,
.boby .update-log:last-child .demo-tabs :deep(.el-tabs__content)::-webkit-scrollbar {
  display: none;
}

.type-select {
  :deep(.el-input__wrapper) {
    background-color: #f5f7fa !important;
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }

  &.negative-option {
    :deep(.el-input__inner) {
      color: #67c23a !important;
    }
  }

  &.positive-option {
    :deep(.el-input__inner) {
      color: #f56c6c !important;
    }
  }

  :deep(.el-select-dropdown__item) {
    &.selected {
      color: #67c23a !important;
      font-weight: bold;
      background-color: #f0f9eb !important;
    }

    &.selected.positive-option {
      color: #f56c6c !important;
      background-color: #fef0f0 !important;
    }
  }
}

.progress-bar {
  width: 100%;
  height: 10px;
  background-color: #f3f3f3;
  border-radius: 5px;
  position: relative;
}

.progress {
  width: 0;
  height: 100%;
  background-color: #409eff;
  border-radius: 5px;
  transition: width 1s ease;
}

.icon {
  margin: 0 10px;
  font-size: 30px;
}

.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}

#imageZoomContainer {
  position: relative;
  width: 300px;
  height: 300px;
  overflow: hidden;
}

.controls {
  flex: 1;
  width: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  height: auto;

  canvas {
    max-width: 100%;
    height: auto;
    object-fit: contain;
    transition: filter 0.3s ease;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

.highlight {
  width: 100%;
  height: auto;
  object-fit: contain;
  transition: filter 0.3s ease;
}

.demo-pagination-block {
  margin-top: 10px;

  :deep(.el-pagination) {
    flex-wrap: wrap;
  }

  :deep(.el-pagination__total) {
    margin: 1px 0 0 0;
  }
}

.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.proportion-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0;
}

.proportion,
.controls {
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border-radius: 8px;
  display: block;
}

.proportion {
  height: 110px;
  object-fit: cover;
}

@media (max-width: 768px) {
  .proportion {
    height: 80px;
  }
}

.proportionSpan {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  width: 100%;
  font-size: 12px;
  color: #606266;
  padding: 0;
  margin: 0;
  text-align: center;
  gap: 0;

  span {
    width: 100%;
    text-align: center;
    padding: 3px 0;
    line-height: 1;
  }
}

/* 优化表格区域 */
.table-container {
  height: calc(100% - 120px); // 减去其他元素的高度
  overflow: hidden;

  :deep(.el-table) {
    height: 100%;

    .el-table__body-wrapper {
      height: calc(100% - 40px); // 减去表头高度
      overflow: auto;
    }
  }
}

:deep(.negative) {
  .el-input__inner {
    color: #67c23a;
  }
}

:deep(.positive) {
  .el-input__inner {
    color: #f56c6c;
  }
}

.type-select {
  :deep(.el-input__wrapper) {
    background-color: #f5f7fa !important;
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }

  &.negative-option {
    :deep(.el-input__inner) {
      color: #67c23a !important;
    }
  }

  &.positive-option {
    :deep(.el-input__inner) {
      color: #f56c6c !important;
    }
  }
}

:deep(.el-select-dropdown__item.selected) {
  font-weight: bold;
  background-color: transparent !important;
}

:deep(.el-input__inner) {
  &.negative {
    color: #67c23a !important;
  }

  &.positive {
    color: #f56c6c !important;
  }
}

// 确保下拉框选项的文本颜色
:deep(.el-select-dropdown__item) {
  &.negative-option {
    color: #67c23a !important;

    span {
      color: #67c23a !important;
    }
  }

  &.positive-option {
    color: #f56c6c !important;

    span {
      color: #f56c6c !important;
    }
  }
}

// 添加一级复核结果相同的字体大小样式
:deep(.el-tag) {
  font-size: 10px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

.clearfix {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px !important;
  margin: 0;
  width: 100%;

  .ai-prediction-header {
    flex: 0 0 auto;

    .header-text {
      color: #409EFF;
      font-size: 16px;
      line-height: 1;
      font-weight: normal;
      white-space: nowrap;
    }
  }

  .prediction-probability {
    flex: 0 0 auto;
    color: #606266;
    font-size: 15px;
    line-height: 1;
    white-space: nowrap;
    margin-left: auto;
    padding-right: 24px;
  }
}

@media (max-width: 768px) {
  .app-container {
    height: auto;
    min-height: 100vh;
  }

  /* 手机版AI预测标题靠左对齐 */
  .clearfix {
    justify-content: flex-start !important;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    .ai-prediction-header {
      .header-text {
        font-size: 14px;
      }
    }

    .prediction-probability {
      margin-left: 0;
      padding-right: 0;
      font-size: 12px;
      color: #909399;
    }
  }

  .el-dialog {
    margin: 10px auto !important;
    max-width: 90% !important;
    max-height: 90vh;
    overflow: auto;

    &.mobile-dialog {
      .el-dialog__body {
        padding: 10px 15px !important;
      }
    }
  }

  .mobile-btn-group {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
    width: 100%;
  }
}

.search-wrapper {
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;

  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;

    .search-item {
      flex: 1;
      min-width: 240px;
      margin: 0;

      :deep(.el-form-item__label) {
        color: #606266;
        font-weight: bold;
        font-size: 15px;
      }

      :deep(.el-input__wrapper),
      :deep(.el-select),
      :deep(.el-date-editor.el-input__wrapper) {
        width: 100%;
        box-shadow: 0 0 0 1px #dcdfe6 inset;

        &:hover {
          box-shadow: 0 0 0 1px #409EFF inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #409EFF inset;
        }
      }
    }
  }

  .search-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;

    .el-button {
      min-width: 80px;
      padding: 8px 16px;
      border-radius: 4px;
      font-weight: 500;

      &--primary {
        background-color: #409EFF;
        border-color: #409EFF;

        &:hover {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }
      }

      &--success {
        &.is-plain {
          background-color: #f0f9ff;
          border-color: #67c23a;
          color: #67c23a;

          &:hover {
            background-color: #67c23a;
            border-color: #67c23a;
            color: #fff;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .search-wrapper {
    .search-buttons {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 0;
      margin: 0;

      .el-button {
        width: 100%;
        margin: 0;
        height: 36px;
      }

      .el-button+.el-button {
        margin-top: 8px;
      }
    }
  }
}



.proportion-header {
  .el-button {
    &.el-button--primary.is-link {
      height: 25px;
      line-height: 25px;
      font-size: 14px;

      &:hover {
        color: #409eff;
      }
    }
  }
}

.proportion-toggle-btn {
  color: #c3420edb !important;
  font-weight: normal !important;
}

.proportion-toggle-btn:hover {
  color: #66b1ff !important;
}

.domain-divider {
  width: 100%;
  margin: 2px auto 6px auto;
  border-top: 1.5px solid #ebebeb;
}

/* 添加图标样式 */
.el-button-group {
  .el-button {
    .el-icon {
      transition: transform 0.3s;
    }

    &:hover .el-icon {
      transform: scale(1.2);
    }
  }
}

/* 搜索图标禁用样式 */
.search-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.search-disabled-line {
  position: absolute;
  width: 140%;
  height: 2px;
  background-color: #ff4949;
  transform: rotate(-45deg);
  left: -20%;
  top: 50%;
}

.image-control-tip {
  margin-left: 8px;
  color: #409EFF;
  cursor: help;
  font-size: 16px;
  vertical-align: middle;

  &:hover {
    color: #66b1ff;
  }
}

.mouse-icon {
  margin-left: 4px;
  font-size: 14px;
  vertical-align: middle;
}

/* 树形选择器层级样式 */
:deep(.el-tree-select) {
  .el-tree {
    background: transparent;

    .el-tree-node {

      // 一级节点样式
      >.el-tree-node__content {
        background-color: #f0f9ff !important;
        border: 1px solid #e1f3ff !important;
        border-radius: 4px !important;
        margin: 4px 0 !important;
        padding: 8px 12px !important;
        font-weight: bold !important;

        &:hover {
          background-color: #e6f7ff !important;
        }
      }

      // 二级节点样式
      .el-tree-node__children {
        .el-tree-node__content {
          background-color: #f6ffed !important;
          border: 1px solid #e6f7d9 !important;
          border-radius: 4px !important;
          margin: 4px 0 4px 24px !important;
          padding: 6px 12px !important;

          &:hover {
            background-color: #e6f7d9 !important;
          }
        }

        // 三级及以下节点样式
        .el-tree-node__children {
          .el-tree-node__content {
            background-color: #fff7e6 !important;
            border: 1px solid #ffe7ba !important;
            border-radius: 4px !important;
            margin: 4px 0 4px 24px !important;
            padding: 6px 12px !important;

            &:hover {
              background-color: #ffe7ba !important;
            }
          }
        }
      }
    }

    // 节点展开/折叠图标样式
    .el-tree-node__expand-icon {
      color: #606266 !important;
      font-size: 16px !important;
      margin-right: 8px !important;

      &.expanded {
        transform: rotate(90deg) !important;
      }
    }

    // 选中节点样式
    .el-tree-node.is-current>.el-tree-node__content {
      background-color: #ecf5ff !important;
      border-color: #409EFF !important;
      color: #409EFF !important;
    }
  }
}

/* 确保下拉面板中的树形选择器样式正确 */
:deep(.el-select-dropdown) {
  .el-tree {
    background: transparent;

    .el-tree-node {
      >.el-tree-node__content {
        background-color: #f0f9ff !important;
        border: 1px solid #e1f3ff !important;
        border-radius: 4px !important;
        margin: 4px 0 !important;
        padding: 8px 12px !important;
        font-weight: bold !important;
      }
    }
  }
}

:deep(.el-select-dropdown .el-tree-node > .el-tree-node__content.has-children) {
  background-color: #e6f7ff !important;
  font-weight: bold !important;
  color: #174a7c !important;
}

:deep(.el-select-dropdown .el-tree-node > .el-tree-node__content.no-children) {
  background-color: #f5f5f5 !important;
  font-weight: normal !important;
  color: #999 !important;
}

.image-tip-popover {
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  color: #606266;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 表格选中行高亮样式 */
:deep(.el-table) {
  .el-table__row {
    &.current-row {
      background-color: rgba(64, 158, 255, 0.1) !important;

      td {
        background-color: rgba(64, 158, 255, 0.1) !important;
      }
    }

    &.hover-row {
      background-color: rgba(64, 158, 255, 0.05) !important;

      td {
        background-color: rgba(64, 158, 255, 0.05) !important;
      }
    }

    /* 复选框选中行样式 */
    &.selected {
      background-color: rgba(64, 158, 255, 0.2) !important;

      td {
        background-color: rgba(64, 158, 255, 0.2) !important;
      }
    }
  }
}

/* 确保选中行在暗色主题下也能清晰显示 */
.tagging {
  :deep(.el-table) {
    .el-table__row {
      &.current-row {
        background-color: rgba(64, 158, 255, 0.2) !important;

        td {
          background-color: rgba(64, 158, 255, 0.2) !important;
        }
      }

      &.hover-row {
        background-color: rgba(64, 158, 255, 0.1) !important;

        td {
          background-color: rgba(64, 158, 255, 0.1) !important;
        }
      }

      /* 暗色主题下复选框选中行样式 */
      &.selected {
        background-color: rgba(64, 158, 255, 0.3) !important;

        td {
          background-color: rgba(64, 158, 255, 0.3) !important;
        }
      }
    }
  }
}

/* ========== 滚动条样式统一设置 ========== */

/* 主容器滚动条 */
.boby::-webkit-scrollbar {
  width: 8px;
}

.boby::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.boby::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.boby::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格滚动条 */
:deep(.el-table .el-scrollbar__bar) {
  &.is-vertical {
    width: 8px !important;
    right: 2px !important;
    z-index: 1 !important;
  }

  &.is-horizontal {
    height: 8px !important;
    bottom: 2px !important;
    z-index: 1 !important;
  }
}

:deep(.el-table .el-scrollbar__thumb) {
  background: #c1c1c1 !important;
  border-radius: 4px !important;
  min-height: 30px !important;
  opacity: 0.8 !important;
  transition: all 0.2s ease !important;

  &:hover {
    background: #a8a8a8 !important;
    opacity: 1 !important;
  }
}

:deep(.el-table .el-scrollbar__track) {
  background: transparent !important;
}

.table-container {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
}

.dialog-content-scroll {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 4px;
}

@media (max-width: 768px) {
  .el-dialog {
    max-width: 98vw !important;
    margin: 10px auto !important;
  }

  .el-form .el-form-item {
    flex-direction: column !important;
    align-items: stretch !important;
    margin-bottom: 12px !important;
  }

  .el-form .el-form-item__label {
    margin-bottom: 4px !important;
    text-align: left !important;
  }

  .dialog-footer {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
  }

  .dialog-footer .el-button {
    width: 100% !important;
  }
}

@media (max-width: 768px) {
  .control-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .filelist-guide {
    align-self: center;
  }
}

// 移除未使用的样式类
.demo-image__preview {
  display: none;
}

// 移除重复的样式定义
.type-select {
  :deep(.el-input__wrapper) {
    background-color: #f5f7fa !important;
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  }
}

.search-item :deep(.el-input__wrapper),
.search-item :deep(.el-select),
.search-item :deep(.el-date-editor) {
  width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box;
}

.file-name-cell {
  white-space: normal;
  word-break: break-all;
  line-height: 1.4;
  padding: 8px 0;
}

/* 控制栏样式 */
.control-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 8px 0;
  padding: 0;
  width: 100%;
}

/* 快捷键提示样式 */
.filelist-guide {
  position: relative;
  background: #409eff;
  color: #fff;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  flex: 1;
  width: 100%;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.guide-text {
  font-weight: 500;
  letter-spacing: 0.5px;
  line-height: 1;
}

/* 滴度切换按钮样式 */
.titer-toggle-container {
  flex: 0 0 auto;
}

.titer-toggle-btn {
  background: linear-gradient(45deg, #409EFF, #67C23A) !important;
  border: none !important;
  color: #fff !important;
  font-weight: 500 !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3) !important;
  font-size: 14px !important;
  height: 32px !important;
  line-height: 1 !important;
  box-sizing: border-box !important;

  &:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4) !important;
  }

  &:active {
    transform: translateY(0) !important;
  }
}

/* 日历弹窗样式 */
.calendar-popover {
  padding: 0;
  margin: 0;
}

:deep(.calendar-popover-custom) {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e4e7ed !important;
  padding: 0 !important;

  .el-popover__content {
    padding: 0 !important;
  }
}

:deep(.el-popover.el-popper) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e4e7ed;

  .el-popover__content {
    padding: 0 !important;
  }
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 50%, #409eff 100%);
  border-bottom: 1px solid #337ecc;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s ease-in-out infinite;
  }

  .header-title {
    display: flex;
    align-items: center;
    color: #ffffff;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    z-index: 2;
    position: relative;

    .el-icon {
      margin-right: 8px;
      font-size: 18px;
      color: #ffffff;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }

    span {
      letter-spacing: 0.5px;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 2;
    position: relative;
  }

  .info-icon {
    color: rgba(255, 255, 255, 0.9);
    cursor: help;
    font-size: 16px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));

    &:hover {
      color: #ffffff;
      transform: scale(1.1);
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.compact-data-calendar {
  :deep(.el-calendar) {
    background: transparent;
    border: none;

    .el-calendar__header {
      background: #f0f2f5;
      border: 1px solid #d9d9d9;
      border-radius: 6px 6px 0 0;
      padding: 8px 16px;

      .el-calendar__title {
        color: #262626;
        font-weight: 600;
        font-size: 14px;
      }

      .el-calendar__button-group {
        .el-button {
          background: white;
          border: 1px solid #d9d9d9;
          color: #262626;
          font-size: 12px;
          padding: 4px 8px;

          &:hover {
            background: #f0f2f5;
            border-color: #409eff;
            color: #409eff;
          }
        }
      }
    }

    .el-calendar__body {
      padding: 0;
      border: 1px solid #d9d9d9;
      border-top: none;
      border-radius: 0 0 6px 6px;

      .el-calendar-table {
        .el-calendar-day {
          height: 45px;
          padding: 0;
          border-right: 1px solid #f0f0f0;
          border-bottom: 1px solid #f0f0f0;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(64, 158, 255, 0.05);
          }

          &.is-selected {
            background: rgba(64, 158, 255, 0.1);
            border-color: #409eff;
          }
        }

        thead th {
          background: #fafafa;
          color: #666;
          font-weight: 500;
          padding: 8px 0;
          border-bottom: 1px solid #e8e8e8;
          font-size: 12px;
        }
      }
    }
  }
}

.compact-calendar-cell {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 2px;

  &:hover {
    background: rgba(64, 158, 255, 0.08);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &.has-data {
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%);
    border: 1px solid rgba(103, 194, 58, 0.3);

    .date-text {
      color: #67c23a;
      font-weight: 600;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(103, 194, 58, 0.25) 0%, rgba(103, 194, 58, 0.15) 100%);
      border-color: #67c23a;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(103, 194, 58, 0.2);
    }
  }
}

.date-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.data-indicator {
  width: 6px;
  height: 6px;
  background: #67c23a;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-calendar-card {
    margin-top: 8px !important;

    :deep(.el-card__header) {
      padding: 8px 12px !important;
    }

    :deep(.el-card__body) {
      padding: 8px !important;
    }
  }

  .calendar-header {
    .header-title {
      font-size: 13px;
    }
  }

  .compact-data-calendar {
    :deep(.el-calendar) {
      .el-calendar__header {
        padding: 6px 12px;

        .el-calendar__title {
          font-size: 13px;
        }


      }

      .el-calendar__body {
        .el-calendar-table {
          .el-calendar-day {
            height: 35px;
          }

          thead th {
            padding: 6px 0;
            font-size: 11px;
          }
        }
      }
    }
  }

  .compact-calendar-cell {
    margin: 1px;

    .date-text {
      font-size: 11px;
    }

    .data-indicator {
      width: 4px;
      height: 4px;
    }
  }
}



  .calendar-header {
    .header-title {
      font-size: 13px;
    }
  }

  .compact-data-calendar {
    :deep(.el-calendar) {
      .el-calendar__header {
        padding: 6px 12px;

        .el-calendar__title {
          font-size: 13px;
        }


      }

      .el-calendar__body {
        .el-calendar-table {
          .el-calendar-day {
            height: 35px;
          }

          thead th {
            padding: 6px 0;
            font-size: 11px;
          }
        }
      }
    }

    .compact-calendar-cell {
      margin: 1px;

      .date-text {
        font-size: 11px;
      }

      .data-indicator {
        width: 4px;
        height: 4px;
      }
    }
  }

</style>

<style lang="scss">
/* 日历弹窗样式 */
.calendar-popover {
  padding: 0;
  margin: 0;
}

:deep(.calendar-popover-custom) {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e4e7ed !important;
  padding: 0 !important;

  .el-popover__content {
    padding: 0 !important;
  }
}

:deep(.el-popover.el-popper) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e4e7ed;

  .el-popover__content {
    padding: 0 !important;
  }
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 50%, #409eff 100%);
  border-bottom: 1px solid #337ecc;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s ease-in-out infinite;
  }

  .header-title {
    display: flex;
    align-items: center;
    color: #ffffff;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    z-index: 2;
    position: relative;

    .el-icon {
      margin-right: 8px;
      font-size: 18px;
      color: #ffffff;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }

    span {
      letter-spacing: 0.5px;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 2;
    position: relative;
  }

  .info-icon {
    color: rgba(255, 255, 255, 0.9);
    cursor: help;
    font-size: 16px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));

    &:hover {
      color: #ffffff;
      transform: scale(1.1);
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.compact-data-calendar {
  :deep(.el-calendar) {
    background: transparent;
    border: none;

    .el-calendar__header {
      background: #f0f2f5;
      border: 1px solid #d9d9d9;
      border-radius: 6px 6px 0 0;
      padding: 8px 16px;

      .el-calendar__title {
        color: #262626;
        font-weight: 600;
        font-size: 14px;
      }


    }

    .el-calendar__body {
      padding: 0;
      border: 1px solid #d9d9d9;
      border-top: none;
      border-radius: 0 0 6px 6px;

      .el-calendar-table {
        .el-calendar-day {
          height: 45px;
          padding: 0;
          border-right: 1px solid #f0f0f0;
          border-bottom: 1px solid #f0f0f0;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(64, 158, 255, 0.05);
          }

          &.is-selected {
            background: rgba(64, 158, 255, 0.1);
            border-color: #409eff;
          }
        }

        thead th {
          background: #fafafa;
          color: #666;
          font-weight: 500;
          padding: 8px 0;
          border-bottom: 1px solid #e8e8e8;
          font-size: 12px;
        }
      }
    }
  }
}

.compact-calendar-cell {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 2px;

  &:hover {
    background: rgba(64, 158, 255, 0.08);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &.has-data {
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%);
    border: 1px solid rgba(103, 194, 58, 0.3);

    .date-text {
      color: #67c23a;
      font-weight: 600;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(103, 194, 58, 0.25) 0%, rgba(103, 194, 58, 0.15) 100%);
      border-color: #67c23a;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(103, 194, 58, 0.2);
    }
  }
}

.date-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.data-indicator {
  width: 6px;
  height: 6px;
  background: #67c23a;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .mobile-btn-group {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
    width: 100%;
  }
}

@media (max-width: 768px) {
  :deep(.el-form-item__label) {
    text-align: left !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
    width: 100% !important;
    min-width: 0 !important;
    display: block !important;
    box-sizing: border-box !important;
  }

  :deep(.el-form-item) {
    align-items: flex-start !important;
    width: 100% !important;
  }
}

/* 增强的数据日期高亮样式 */
@keyframes dataPulse {
  0% {
    box-shadow: 0 2px 12px rgba(255, 107, 53, 0.4);
  }

  50% {
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.8);
  }

  100% {
    box-shadow: 0 2px 12px rgba(255, 107, 53, 0.4);
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-4px);
  }
}

/* 橙色高亮样式 - 最高优先级 */
.compact-data-calendar .compact-calendar-cell.has-data,
.compact-calendar-cell.has-data,
:deep(.el-calendar) .compact-calendar-cell.has-data,
:deep(.el-calendar-day) .compact-calendar-cell.has-data {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 2px solid #e55a2b !important;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.6) !important;
  animation: dataPulse 2s infinite !important;
  position: relative !important;
  z-index: 5 !important;
  border-radius: 6px !important;
  color: white !important;
  font-weight: bold !important;
  transform: scale(1.02) !important;
}

/* 更高优先级的选择器 */
:deep(.el-calendar-day .compact-calendar-cell.has-data) {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 2px solid #e55a2b !important;
  box-shadow: 0 2px 12px rgba(255, 107, 53, 0.4) !important;
  animation: dataPulse 2s infinite !important;
  position: relative !important;
  z-index: 5 !important;
}

.compact-data-calendar .compact-calendar-cell.has-data .date-text,
.compact-calendar-cell.has-data .date-text {
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  font-size: 15px !important;
}

:deep(.el-calendar-day .compact-calendar-cell.has-data .date-text) {
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  font-size: 15px !important;
}

.compact-data-calendar .compact-calendar-cell.has-data:hover {
  background: linear-gradient(135deg, #f7931e 0%, #ff6b35 100%) !important;
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6) !important;
  border-color: #409eff !important;
}

:deep(.el-calendar-day .compact-calendar-cell.has-data:hover) {
  background: linear-gradient(135deg, #f7931e 0%, #ff6b35 100%) !important;
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6) !important;
  border-color: #409eff !important;
}

.compact-calendar-cell.has-data::before {
  content: '📊';
  position: absolute;
  top: -8px;
  right: -8px;
  background: #409eff;
  color: white;
  font-size: 10px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
  animation: bounce 1.5s infinite alternate;
  z-index: 10;
}

/* 调试标记样式 */
.debug-mark {
  position: absolute;
  top: 2px;
  left: 2px;
  background: #67c23a;
  color: white;
  font-size: 10px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 15;
  border: 1px solid white;
}

/* 调试边框 */
.debug-cell {
  border: 1px dashed #ccc !important;
}

/* 微光动画效果 */
@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

/* 最高优先级的样式规则 - 确保高亮显示 */
.compact-data-calendar :deep(.el-calendar-table) .el-calendar-day .compact-calendar-cell.has-data {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 3px solid #e55a2b !important;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.6) !important;
  animation: dataPulse 2s infinite !important;
  position: relative !important;
  z-index: 10 !important;
  border-radius: 6px !important;
}

.compact-data-calendar :deep(.el-calendar-table) .el-calendar-day .compact-calendar-cell.has-data .date-text {
  color: white !important;
  font-weight: 900 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
  font-size: 16px !important;
}

/* 强制覆盖Element Plus的默认样式 */
.compact-data-calendar :deep(.el-calendar-day) {
  position: relative !important;
}

.compact-data-calendar :deep(.el-calendar-day):has(.compact-calendar-cell.has-data) {
  background: transparent !important;
  border: none !important;
}

/* 超高优先级样式 */
[data-has-data="true"] {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 3px solid #e55a2b !important;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.6) !important;
  color: white !important;
  font-weight: bold !important;
  border-radius: 6px !important;
}

/* 简单高亮类 - 最高优先级 */
.highlight-orange,
.compact-calendar-cell.highlight-orange,
.compact-data-calendar .highlight-orange,
:deep(.el-calendar-day) .highlight-orange {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 4px solid #e55a2b !important;
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.8) !important;
  color: white !important;
  font-weight: bold !important;
  border-radius: 8px !important;
  position: relative !important;
  z-index: 999 !important;
  margin: 2px !important;
  animation: highlightPulse 2s infinite !important;
}

.highlight-orange .date-text,
.compact-calendar-cell.highlight-orange .date-text,
:deep(.el-calendar-day) .highlight-orange .date-text {
  color: white !important;
  font-weight: 900 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
  font-size: 18px !important;
}

/* 特殊动画效果 */
@keyframes highlightPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.8);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 1);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.8);
  }
}

/* 强制覆盖Element Plus日历样式 */
.compact-data-calendar :deep(.el-calendar-table .el-calendar-day) {
  position: relative !important;
  padding: 0 !important;
}

/* 覆盖选中日期的默认样式 */
.compact-data-calendar :deep(.el-calendar-day.is-selected) {
  background-color: transparent !important;
}

.compact-data-calendar :deep(.el-calendar-day.is-selected .highlight-orange) {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 4px solid #e55a2b !important;
}

/* 最终的强制样式 */
.data-highlight-override {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 4px solid #e55a2b !important;
  box-shadow: 0 8px 25px rgba(255, 107, 53, 1) !important;
  color: white !important;
  font-weight: bold !important;
  border-radius: 8px !important;
  position: relative !important;
  z-index: 9999 !important;
  animation: highlightPulse 2s infinite !important;
  width: calc(100% - 8px) !important;
  height: calc(100% - 8px) !important;
  margin: 4px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 强制高亮覆盖层 */
.force-highlight-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.9) 0%, rgba(247, 147, 30, 0.9) 100%) !important;
  border: 3px solid #e55a2b !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.6) !important;
  z-index: 1 !important;
  pointer-events: none !important;
  animation: highlightPulse 2s infinite !important;
}

/* 确保文字在覆盖层之上 */
.compact-calendar-cell.has-data .date-text {
  position: relative !important;
  z-index: 2 !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* 超级明显的高亮指示器 */
.super-obvious-indicator {
  position: absolute !important;
  top: -10px !important;
  left: -10px !important;
  right: -10px !important;
  bottom: -10px !important;
  background: linear-gradient(45deg, #ff4444, #ff6600, #ff4444, #ff6600) !important;
  background-size: 400% 400% !important;
  border: 3px solid #ff0000 !important;
  border-radius: 10px !important;
  color: white !important;
  font-size: 8px !important;
  font-weight: 900 !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 9999 !important;
  animation: superBlink 1s infinite, gradientShift 2s ease infinite !important;
  box-shadow: 0 0 15px #ff4444, inset 0 0 15px rgba(255, 255, 255, 0.2) !important;
  text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff !important;
}

@keyframes superBlink {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 特殊容器的强制高亮规则 */
.has-data-highlighting .compact-calendar-cell {
  position: relative !important;
}

.has-data-highlighting .compact-calendar-cell.has-data {
  background: #ff6b35 !important;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 2px solid #e55a2b !important;
  border-radius: 6px !important;
  color: white !important;
  font-weight: bold !important;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.6) !important;
  transform: scale(1.05) !important;
  z-index: 100 !important;
}

.has-data-highlighting .compact-calendar-cell.has-data .date-text {
  color: white !important;
  font-weight: 900 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7) !important;
  font-size: 16px !important;
}

/* 最终的强制样式 - 绝对确保显示 */
.has-data-highlighting :deep(.el-calendar-day) .compact-calendar-cell.has-data {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
  border: 3px solid #e55a2b !important;
  border-radius: 8px !important;
  color: white !important;
  font-weight: bold !important;
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.8) !important;
  transform: scale(1.1) !important;
  z-index: 999 !important;
  animation: highlightPulse 2s infinite !important;
}

/* 备选方案：红色高亮 */
.compact-calendar-cell.has-data-red {
  background: linear-gradient(135deg, #f56c6c 0%, #e74c3c 100%) !important;
  border: 2px solid #c0392b !important;
  box-shadow: 0 2px 12px rgba(245, 108, 108, 0.4) !important;
}

.compact-calendar-cell.has-data-red .date-text {
  color: white !important;
  font-weight: bold !important;
}

/* 备选方案：蓝色高亮 */
.compact-calendar-cell.has-data-blue {
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%) !important;
  border: 2px solid #337ecc !important;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.4) !important;
}

.compact-calendar-cell.has-data-blue .date-text {
  color: white !important;
  font-weight: bold !important;
}



.demo-pagination-block :deep(.el-pagination) {
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.demo-pagination-block :deep(.el-pagination__total) {
  margin-right: 8px;
}

.demo-pagination-block :deep(.el-pagination__sizes) {
  margin-right: 8px;
}

.demo-pagination-block :deep(.el-pagination__jump) {
  margin-left: 8px;
}

.demo-pagination-block :deep(.el-pager) {
  margin: 0 8px;
}

/* 让条/页选择框更小 */
.demo-pagination-block :deep(.el-pagination__sizes .el-select) {
  width: 90px;
}

.demo-pagination-block :deep(.el-pagination__sizes .el-select .el-input) {
  width: 90px;
}

.demo-pagination-block :deep(.el-pagination__sizes .el-select .el-input__inner) {
  padding-left: 8px;
  padding-right: 24px;
}
</style>
