@import './variables.module.scss';

@mixin colorBtn($color) {
  background: $color;

  &:hover {
    color: $color;

    &:before,
    &:after {
      background: $color;
    }
  }
}

.blue-btn {
  @include colorBtn($blue)
}

.light-blue-btn {
  @include colorBtn($light-blue)
}

.red-btn {
  @include colorBtn($red)
}

.pink-btn {
  @include colorBtn($pink)
}

.green-btn {
  @include colorBtn($green)
}

.tiffany-btn {
  @include colorBtn($tiffany)
}

.yellow-btn {
  @include colorBtn($yellow)
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;

  &:hover {
    background: #fff;

    &:before,
    &:after {
      width: 100%;
      transition: 600ms ease all;
    }
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 2px;
    width: 0;
    transition: 400ms ease all;
  }

  &::after {
    right: inherit;
    top: inherit;
    left: 0;
    bottom: 0;
  }
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

/**
 * @description 通用按钮增强样式，提升视觉效果与现代感
 */
.el-button,
.pan-btn,
.custom-button {
  border-radius: 6px !important; /* 统一圆角为6px，.pan-btn 原有为8px，可在此调整或在 .pan-btn 中覆盖 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(0, 0, 0, 0.08); /* 更柔和、分层的阴影 */
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1); /* 更平滑的过渡动画 */
  // font-weight: 500; /* 全局已设置 font-weight: bold !important; */

  &:hover,
  &:focus {
    transform: translateY(-1px); /* 更细微的悬停上移效果 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.12); /* 悬停或聚焦时阴影加深 */
  }

  &:active {
    transform: translateY(0px); /* 点击时恢复位置 */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 点击时更细微的阴影 */
  }
}

/**
 * @description 针对 .pan-btn 的特定样式调整说明
 * .pan-btn 具有独特的悬停效果（如背景色变化和线条动画）。
 * 上述通用按钮样式（如 transform, box-shadow, border-radius, transition duration）也会默认应用于 .pan-btn。
 *
 * 当前通用样式细节:
 * - `border-radius` 设定为 6px (覆盖了 .pan-btn 原定义中的 8px)。
 * - `transition` 的持续时间和缓动函数被设定为 0.3s cubic-bezier(...) (覆盖了 .pan-btn 原定义中的 600ms ease all)。
 *
 * 如果需要为 .pan-btn 保留其原有特定属性值或进一步自定义，可以在下方 .pan-btn 规则块中进行覆盖。
 */
.pan-btn {
  /* 
    例如，如果希望 .pan-btn 恢复其原有的 8px 圆角:
    border-radius: 8px !important; 
  */
  
  /* 
    例如，如果希望 .pan-btn 的所有动画（包括背景、线条、以及通用样式引入的transform/box-shadow）
    都使用其原有的 600ms 时长和 'ease all' 缓动:
    transition: all 600ms ease all !important; 
  */

  /* 
    或者，如果仅希望 .pan-btn 的特定动画（如背景变化）使用 600ms，而 transform/box-shadow 使用通用的 0.3s，
    则需要更细致地定义 transition-property, transition-duration 等。
    .pan-btn 原有 transition: 600ms ease all; 位于其原始定义中。
    通用样式会覆盖 'all' 这个属性。
    如果想混合使用，可能需要调整 .pan-btn 原始定义中的 transition 或在此处精确指定。
  */

  /* 
    .pan-btn 在 :hover 状态下已有背景和颜色变化。
    通用样式中的 transform 和 box-shadow 会叠加生效。
    如果不希望在 .pan-btn 上应用通用样式中的 transform 效果，可以在此重置:
    &:hover,
    &:focus {
      transform: none; // 移除或更改 transform 效果
    }
  */
}
