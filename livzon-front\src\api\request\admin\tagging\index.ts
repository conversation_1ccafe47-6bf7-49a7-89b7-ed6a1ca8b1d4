import { ref, getCurrentInstance, onMounted, onBeforeUnmount, watch, nextTick, watchEffect, reactive, h, onUnmounted, computed } from "vue";
//api
import {
	query, details, edit, del, clearCache, recognition,
	dropDown, downReviewers, distribution, submit, manualAnnotationDel, shouldManualAnnotationDel,
	resultJudgmentFind, copy, copyAI, manualAnnotationDetails, publicRole, imagesDownload,
	getDateStatistics
} from "@/api/request/admin/tagging";
import { markDetails, addOrEdit, markReviewDel, fileByDel } from "@/api/request/admin/markreview";
import { ElForm, ElTable, FormInstance, TabsPaneContext } from "element-plus";
import { ElMessageBox } from 'element-plus';
import * as UTIF from 'utif';

// 常量定义
export const NEGATIVE = 'NEGATIVE';
export const POSITIVE = 'POSITIVE';
export const SUOICIOUS = 'SUOICIOUS';

//核型分类树
import { karyotypeTree } from "@/api/request/admin/karyotype";
import { useMagicKeys } from '@vueuse/core';
import { findRoleCode } from "@/api/login";
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import useAppStore from "@/store/modules/app";
const baseURL = import.meta.env.VITE_PC_BASE_PATH;
// 在文件顶部添加变量声明
const manualAnnotationType = ref<string>('');
const oneCheckType = ref<string>('');
const twoCheckType = ref<string>('');

export default () => {
	const { proxy } = getCurrentInstance() as any;
	const appStore = useAppStore();
	const url = ref<any>("");
	// 遮罩层
	const loading = ref<boolean>(true);
	// 选中数组
	const ids = ref<any>();
	// 选中数组
	const fileNames = ref<any>();
	// 非单个禁用
	const single = ref<boolean>(true);
	// 非多个禁用
	const multiple = ref<boolean>(true);
	// 显示搜索条件
	const showSearch = ref<boolean>(true);
	// 总条数
	const total = ref<number>(0);
	// 参数表格数据
	const fileList = ref<any>();
	// 弹出层标题
	const title = ref<string>("");
	// 是否显示弹出层
	const open = ref<boolean>(false);
	// 类型数据字典
	const typeOptions = ref<any>();
	// 日期范围
	const dateRange = ref<any>();
	// 查询参数
	const queryParams = ref<{
		pageNum: number;
		pageSize: number;
		fileName: string;
		fileStatus: string[];
		oneFileStatus: string[];
		twoFileStatus: string[];
		startTime: string | undefined;
		endTime: string | undefined;
		orderByColumn: string | undefined;
		isAsc: string | undefined;
		taggingPeopleId: string | undefined;
		fileIds: string | undefined;
	}>({
		pageNum: 1,
		pageSize: 10,
		fileName: '',
		fileStatus: [],
		oneFileStatus: [],
		twoFileStatus: [],
		startTime: new Date().toISOString().split('T')[0] + ' 00:00:00',
		endTime: new Date().toISOString().split('T')[0] + ' 23:59:59',
		orderByColumn: undefined,
		isAsc: undefined,
		taggingPeopleId: undefined,
		fileIds: undefined
	});
	const queryFormRef = ref<InstanceType<typeof ElForm>>();
	// 表单参数
	const form = ref<any>();
	const formRef = ref<InstanceType<typeof ElForm>>();
	const pageTableRef = ref<InstanceType<typeof ElTable>>();
	// 表单校验
	const rules = ref<any>({
		taggingPeopleId: [
			{
				required: true,
				message: "用户名称不能为空",
				trigger: "blur",
			},
		],
		name: [
			{ required: true, message: "核型名称不能为空", trigger: "blur" },
		],
		reagentManufacturer: [
			{ required: true, message: "试剂厂家不能为空", trigger: "blur" },
		],
		imagingDevice: [
			{ required: true, message: "成像设备不能为空", trigger: "blur" },
		],
		objective: [
			{ required: true, message: "物镜不能为空", trigger: "blur" },
		],
	});
	//大众、标注状态字典
	const fileStatusOptions = ref<any>();
	//一级复核状态字典
	const oneFileStatusOptions = ref<any>();
	//二级复核状态字典
	const twoFileStatusOptions = ref<any>();
	//滴度字典
	const titerStatusOptions = ref<any>();
	//√10滴度字典
	const isTiter = ref<boolean>(true);
	//状态字典
	const reagent_manufacturer = ref<any>();
	//设备字典
	const imaging_device = ref<any>();
	//设备字典
	const objective = ref<any>();
	//大众确认状态
	const public_file_status = ref<any>();
	let files = ref<any>();//用于存储文件列表的数组
	const uploadEle = ref<any>()
	//核型分类
	const karyotypeOptions = ref<any>();
	//复核开关
	const switchValue = ref<any>(true);
	// 选中行
	const selectedRow = ref<any>(null);
	// 添加上传状态变量
	const isUploading = ref(false);

	/**
	 * 标注列表项接口
	 */
	interface TaggingListVO {
		fileId: string;
		fileName: string;
		fileStatus: string;
		oneFileStatus: string;
		twoFileStatus: string;
		createDate: string;
		createUserName: string;
		taggingPeopleName: string;
	}
	/** 查询参数列表 */
	const getList = async () => {
		loading.value = true;
		// prettier-ignore
		if (queryParams.value.startTime) {
			queryParams.value.startTime = queryParams.value.startTime + ":00";
		} else {
			queryParams.value.startTime = undefined as string | undefined;
		}
		if (queryParams.value.endTime) {
			queryParams.value.endTime = queryParams.value.endTime + ":00";
		} else {
			queryParams.value.endTime = undefined as string | undefined;
		}
		await query(queryParams.value).then((response: any) => {
			fileList.value = response.data.list;
			total.value = parseInt(response.data.total);
			loading.value = false;
		});
	};

	/** 查询参数列表 */
	const getList1 = async () => {
		loading.value = true;
		// 保存当前选中的 fileId
		const currentFileId = selectedRow.value?.fileId;
		// 直接使用queryParams中的startTime和endTime
		await query(queryParams.value).then((response: any) => {
			fileList.value = response.data.list;
			total.value = parseInt(response.data.total);
			// 恢复选中状态
			if (currentFileId) {
				nextTick(() => {
					const currentRow = fileList.value.find((item: any) => item.fileId === currentFileId);
					if (currentRow) {
						// 先设置当前行
						pageTableRef.value?.setCurrentRow(currentRow);
						// 再设置选中状态
						proxy.setTableRowSelected(pageTableRef, currentRow, true);
						// 更新 selectedRow 为最新的行数据
						selectedRow.value = currentRow;
					}
				});
			}
			loading.value = false;
		});
	};
	//复核人员
	const dropDownSelect = ref<any>();
	//标签指读内容
	const oneDisabled = ref<any>(false);
	const twoDisabled = ref<any>(false);
	const threeDisabled = ref<any>(false);
	//二维码展示
	const qrcodeShow = ref<any>(false);

	// 日历相关变量
	const calendarValue = ref(new Date());
	const datesWithData = ref<string[]>([]);

	// 监听日历值变化，自动刷新数据
	watch(calendarValue, async (newDate) => {
		console.log('📅 日历值发生变化:', newDate);
		await loadDateStatistics(newDate);

		// 延迟应用标记，确保DOM更新完成
		setTimeout(() => {
			applyHighlights();
		}, 300);
	});

	//路径配置

	/** 查询核型列表 */
	const tree = async () => {
		loading.value = true;
		await karyotypeTree().then((response: any) => {
			if (response.code === 200) {
				karyotypeOptions.value = proxy.handleTree(response.data, "karyotypeId");
				loading.value = false;
			}
		});
	};

	const cleanSelect = () => {
		pageTableRef.value?.clearSelection();
		selectedRow.value = null;
	};

	const handleReset = () => {
		// 清空表单
		formItemRef.value?.resetFields?.();
		// 清空动态表单数据
		dynamicValidateForm.domains = [];
		// 清空表格选中
		cleanSelect();
		// 重置状态
		isPrimary.value = false;
		isdanger.value = false;
		resultShow.value = false;
	};

	// 添加防抖函数
	const debounce = (fn: Function, delay: number) => {
		let timer: any = null;
		return function (this: any, ...args: any[]) {
			if (timer) clearTimeout(timer);
			timer = setTimeout(() => {
				fn.apply(this, args);
			}, delay);
		};
	};

	// 修改confirm函数
	const confirm = () => {
		// 重置表单
		proxy.$refs.fileRef?.resetFields?.();
		proxy.$refs.uploadComponent?.clearFiles?.();

		// 重置标注框状态
		oneQualitativeShow.value = false;
		twoQualitativeShow.value = false;
		threeQualitativeShow.value = false;
		buttonShow.value = false;

		// 清空标注数据
		dynamicValidateForm.domains = [];
		dynamicValidateForm.markReviewDetails = [];

		// 重置其他状态
		open.value = false;
		// 重置刷新次数
		refreshCount.value = 0;
		checkAIStatus();
		// 不再调用 cleanSelect()，保持选中状态
	};

	// 添加定时器引用
	const refreshTimer = ref<number | null>(null);
	// 添加刷新次数计数器
	const refreshCount = ref(0);
	// 添加检查AI判读状态的方法
	const checkAIStatus = () => {
		// 检查是否有文件处于AI判读中状态
		const hasAIProcessing = fileList.value?.some((file: any) => file.fileStatus === 'AI_INTERPRETATION');

		if (hasAIProcessing) {
			// 如果有文件在AI判读中，刷新列表
			getList1();
			// 增加刷新次数
			refreshCount.value++;

			// 根据刷新次数设置不同的时间间隔
			if (refreshCount.value < 3) {
				// 前三次20秒一次
				if (refreshTimer.value) {
					clearInterval(refreshTimer.value);
				}
				refreshTimer.value = window.setInterval(checkAIStatus, 20000);
			} else if (refreshCount.value < 6) {
				// 中间三次30秒一次
				if (refreshTimer.value) {
					clearInterval(refreshTimer.value);
				}
				refreshTimer.value = window.setInterval(checkAIStatus, 30000);
			} else if (refreshCount.value < 9) {
				// 最后三次1分钟一次
				if (refreshTimer.value) {
					clearInterval(refreshTimer.value);
				}
				refreshTimer.value = window.setInterval(checkAIStatus, 60000);
			} else {
				// 达到最大刷新次数，清除定时器
				if (refreshTimer.value) {
					clearInterval(refreshTimer.value);
					refreshTimer.value = null;
				}
			}
		} else {
			// 如果没有文件在AI判读中，直接清除定时器
			if (refreshTimer.value) {
				clearInterval(refreshTimer.value);
				refreshTimer.value = null;
			}
		}
	};
	// 表单重置
	const reset = () => {
		form.value = {
			fileId: undefined,
			fileName: undefined,
			startTime: undefined as string | undefined,
			endTime: undefined as string | undefined,
			predictType: undefined,
			predictKaryotype: undefined,
			predictTiters: undefined,
			taggingPeopleId: undefined,
		};
		proxy.resetForm(formRef);
	};
	/** 搜索按钮操作 */
	const handleQuery = () => {
		queryParams.value.pageNum = 1;
		getList();
	};
	/** 重置按钮操作 */
	const resetQuery = () => {
		queryParams.value = {
			pageNum: 1,
			pageSize: 20,
			fileName: '',
			fileStatus: [] as string[], // 添加类型注解
			oneFileStatus: [] as string[], // 添加类型注解
			twoFileStatus: [] as string[], // 添加类型注解
			startTime: undefined as string | undefined,
			endTime: undefined as string | undefined,
			orderByColumn: undefined as string | undefined,
			isAsc: undefined as string | undefined,
			taggingPeopleId: undefined as string | undefined,
			fileIds: undefined as string | undefined
		};
		handleQuery();
	};
	// 多选框选中数据
	const handleSelectionChange = (selection: any) => {
		ids.value = selection.map((item: any) => item.fileId);
		fileNames.value = selection.map((item: any) => item.fileName);
		single.value = selection.length !== 1;
		multiple.value = !selection.length;
		// 更新 selectedRow 为最后选中的行
		if (selection.length > 0) {
			selectedRow.value = selection[selection.length - 1];
		}
	};


	//标注删除
	const removeDomain = (item: mnualAnnotationDetails) => {
		if (item.manualAnnotationId != "") {
			proxy.$modal.confirm('是否确认删除数据项?', "警告")
				.then(() => {
					return manualAnnotationDel(item.manualAnnotationId);
				})
				.then((response: any) => {
					if (response.code === 200) {
						getList();
						proxy.$modal.msgSuccess("删除成功");
						const index = dynamicValidateForm.domains.indexOf(item)
						delIndexData(index);
						setTimeout(() => {
							resultJudgmentQuery("one");
						}, 150);
					}
				})
				.catch(() => {
					console.log("取消了删除");
					cleanSelect();
				});

		} else {
			const index = dynamicValidateForm.domains.indexOf(item)
			delIndexData(index);
		}
		// 检查是否删除了最后一个核型
		if (dynamicValidateForm.domains.length === 0) {
			// 清空定性内容
			dynamicValidateForm.selectedType = undefined;
		}
	}

	function delIndexData(index: any) {
		if (index !== -1) {
			if (switchValue.value) {
				dynamicValidateForm.domains.splice(index, 1)
			} else {
				dynamicValidateForm.markReviewDetails.splice(index, 1)
			}
		}
	}
	//复核删除
	const removeMarkReview = (item: markReviewDetails, type: any) => {
		if (item.markReviewId != "") {
			proxy.$modal.confirm('是否确认删除数据项?', "警告")
				.then(() => {
					return markReviewDel(item.markReviewId);
				})
				.then((response: any) => {
					if (response.code === 200) {
						getList();
						proxy.$modal.msgSuccess("删除成功");
						const index = dynamicValidateForm.markReviewDetails.indexOf(item);
						delIndexData(index);
						setTimeout(() => {
							resultJudgmentQuery(type);
						}, 150);
					}
				})
				.catch(() => {
					cleanSelect();
					console.log("取消了删除");
				});
		} else {
			const index = dynamicValidateForm.markReviewDetails.indexOf(item);
			delIndexData(index);
		}
		// 检查是否删除了最后一个核型
		if (dynamicValidateForm.markReviewDetails.length === 0) {
			// 清空定性内容
			dynamicValidateForm.selectedOneType = undefined;
		}
	}
	//定性下拉，大众、标注、复核
	const oneQualitativeShow = ref<boolean>(false);
	const twoQualitativeShow = ref<boolean>(false);
	const threeQualitativeShow = ref<boolean>(false);
	//复制AI、标注复核结果按钮
	const oneCopyShow = ref<boolean>(true);
	//确认、取消
	const buttonShow = ref<boolean>(false);
	//增加核型
	const addShow = ref<boolean>(true);
	//按钮标题
	const oneTitle = ref<string>("不认可AI结果");
	const twoTitle = ref<string>("不认可标注结果");
	const threeTitle = ref<string>("不认可复核结果");
	//标注
	const addDomain = (type: any) => {
		if (ids.value == undefined) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}

		// 检查当前数量限制，最多允许5个核型和滴度项目
		const currentCount = type === 'one' ? dynamicValidateForm.domains.length : dynamicValidateForm.markReviewDetails.length;
		if (currentCount >= 5) {
			proxy.$modal.msgWarning("最多只能添加5个核型和滴度");
			return;
		}
		oneQualitativeShow.value = true;//定性下拉
		twoQualitativeShow.value = true;//定性下拉
		threeQualitativeShow.value = true;//定性下拉
		buttonShow.value = true;//确认、取消
		oneCopyShow.value = false;//复制AI、标注复核结果按钮
		//初始化按钮标题
		if (!isOnePositive.value) {
			oneTitle.value = "增加核型";
		} else if (!isTwoPositive.value) {
			twoTitle.value = "增加核型";
		} else if (!isThreePositive.value) {
			threeTitle.value = "增加核型";
		}

		// AI预测核型和滴度来源修正
		const aiDetails = aiForm.value && Array.isArray(aiForm.value.aiPredictKaryotypeDetailsVOS)
			? aiForm.value.aiPredictKaryotypeDetailsVOS
			: [];

		if (type === 'one' && dynamicValidateForm.selectedType && (dynamicValidateForm.selectedType == POSITIVE || dynamicValidateForm.selectedType == SUOICIOUS)) {
			// 只在 domains 为空时自动带入AI预测，防止重复累加
			if (dynamicValidateForm.domains.length === 0) {
				if (aiDetails.length > 0) {
					// 最多添加AI预测结果，确保总数不超过4个（为手动输入空行预留1个位置）
					const maxAiItems = Math.min(aiDetails.length, 4);
					for (let i = 0; i < maxAiItems; i++) {
						const item = aiDetails[i];
						const karyotypeId = findKaryotypeIdByLabel(item.predictKaryotype, karyotypeOptions.value || []) || "";
						const titerValue = findTiterValueByLabel(item.predictTiters, titerStatusOptions.value || []);
						dynamicValidateForm.domains.push({
							manualAnnotationId: "",
							fileId: manualAnnotationForm.value.fileId,
							karyotype: karyotypeId,
							titer: titerValue,
						});
					}
				}
			}
			// 只有在总数少于5个时才添加空行用于手动输入
			if (dynamicValidateForm.domains.length < 5) {
				dynamicValidateForm.domains.push({
					manualAnnotationId: "",
					fileId: manualAnnotationForm.value.fileId,
					karyotype: "",
					titer: "",
				});
			}
			oneTitle.value = "增加核型";
		} else if ((type === 'two' && dynamicValidateForm.selectedOneType && (dynamicValidateForm.selectedOneType == POSITIVE || dynamicValidateForm.selectedOneType == SUOICIOUS))
			|| (type === 'three' && dynamicValidateForm.selectedTwoType && (dynamicValidateForm.selectedTwoType == POSITIVE || dynamicValidateForm.selectedOneType == SUOICIOUS))) {
			// 只在 markReviewDetails 为空时自动带入AI预测，防止重复累加
			if (dynamicValidateForm.markReviewDetails.length === 0) {
				if (aiDetails.length > 0) {
					// 最多添加AI预测结果，确保总数不超过4个（为手动输入空行预留1个位置）
					const maxAiItems = Math.min(aiDetails.length, 4);
					for (let i = 0; i < maxAiItems; i++) {
						const item = aiDetails[i];
						const karyotypeId = findKaryotypeIdByLabel(item.predictKaryotype, karyotypeOptions.value || []) || "";
						const titerValue = findTiterValueByLabel(item.predictTiters, titerStatusOptions.value || []);
						dynamicValidateForm.markReviewDetails.push({
							markReviewId: "",
							fileId: manualAnnotationForm.value.fileId,
							annotationMethod: "",
							karyotype: karyotypeId,
							titer: titerValue,
						});
					}
				}
			}
			// 只有在总数少于5个时才添加空行用于手动输入
			if (dynamicValidateForm.markReviewDetails.length < 5) {
				dynamicValidateForm.markReviewDetails.push({
					markReviewId: "",
					fileId: manualAnnotationForm.value.fileId,
					annotationMethod: "",
					karyotype: "",
					titer: "",
				});
			}
			twoTitle.value = "增加核型";
			threeTitle.value = "增加核型";
		} else {
			addShow.value = false;
		}
		// 打开指读
		oneDisabled.value = false;
		// 打开指读
		twoDisabled.value = false;
		// 打开指读
		threeDisabled.value = false;
	}

	const submitForm = (formEl: FormInstance | undefined, submitType: any) => {
		if (manualAnnotationForm.value.fileId === undefined) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}
		loading.value = true;
		if (!formEl) return
		formEl.validate((valid) => {
			if (valid) {
				interface EditBO {
					fileId: string;
					manualAnnotationType: 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
					oneCheckType: 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
					twoCheckType: 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
					isTiter: boolean;
				}

				let editBO: EditBO = {
					fileId: manualAnnotationForm.value.fileId,
					manualAnnotationType: undefined,
					oneCheckType: undefined,
					twoCheckType: undefined,
					isTiter: isTiter.value,
				}
				switch (submitType) {
					case "one":
						editBO.manualAnnotationType = dynamicValidateForm.selectedType;
						//如果定性为阴性，则删除改文件下的标注结果
						if (dynamicValidateForm.selectedType === 'NEGATIVE') {
							shouldManualAnnotationDel(manualAnnotationForm.value.fileId).then((response: any) => {
								if (response.code === 200) {
									proxy.$modal.msgSuccess("结果上传成功");
								}
							})
						} else {
							dynamicValidateForm.domains.forEach((f: any) => {
								f.remark = dynamicValidateForm.remark;
							})
							//标注
							submit(dynamicValidateForm.domains).then((response: any) => {
								if (response.code === 200) {
									proxy.$modal.msgSuccess("结果上传成功");
									//重置标注内容
									let manualAnnotation = response.data;
									if (manualAnnotation.length > 0) {
										dynamicValidateForm.domains = [];
										manualAnnotation.forEach((manualAnnotation: any) => {
											dynamicValidateForm.domains.push({
												manualAnnotationId: manualAnnotation.manualAnnotationId,
												karyotype: manualAnnotation.karyotype,
												fileId: manualAnnotation.fileId,
												titer: manualAnnotation.titer,
												remark: manualAnnotation.remark,
											});
										})
									}
								}
							});
						}
						oneQualitativeShow.value = true;//定性下拉
						break;
					case "two":
					case "three":
						let annotationMethod: any = "ONE_CHECK";
						editBO.oneCheckType = buttonPlain(isOnePrimary.value);
						editBO.twoCheckType = buttonPlain(isTwoPrimary.value);
						if (submitType === "two") {
							editBO.oneCheckType = dynamicValidateForm.selectedOneType;
						} else if (submitType === "three") {
							editBO.twoCheckType = dynamicValidateForm.selectedTwoType;
							annotationMethod = "TWO_CHECK";
						}
						dynamicValidateForm.markReviewDetails.forEach((f: any) => {
							f.remark = dynamicValidateForm.remark;
							f.annotationMethod = annotationMethod;
						})
						//如果定性为阴性，则删除改文件下的复核结果
						if (dynamicValidateForm.selectedType === 'NEGATIVE') {
							fileByDel(manualAnnotationForm.value.fileId, submitType.toLowerCase()).then((response: any) => {
								if (response.code === 200) {
									proxy.$modal.msgSuccess("结果上传成功");
								}
							})
						} else {
							addOrEdit(dynamicValidateForm.markReviewDetails).then((response: any) => {
								if (response.code === 200) {
									proxy.$modal.msgSuccess("复核成功");
									//重置复核内容
									let markReview = response.data;
									if (markReview.length > 0) {
										dynamicValidateForm.markReviewDetails = [];
										markReview.forEach((m: any) => {
											dynamicValidateForm.markReviewDetails.push({
												markReviewId: m.markReviewId,
												annotationMethod: m.annotationMethod,
												karyotype: m.karyotype,
												fileId: m.fileId,
												titer: m.titer,
												remark: m.remark,
											});
										})
									}
								}
							});
						}
						twoQualitativeShow.value = true;//定性下拉
						threeQualitativeShow.value = true;//定性下拉
						break;
				}
				addShow.value = true;
				buttonShow.value = false;//确认、取消
				oneTitle.value = "修改结果";
				oneDisabled.value = true;
				twoTitle.value = "修改结果";
				twoDisabled.value = true;
				threeTitle.value = "修改结果";
				threeDisabled.value = true;
				edit(editBO);
			}
		})
		// 增加延迟时间到 300ms，确保状态更新完成
		setTimeout(() => {
			resultJudgmentQuery(submitType);
			// 在状态查询完成后再次刷新列表
			setTimeout(() => {
				getList1();
			}, 150);
		}, 300);
		loading.value = false;
	}
	function buttonPlain(primary: any) {
		let result: any = 'NEGATIVE';
		if (primary) {
			result = 'POSITIVE';
		}
		return result;
	}
	const resetForm = (formEl: FormInstance | undefined) => {
		if (formEl) {
			// 保存阴阳性下拉框的值
			const selectedType = dynamicValidateForm.selectedType;
			const selectedOneType = dynamicValidateForm.selectedOneType;
			const selectedTwoType = dynamicValidateForm.selectedTwoType;

			// 清空表单验证
			formEl.resetFields();


			// 恢复阴阳性下拉框的值
			dynamicValidateForm.selectedType = selectedType;
			dynamicValidateForm.selectedOneType = selectedOneType;
			dynamicValidateForm.selectedTwoType = selectedTwoType;

			// 重置状态
			isPrimary.value = false;
			isdanger.value = true;
			isOnePrimary.value = false;
			isOneDanger.value = true;
			isTwoPrimary.value = false;
			isTwoDanger.value = true;
			if (isOnePositive.value && dynamicValidateForm.selectedType && dynamicValidateForm.selectedType == POSITIVE) {
				oneQualitativeShow.value = true;//定性下拉
			} else if (isTwoPositive.value && dynamicValidateForm.selectedOneType && dynamicValidateForm.selectedOneType == POSITIVE) {
				twoQualitativeShow.value = true;//定性下拉
			} else if (isThreePositive.value && dynamicValidateForm.selectedTwoType && dynamicValidateForm.selectedTwoType == POSITIVE) {
				threeQualitativeShow.value = true;//定性下拉
			} else {
				oneQualitativeShow.value = false;//定性下拉
				twoQualitativeShow.value = false;//定性下拉
				threeQualitativeShow.value = false;//定性下拉
			}

			buttonShow.value = false;//确认、取消
			addShow.value = true;//增加核型

			// 只清空未填写内容的下拉框
			dynamicValidateForm.domains = dynamicValidateForm.domains.filter(domain =>
				domain.karyotype && domain.titer
			);
			dynamicValidateForm.markReviewDetails = dynamicValidateForm.markReviewDetails.filter(detail =>
				detail.karyotype && detail.titer
			);
			//初始化按钮标题
			if (isOnePositive.value) {
				oneTitle.value = "修改结果";
				oneDisabled.value = true;
			} else if (isTwoPositive.value) {
				twoTitle.value = "修改结果";
				twoDisabled.value = true;
			} else if (isThreePositive.value) {
				threeTitle.value = "修改结果";
				threeDisabled.value = true;
			} else {
				oneTitle.value = "不认可AI结果";
				twoTitle.value = "不认可标注结果";
				threeTitle.value = "不认可复核结果";
				oneCopyShow.value = true;//复制AI、标注复核结果按钮
			}

		}
	};
	/** 删除按钮操作 */
	const handleDelete = (row: any) => {
		if (ids.value == undefined) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}
		const fileIds = row.fileId || ids.value;
		const names = row.fileName || fileNames.value;
		proxy.setTableRowSelected(pageTableRef, row, true);
		// prettier-ignore
		proxy.$modal.confirm('是否确认删除为"' + names + '"的数据项?', "警告")
			.then(() => {
				return del(fileIds);
			})
			.then((response: any) => {
				if (response.code === 200) {
					getList();
					newTime.value = Date.now();
					proxy.$modal.msgSuccess("删除成功");
					// 清空画布相关数据
					url.value = "";
					image.value = null;
					scale.value = 1;
					brightness.value = 100;
					offsetX.value = 0;
					offsetY.value = 0;
					// 清空标注数据
					dynamicValidateForm.domains = [];
					dynamicValidateForm.markReviewDetails = [];
					dynamicValidateForm.selectedType = undefined;
					dynamicValidateForm.selectedOneType = undefined;
					dynamicValidateForm.selectedTwoType = undefined;
					// 重置状态
					oneQualitativeShow.value = false;
					twoQualitativeShow.value = false;
					threeQualitativeShow.value = false;
					buttonShow.value = false;
					addShow.value = true;
					oneCopyShow.value = true;
					// 重置按钮标题
					oneTitle.value = "不认可AI结果";
					twoTitle.value = "不认可标注结果";
					threeTitle.value = "不认可复核结果";
					// 重置指读状态
					oneDisabled.value = false;
					twoDisabled.value = false;
					threeDisabled.value = false;
					// 重置阳性状态
					isOnePositive.value = false;
					isTwoPositive.value = false;
					isThreePositive.value = false;
					// 重置文件名和二维码
					imageInfo.value = "";
					config.value = null;
					qrcodeShow.value = false;
					// 重置还原按钮状态
					isPrimary.value = false;
					isdanger.value = true;
					isOnePrimary.value = false;
					isOneDanger.value = true;
					isTwoPrimary.value = false;
					isTwoDanger.value = true;
					// 重置选中行
					selectedRow.value = null;
					// 重置文件ID
					fileId.value = null;
					// 重置结果显示
					resultShow.value = true;
				}
			})
			.catch(() => {
				cleanSelect();
				console.log("取消了删除");
			});
	};
	/** 导出按钮操作 */
	const handleExport = () => {
		if (!ids.value || ids.value.length === 0) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}

		// 导出弹窗输入框和复选框响应式变量
		const inputRef = ref('');

		// 默认文件名
		const now = new Date();
		const defaultFileName = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
		inputRef.value = defaultFileName;
		ElMessageBox({
			title: '数据导出：xlsx格式',
			message: h('div', { class: 'export-dialog' }, [
				h('div', {
					class: 'input-wrapper',
					style: `
						display: flex;
						align-items: center;
						gap: 6px;
						margin-bottom: 12px;
					`
				}, [
					h('div', {
						style: `
							font-size: 14px;
							color: #606266;
							white-space: nowrap;
						`
					}, '导出文件名称'),
					h('input', {
						type: 'text',
						value: inputRef.value,
						onInput: (e: Event) => {
							inputRef.value = (e.target as HTMLInputElement).value;
						},
						placeholder: '请输入导出文件名称',
						style: `
							flex: 1;
							height: 38px;
							font-size: 16px;
							border: 1.5px solid #d0d7de;
							border-radius: 6px;
							background: #f8fafc;
							color: #222;
							padding: 0 12px;
							box-sizing: border-box;
							transition: border-color 0.2s, box-shadow 0.2s;
							outline: none;
						`
					})
				])
			]),
			showCancelButton: true,
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			beforeClose: (action, instance, done) => {
				if (action === 'confirm') {
					if (!/^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/.test(inputRef.value)) {
						ElMessageBox.alert('文件名只能包含中文、英文、数字、下划线和横线', '提示', {
							confirmButtonText: '确定',
							type: 'error',
							customClass: 'custom-error-message'
						});
						return;
					}
				}
				done();
			}
		}).then((action) => {
			if (action === 'confirm') {
				try {
					const fileName = inputRef.value ? `${inputRef.value}.xlsx` : `${defaultFileName}.xlsx`;

					// 确保 fileIds 是数组
					const fileIds = Array.isArray(ids.value) ? ids.value : [ids.value];

					proxy.download(
						'/file/export',
						{ fileIds, isTiter: isTiter.value },
						fileName,
					);
				} catch (error) {
					console.error('导出失败:', error);
					proxy.$modal.msgError("导出失败，请重试");
				}
			}
		}).catch((error) => {
			console.error('导出操作取消或出错:', error);
			cleanSelect();
		});
	};
	/** 清理缓存按钮操作 */
	const handleClearCache = () => {
		clearCache().then((response: any) => {
			if (response.code === 200) {
				proxy.$modal.msgSuccess("清理成功");
			}
		});
	};

	/** 上传 */
	const handleUpload = () => {
		title.value = "上传";
		open.value = true;
	};
	let uploadSuccess = (item: any, itemList: any) => {
		files.value = itemList;
	};

	const keys = useMagicKeys({
		passive: false,
		onEventFired(e) {
			return false
		}
	})

	// 添加键盘事件处理函数
	const handleKeyDown = (e: KeyboardEvent) => {
		if (!fileList.value || fileList.value.length === 0) return;

		const currentIndex = fileList.value.findIndex((item: any) => item.fileId === fileId.value);
		if (currentIndex === -1) return;

		let nextIndex: number;

		if (e.key === 'ArrowUp') {
			nextIndex = Math.max(0, currentIndex - 1);
		} else if (e.key === 'ArrowDown') {
			nextIndex = Math.min(fileList.value.length - 1, currentIndex + 1);
		} else {
			return;
		}

		// 清除所有选中状态
		pageTableRef.value?.clearSelection();

		// 设置新的选中行
		const nextRow = fileList.value[nextIndex];
		selectedRow.value = nextRow;

		// 设置新的复选框选中状态
		proxy.setTableRowSelected(pageTableRef, nextRow, true);

		// 自动滚动到该行（Element Plus 官方方法）
		pageTableRef.value?.scrollTo(nextIndex);

		// 设置高亮背景色
		nextTick(() => {
			const rows = pageTableRef.value?.$el.querySelectorAll('.el-table__row');
			if (rows && rows[nextIndex]) {
				rows.forEach((row: any) => row.classList.remove('current-row'));
				rows[nextIndex].classList.add('current-row');
				// 强制滚动到可见区域中央，保证视觉体验
				rows[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
			}
		});

		// 加载行数据
		rowClick(nextRow);
	};

	onMounted(() => {
		dropDown().then((res: any) => {
			if (res.code === 200) {
				dropDownSelect.value = res.data;
			}
		})
		getList();
		check();
		tree();
		//大众、标注文件状态
		proxy.getDicts("file_status").then((response: any) => {
			if (response.code === 200) {
				fileStatusOptions.value = response.data;
			}
		});
		//一级复核文件状态
		proxy.getDicts("one_file_status").then((response: any) => {
			if (response.code === 200) {
				oneFileStatusOptions.value = response.data;
			}
		});
		//二级复核文件状态
		proxy.getDicts("two_file_status").then((response: any) => {
			if (response.code === 200) {
				twoFileStatusOptions.value = response.data;
			}
		});

		// 延迟弹出滴度选择对话框，确保页面完全加载
		setTimeout(() => {
			showTiterSelectionDialog();
		}, 500);

		//试剂厂家
		proxy.getDicts("REAGENT_MANUFACTURER").then((response: any) => {
			if (response.code === 200) {
				reagent_manufacturer.value = response.data;
			}
		});
		//显微镜
		proxy.getDicts("IMAGING_DEVICE").then((response: any) => {
			if (response.code === 200) {
				imaging_device.value = response.data;
			}
		});
		//物镜
		proxy.getDicts("OBJECTIVE").then((response: any) => {
			if (response.code === 200) {
				objective.value = response.data;
			}
		});
		//大众确认状态
		proxy.getDicts("PUBLIC_FILE_STATUS").then((response: any) => {
			if (response.code === 200) {
				public_file_status.value = response.data;
			}
		});
		const ctrls = keys['Space']
		watch(ctrls, (v) => {
			if (v && buttonShow.value) {
				submitForm(formItemRef.value, 'one');
			}
		});
		//加载图片
		loadImage();
		//加载大众角色
		isPublicRole();

		// 初始化日历数据
		const now = new Date();
		loadDateStatistics(now);

		// 添加键盘事件监听
		window.addEventListener('keydown', handleKeyDown);
	});

	// 修改onUnmounted钩子
	onUnmounted(() => {
		// 清理定时器
		if (refreshTimer.value) {
			clearInterval(refreshTimer.value);
			refreshTimer.value = null;
		}
		// 重置刷新次数
		refreshCount.value = 0;
		// 移除键盘事件监听
		window.removeEventListener('keydown', handleKeyDown);
	});
	//大众角色
	const isPublic = ref<Boolean>(false);
	function isPublicRole() {
		publicRole().then((res: any) => {
			if (res.code == 200 && res.data != null) {
				// 只有当用户只有一个角色且是 public 时才隐藏内容
				isPublic.value = res.data;
			}
		})
	};

	const oneCheck = ref<any>(false);
	const twoCheck = ref<any>(false);
	//初始话一级复核/二级复核
	async function check() {
		await findRoleCode().then((res: any) => {
			let role = res.data;
			if (res.code == 200 && role != null) {
				//如果二级复核都为true
				if ("two_check" === role || "admin" === role) {
					twoCheck.value = true;
				} else if ("one_check" === role) {
					oneCheck.value = true;
				}

			}
		})
	};

	/** 单行点击 */
	//AI表单参数
	const aiForm = ref<any>({});
	const aiFormRef = ref<InstanceType<typeof ElForm>>();
	//人工单参数
	const formItemRef = ref<FormInstance>()
	/**
	 * 动态表单数据结构
	 */
	const dynamicValidateForm = reactive<{
		domains: mnualAnnotationDetails[]; // 人工标注
		markReviewDetails: markReviewDetails[]; // 复核
		remark: string; // 备注
		selectedType: 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
		selectedOneType: 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
		selectedTwoType: 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
	}>({
		domains: [],
		markReviewDetails: [],
		remark: '',
		selectedType: undefined,
		selectedOneType: undefined,
		selectedTwoType: undefined,
	})

	interface mnualAnnotationDetails {
		manualAnnotationId: String
		fileId: String
		karyotype: String
		titer: String
		karyotypeValue?: String
		titerValue?: String
		objective?: String
		remark?: String
	}

	interface markReviewDetails {
		markReviewId: String
		annotationMethod: String
		fileId: String
		karyotype: String
		titer: String
		karyotypeValue?: String
		titerValue?: String
		remark?: String
	}
	const manualAnnotationForm = ref<any>({});
	const fileId = ref<any>();
	const type = ref<any>();
	const config = ref<string | null>()
	const resultShow = ref<any>(true);
	const imageInfo = ref<any>();
	//如果后台阳性有值，为true，否则为false
	const isOnePositive = ref<boolean>(false);
	const isTwoPositive = ref<boolean>(false);
	const isThreePositive = ref<boolean>(false);
	const rowClick = async (row: any) => {
		// 先清除所有选中状态
		pageTableRef.value?.clearSelection();
		// 设置新的选中行
		selectedRow.value = row;
		// 设置当前行的复选框选中
		proxy.setTableRowSelected(pageTableRef, row, true);
		dynamicValidateForm.domains = [];
		fileId.value = row.fileId;
		config.value = baseURL + `/share?fileId=${row.fileId}`
		//详情
		loading.value = true;
		await details(row.fileId, isTiter.value).then((response: any) => {
			if (response.code === 200) {
				//圖片信息
				imageInfo.value = "🧪文件名称：" + response.data.fileName;
				//图片
				url.value = row.filePath;
				//ai预测
				aiForm.value = response.data;
				//人工标注
				manualAnnotationForm.value = response.data;
				if (response.data.manualAnnotationDetailsVOList.length > 0) {
					response.data.manualAnnotationDetailsVOList.forEach((manualAnnotationDetails: any) => {
						dynamicValidateForm.domains.push({
							manualAnnotationId: manualAnnotationDetails.manualAnnotationId,
							karyotype: manualAnnotationDetails.karyotype,
							fileId: manualAnnotationDetails.fileId,
							titer: manualAnnotationDetails.titer,
							objective: manualAnnotationDetails.objective,
						});
					})
					dynamicValidateForm.remark = response.data.manualAnnotationDetailsVOList[0].remark;
				}
				//初始化阴阳性按钮
				manualAnnotationType.value = response.data.manualAnnotationType;
				oneCheckType.value = response.data.oneCheckType;
				twoCheckType.value = response.data.twoCheckType;
				if (!oneCheck.value && !twoCheck.value && response.data.manualAnnotationType) {
					dynamicValidateForm.selectedType = response.data.manualAnnotationType;
					oneQualitativeShow.value = true;//定性下拉
					isOnePositive.value = true;
					oneTitle.value = "修改结果";
					oneCopyShow.value = false;
					oneDisabled.value = true;
				} else if (!oneCheck.value && !twoCheck.value && response.data.manualAnnotationType == null) {
					dynamicValidateForm.selectedType = undefined;
					oneQualitativeShow.value = false;
					oneTitle.value = "不认可AI结果";
					oneCopyShow.value = true;
					isOnePositive.value = false;
					oneDisabled.value = false;
				} else if (oneCheck.value && response.data.oneCheckType) {
					dynamicValidateForm.selectedOneType = response.data.oneCheckType;
					twoQualitativeShow.value = true;//定性下拉
					isTwoPositive.value = true;
					twoTitle.value = "修改结果";
					oneCopyShow.value = false;
					twoDisabled.value = true;
				} else if (oneCheck.value && response.data.oneCheckType == null) {
					dynamicValidateForm.selectedOneType = undefined;
					twoQualitativeShow.value = false;
					twoTitle.value = "不认可标注结果";
					oneCopyShow.value = true;
					isTwoPositive.value = false;
					twoDisabled.value = false;
				} else if (twoCheck.value && response.data.twoCheckType) {
					dynamicValidateForm.selectedTwoType = response.data.twoCheckType;
					threeQualitativeShow.value = true;//定性下拉
					isThreePositive.value = true;
					threeTitle.value = "修改结果";
					oneCopyShow.value = false;
					threeDisabled.value = true;
				} else if (twoCheck.value && response.data.twoCheckType == null) {
					dynamicValidateForm.selectedTwoType = undefined;
					threeQualitativeShow.value = false;
					threeTitle.value = "不认可复核结果";
					oneCopyShow.value = true;
					// 重置阳性状态
					isThreePositive.value = false;
					// 重置指读状态
					threeDisabled.value = false;
				}

				// 重置按钮状态
				buttonShow.value = false;
				// 重置增加核型状态
				addShow.value = true;
				let param = {
					fileId: fileId.value,
					annotationMethod: "",
				}
				let type = "one";
				//初始话标签选中
				if (twoCheck.value) {
					activeName.value = 'three'
					param.annotationMethod = response.data.twoCheckType == null ? "" : "TWO_CHECK"
					oneDisabled.value = true;
					twoDisabled.value = true;
					drawerDetails(param);
					type = "three";
					//添加内容控制
					switchValue.value = false;
				} else if (oneCheck.value && !twoCheck.value) {
					param.annotationMethod = response.data.oneCheckType == null ? "" : "ONE_CHECK";
					activeName.value = 'two';
					oneDisabled.value = true;
					drawerDetails(param);
					type = "two";
					//添加内容控制
					switchValue.value = false;
				}
				//比较ai、标注、一级复核、二级复核结果
				resultJudgmentQuery(type);
			}
		});
		loadImage();
		//二维码展示
		qrcodeShow.value = true;
		loading.value = false;
	};



	const switchButton = (type: any) => {

		isPrimary.value = false;
		isdanger.value = true;
		if (type.toLowerCase() == "positive") {
			isPrimary.value = true;
			isdanger.value = false;
		}
	};
	// 阴阳性选择值
	const selectedType = ref<String>('');
	const selectedOneType = ref<String>('');
	const selectedTwoType = ref<String>('');

	//阴性阳性切换
	const isPrimary = ref(false);
	const isdanger = ref(true);
	const isOnePrimary = ref(false);
	const isOneDanger = ref(true);
	const isTwoPrimary = ref(false);
	const isTwoDanger = ref(true);

	const handleChange = (val: String, type: String) => {
		switch (type) {
			case 'one':	//标注、大众
				dynamicValidateForm.selectedType = val as 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
				if (val === POSITIVE || val === SUOICIOUS) {
					addDomain('one');
					addShow.value = true;
				} else if (val === NEGATIVE) {
					// 清空记录
					dynamicValidateForm.domains = [];
					addShow.value = false;//增加核型
					buttonShow.value = true;//确认、取消
					oneCopyShow.value = false;//复制AI、标注复核结果按钮
				}
				break;
			case 'two':	//一级复核
				dynamicValidateForm.selectedOneType = val as 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
				if (val === POSITIVE || val === SUOICIOUS) {
					addDomain('two');
					addShow.value = true;
				} else if (val === NEGATIVE) {
					// 清空记录
					dynamicValidateForm.markReviewDetails = [];
					addShow.value = false;//增加核型
					buttonShow.value = true;//确认、取消
					oneCopyShow.value = false;//复制AI、标注复核结果按钮
				}
				break;
			case 'three'://二级复核
				dynamicValidateForm.selectedTwoType = val as 'NEGATIVE' | 'POSITIVE' | 'SUOICIOUS' | undefined;
				if (val === POSITIVE || val === SUOICIOUS) {
					addDomain('three');
					addShow.value = true;
				} else if (val === NEGATIVE) {
					// 清空记录
					dynamicValidateForm.markReviewDetails = [];
					addShow.value = false;//增加核型
					buttonShow.value = true;//确认、取消
					oneCopyShow.value = false;//复制AI、标注复核结果按钮
				}
				break;
		}
	}

	const handleOneChange = (val: String) => {
		selectedOneType.value = val;
		if (val === POSITIVE) {
			isOnePrimary.value = true;
			isOneDanger.value = false;
		} else {
			isOnePrimary.value = false;
			isOneDanger.value = true;
		}


	}

	const handleTwoChange = (val: String) => {
		selectedTwoType.value = val;
		if (val === POSITIVE) {
			isTwoPrimary.value = true;
			isTwoDanger.value = false;
		} else {
			isTwoPrimary.value = false;
			isTwoDanger.value = true;
		}


	}

	/** AI识别按钮操作，识别后恢复所有原本选中的复选框
	 * @param {any} row 当前操作的行
	 */
	const handleRecognition = async (row: any) => {
		if (ids.value != undefined && ids.value.length > 100) {
			proxy.$modal.msgError("识别不能超过100条，请检查");
			return;
		}

		// 保存当前选中的行
		const currentSelectedRow = selectedRow.value || row;
		if (!currentSelectedRow?.fileId) {
			proxy.$modal.msgError("请选择需要识别的数据");
			return;
		}

		// 保存当前所有选中的fileIds
		const selectedFileIds = ids.value ? [...ids.value] : [];

		let recognitionNum = Array.isArray(ids.value) ? ids.value.length : 1;
		proxy.$modal.confirm('是否确认识别[' + recognitionNum + ']条数据?', "提示")
			.then(async () => {
				loading.value = true;
				try {
					await recognition(ids.value);
					await getList();

					// 等待数据加载完成
					await nextTick();

					// 先清空所有选中，再恢复
					if (selectedFileIds.length > 0) {
						pageTableRef.value?.clearSelection();
						selectedFileIds.forEach(fileId => {
							const row = fileList.value?.find((item: any) => item.fileId === fileId);
							if (row) {
								pageTableRef.value?.toggleRowSelection(row, true);
							}
						});
					}

					// 更新 selectedRow 为最后选中的行
					// const lastSelectedRow = fileList.value?.find((item: any) => item.fileId === currentSelectedRow.fileId);
					// if (lastSelectedRow) {
					// 	selectedRow.value = lastSelectedRow;
					// 	// 加载行数据
					// 	await rowClick(lastSelectedRow);
					// } else {
					// 	proxy.$modal.msgError("无法找到更新后的数据");
					// }
					proxy.$modal.msgSuccess("识别成功");
				} catch (error) {
					console.error("识别过程出错:", error);
					proxy.$modal.msgError("识别过程出错");
				}
			})
			.catch(() => {
				console.log("取消了识别");
			}).finally(() => {
				loading.value = false;
			});
	};
	// 表单参数
	const formDetails = ref<any>({});
	// 是否显示详细弹出层
	const openView = ref<boolean>(false);
	//双击事件详情
	const rowDblClick = async (row: any) => {
		// 设置当前选中行
		selectedRow.value = row;
		// 确保有fileId
		if (!row?.fileId) {
			proxy.$modal.msgError("请选择需要查看的数据");
			return;
		}
		openView.value = true;
		//详情
		await details(row.fileId, isTiter.value).then((response: any) => {
			if (response.code === 200) {
				formDetails.value = response.data;
			}
		});
	};



	interface Tree {
		label: string;
		children?: Tree[];
	}

	const fileRef = ref<InstanceType<typeof ElForm>>();
	const uploadComponent = ref<any>();
	const uploadForm = ref<any>({
		reagentManufacturer: undefined,
		imagingDevice: undefined,
		objective: undefined,
	});


	//分配
	const openDistribution = ref<any>(false);
	const formDistribution = ref<any>({
		taggingPeopleId: undefined,
	});
	const downReviewersSelect = ref<any>();
	const distributionRef = ref<InstanceType<typeof ElForm>>();
	const handleDistribution = async () => {
		if (ids.value == undefined || ids.value.length === 0) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}
		title.value = "分配";
		openDistribution.value = true;
		//下拉复核人员
		await downReviewers().then((res: any) => {
			if (res.code === 200) {
				downReviewersSelect.value = res.data;
			}
		})
	}

	const submitDistribution = () => {
		distributionRef.value?.validate(async (valid: boolean) => {
			if (valid) {
				let param = {
					fileId: [],
					taggingPeopleId: undefined,
				}
				param.fileId = ids.value;
				param.taggingPeopleId = formDistribution.value.taggingPeopleId;
				loading.value = true;
				await distribution(param).then((res: any) => {
					if (res.code === 200) {
						getList();
						proxy.$modal.msgSuccess("分配成功");
					}
				}).finally(() => {
					loading.value = false;
					openDistribution.value = false;
				});
			}
		});
	};


	const imageCanvasRef = ref<any>();
	const brightness = ref<any>(60);
	const scale = ref<any>(1);
	const image = ref<HTMLImageElement | null>(null);
	const originalWidth = ref<any>();
	const originalHeight = ref<any>();
	const centerX = ref<any>();
	const centerY = ref<any>();
	const originalCenterX = ref<any>(); // Store original center position
	const originalCenterY = ref<any>(); // Store original center position
	const offsetX = ref<any>(0); // Offset for dragging
	const offsetY = ref<any>(0); // Offset for dragging
	const isDragging = ref<any>(false);
	const lastX = ref<any>();
	const lastY = ref<any>();
	const width = ref<any>(950);

	// 添加触摸相关的变量
	const touchStartDistance = ref(0);
	const initialScale = ref(1);
	const touchStartX = ref(0);
	const touchStartY = ref(0);
	const isTouchDragging = ref(false);

	// 修改触摸事件处理函数
	function handleTouchStart(e: TouchEvent) {
		if (e.touches.length === 2) {
			// 双指触摸 - 缩放
			touchStartDistance.value = Math.hypot(
				e.touches[0].pageX - e.touches[1].pageX,
				e.touches[0].pageY - e.touches[1].pageY
			);
			initialScale.value = scale.value;
		} else if (e.touches.length === 1) {
			// 单指触摸 - 移动
			isTouchDragging.value = true;
			touchStartX.value = e.touches[0].pageX;
			touchStartY.value = e.touches[0].pageY;
		}
	}

	function handleTouchMove(e: TouchEvent) {
		if (e.touches.length === 2) {
			// 双指移动 - 缩放
			e.preventDefault();
			const currentDistance = Math.hypot(
				e.touches[0].pageX - e.touches[1].pageX,
				e.touches[0].pageY - e.touches[1].pageY
			);
			const newScale = initialScale.value * (currentDistance / touchStartDistance.value);
			scale.value = Math.min(Math.max(newScale, 0.5), 3); // 限制缩放范围在0.5到3之间
			updateCanvas();
		} else if (e.touches.length === 1 && isTouchDragging.value) {
			// 单指移动 - 拖动
			e.preventDefault();
			const currentX = e.touches[0].pageX;
			const currentY = e.touches[0].pageY;

			offsetX.value += currentX - touchStartX.value;
			offsetY.value += currentY - touchStartY.value;

			touchStartX.value = currentX;
			touchStartY.value = currentY;

			updateCanvas();
		}
	}

	function handleTouchEnd() {
		touchStartDistance.value = 0;
		initialScale.value = 1;
		isTouchDragging.value = false;
		touchStartX.value = 0;
		touchStartY.value = 0;
	}

	const imageLoading = ref(false); // 添加图片加载状态

	// 添加图片缓存
	const imageCache = new Map<string, HTMLImageElement>();

	// 添加图片预加载队列
	const preloadQueue = ref<Set<string>>(new Set());
	const isPreloading = ref(false);

	// 优化预加载函数
	const preloadImage = async (url: string): Promise<HTMLImageElement> => {
		return new Promise((resolve, reject) => {
			if (imageCache.has(url)) {
				resolve(imageCache.get(url)!);
				return;
			}

			const img = new Image();
			img.onload = () => {
				imageCache.set(url, img);
				resolve(img);
			};
			img.onerror = reject;
			img.src = url;
		});
	};

	// 添加渐进式加载函数
	const progressiveLoad = async (url: string): Promise<HTMLImageElement> => {
		return new Promise((resolve, reject) => {
			const img = new Image();
			let loaded = false;

			// 先加载低质量版本
			const lowQualityUrl = `${url}?quality=20`;
			const lowQualityImg = new Image();

			lowQualityImg.onload = () => {
				if (!loaded) {
					image.value = lowQualityImg;
					updateCanvas();
				}
			};

			lowQualityImg.src = lowQualityUrl;

			// 然后加载高质量版本
			img.onload = () => {
				loaded = true;
				image.value = img;
				imageCache.set(url, img);
				updateCanvas();
				resolve(img);
			};

			img.onerror = reject;
			img.src = url;
		});
	};

	// 修改loadImage函数
	async function loadImage() {
		if (!url.value) return;
		imageLoading.value = true;

		// 完全清理旧图片状态
		if (image.value) {
			image.value.onload = null;
			image.value.onerror = null;
			image.value.src = '';
			image.value = null;
		}

		// 重置画布状态
		scale.value = 0.4;
		brightness.value = 100;
		offsetX.value = 0;
		offsetY.value = 0;
		isDragging.value = false;
		isTouchDragging.value = false;

		try {
			// 检查是否是TIF格式
			if (url.value.toLowerCase().endsWith('.tif') || url.value.toLowerCase().endsWith('.tiff')) {
				// 使用fetch获取TIF文件
				const response = await fetch(url.value);
				const arrayBuffer = await response.arrayBuffer();

				// 使用UTIF处理TIF文件
				const ifds = UTIF.decode(arrayBuffer);
				UTIF.decodeImage(arrayBuffer, ifds[0]);
				const rgba = UTIF.toRGBA8(ifds[0]);

				// 创建canvas
				const canvas = document.createElement('canvas');
				canvas.width = ifds[0].width;
				canvas.height = ifds[0].height;

				// 将图像数据绘制到canvas
				const ctx = canvas.getContext('2d');
				if (ctx) {
					const imageData = new ImageData(new Uint8ClampedArray(rgba), ifds[0].width, ifds[0].height);
					ctx.putImageData(imageData, 0, 0);
				}

				// 创建新的图片对象
				const img = new Image();
				img.onload = () => {
					image.value = img;
					// 确保图片尺寸已设置
					originalWidth.value = img.width;
					originalHeight.value = img.height;
					// 设置画布中心点
					centerX.value = imageCanvasRef.value.width / 2;
					centerY.value = imageCanvasRef.value.height / 2;
					// 计算初始缩放比例
					const scaleToWidth = imageCanvasRef.value.width / originalWidth.value;
					const scaleToHeight = imageCanvasRef.value.height / originalHeight.value;
					scale.value = Math.min(scaleToWidth, scaleToHeight) * 1.5; // 使用0.4作为基础缩放因子
					// 更新画布
					updateCanvas();
					imageLoading.value = false;
				};
				img.src = canvas.toDataURL('image/png');
			} else {
				// 非TIF格式使用原有逻辑
				const img = new Image();
				img.onload = () => {
					image.value = img;
					// 确保图片尺寸已设置
					originalWidth.value = img.width;
					originalHeight.value = img.height;
					// 设置画布中心点
					centerX.value = imageCanvasRef.value.width / 2;
					centerY.value = imageCanvasRef.value.height / 2;
					// 更新画布
					updateCanvas();
					imageLoading.value = false;
				};
				img.onerror = () => {
					proxy.$modal.msgError("图片加载失败");
					imageLoading.value = false;
				};
				img.src = url.value;
			}
		} catch (error) {
			console.error('图片加载错误:', error);
			proxy.$modal.msgError("图片加载失败");
			imageLoading.value = false;
		}
	}

	// 添加预加载队列处理函数
	const processPreloadQueue = async () => {
		if (isPreloading.value || preloadQueue.value.size === 0) return;

		isPreloading.value = true;
		const urls = Array.from(preloadQueue.value);
		preloadQueue.value.clear();

		try {
			await Promise.all(urls.map(url => preloadImage(url)));
		} catch (error) {
			console.error('预加载失败:', error);
		} finally {
			isPreloading.value = false;
			if (preloadQueue.value.size > 0) {
				processPreloadQueue();
			}
		}
	};

	function updateCanvas() {
		if (!image.value || !image.value.complete) return;

		const ctx = imageCanvasRef.value.getContext("2d", { alpha: false });
		if (!ctx) return;

		// 使用requestAnimationFrame确保在下一帧渲染
		requestAnimationFrame(() => {
			try {
				// 完全清除画布
				ctx.clearRect(0, 0, imageCanvasRef.value.width, imageCanvasRef.value.height);

				// 创建离屏canvas
				const offscreenCanvas = document.createElement('canvas');
				const offscreenCtx = offscreenCanvas.getContext('2d');
				if (!offscreenCtx) return;

				offscreenCanvas.width = originalWidth.value;
				offscreenCanvas.height = originalHeight.value;

				// 在离屏canvas上绘制
				if (image.value) {
					offscreenCtx.drawImage(
						image.value,
						0, 0,
						originalWidth.value,
						originalHeight.value
					);
				}

				// 保存当前状态
				ctx.save();

				// 应用变换
				ctx.translate(centerX.value + offsetX.value, centerY.value + offsetY.value);
				ctx.scale(scale.value, scale.value);
				ctx.filter = `brightness(${brightness.value}%)`;

				// 绘制到主画布
				ctx.drawImage(
					offscreenCanvas,
					-originalWidth.value / 2,
					-originalHeight.value / 2,
					originalWidth.value,
					originalHeight.value
				);

				// 恢复状态
				ctx.restore();
			} catch (error) {
				console.error('Canvas绘制错误:', error);
				// 发生错误时重置画布状态
				resetImage();
			}
		});
	}

	function handleWheel(event: any) {
		event.preventDefault();
		if (event.deltaY < 0) {
			scale.value *= 1.1;
		} else if (event.deltaY > 0) {
			scale.value /= 1.1;
		}
		updateCanvas();
	}

	function resetImage() {
		if (!image.value) return;
		image.value.src = url.value;
		scale.value = 0.4;
		brightness.value = 100;
		offsetX.value = 0;
		offsetY.value = 0;
		setImage();
		updateCanvas();
	}

	function setImage() {
		if (!image.value) return;
		originalWidth.value = image.value.width;
		originalHeight.value = image.value.height;
		// 图片正常比例换算
		const scaleToWidth = imageCanvasRef.value.width / originalWidth.value;
		const scaleToHeight = imageCanvasRef.value.height / originalHeight.value;
		// scale.value = Math.min(scaleToWidth, scaleToHeight);
		originalCenterX.value = imageCanvasRef.value.width / 2;
		originalCenterY.value = imageCanvasRef.value.height / 2;
		centerX.value = originalCenterX.value;
		centerY.value = originalCenterY.value;
	}

	function mouseDownListener(e: any) {
		const rect = imageCanvasRef.value.getBoundingClientRect();
		isDragging.value = true;
		lastX.value = e.clientX - rect.left;
		lastY.value = e.clientY - rect.top;
	}

	function mouseUpListener(e: any) {
		isDragging.value = false;
	}

	function mouseMoveListener(e: any) {
		if (isDragging.value) {
			const rect = imageCanvasRef.value.getBoundingClientRect();
			offsetX.value += (e.clientX - rect.left) - lastX.value;
			offsetY.value += (e.clientY - rect.top) - lastY.value;
			lastX.value = e.clientX - rect.left;
			lastY.value = e.clientY - rect.top;
			updateCanvas();
		}
	}

	// 修改 watchEffect 函数，添加更好的清理机制
	watchEffect((onCleanup) => {
		const canvas = imageCanvasRef.value;
		if (!canvas) return;

		// 添加事件监听
		const eventListeners = {
			wheel: handleWheel,
			mousedown: mouseDownListener,
			mouseup: mouseUpListener,
			mousemove: mouseMoveListener,
			mouseleave: mouseUpListener,
			touchstart: handleTouchStart,
			touchmove: handleTouchMove,
			touchend: handleTouchEnd,
			touchcancel: handleTouchEnd
		};

		Object.entries(eventListeners).forEach(([event, handler]) => {
			canvas.addEventListener(event, handler, { passive: false });
		});

		// 清理函数
		onCleanup(() => {
			Object.entries(eventListeners).forEach(([event, handler]) => {
				canvas.removeEventListener(event, handler);
			});
		});
	});

	//查询详情
	const drawerDetails = async (param: any) => {
		dynamicValidateForm.markReviewDetails = [];
		dynamicValidateForm.remark = "";
		loading.value = true;
		await markDetails(param).then((res: any) => {
			if (res.code === 200 && res.data != null) {
				let arkReviewDetailsVO = res.data;
				if (arkReviewDetailsVO.length > 0) {
					dynamicValidateForm.remark = arkReviewDetailsVO[0].remark;
					arkReviewDetailsVO.forEach((arkReviewDetailsVO: any) => {
						dynamicValidateForm.markReviewDetails.push({
							markReviewId: arkReviewDetailsVO.markReviewId,
							annotationMethod: arkReviewDetailsVO.annotationMethod,
							fileId: arkReviewDetailsVO.fileId,
							karyotype: arkReviewDetailsVO.karyotype,
							titer: arkReviewDetailsVO.titer,
							remark: arkReviewDetailsVO.remark,
						})
					})
				}
				if (param.annotationMethod == "ONE_CHECK") {
					twoQualitativeShow.value = true;
					dynamicValidateForm.selectedOneType = res.data[0] != null ? res.data[0].oneCheckType?.toUpperCase() : null;
				} else if (param.annotationMethod == "TWO_CHECK") {
					threeQualitativeShow.value = true;
					dynamicValidateForm.selectedTwoType = res.data[0] != null ? res.data[0].twoCheckType?.toUpperCase() : null;
				} else {
					twoQualitativeShow.value = false;
					threeQualitativeShow.value = false;
				}
				loading.value = false;
			}
		});
	};

	const activeName = ref<any>("one");
	const onChangeChecked = async (tab: TabsPaneContext, event: Event) => {
		if (fileId.value == undefined || fileId.value.length === 0) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}
		let param = {
			fileId: fileId.value,
			annotationMethod: "ONE_CHECK",
		}
		switch (tab.props.name) {
			case "one":
				switchValue.value = true;
				//阴阳性控制
				if (type.value) {
					await switchButton(type.value);
				}
				await manualAnnotationQuery(fileId.value);
				break;
			case "two"://一级复核
				//复核详情数据
				await drawerDetails(param);
				switchValue.value = false;
				break;
			case "three"://二级复核
				//复核详情数据
				param.annotationMethod = "TWO_CHECK"
				await drawerDetails(param);
				//切换内容
				switchValue.value = false;

				break;
			default:
				break;
		}
		await resultJudgmentQuery(tab.props.name);

	};

	//人工标注下拉选择内容：选择好核型，关闭下拉，展开滴度
	function handleDomainsChange(index: any) {
		nextTick(() => {
			if (proxy.$refs[`treeDomains_${index}`] && proxy.$refs[`treeDomains_${index}`][0]) {
				proxy.$refs[`treeDomains_${index}`][0].blur();
				// 延迟一点再聚焦滴度下拉框，避免连续闪烁
				setTimeout(() => {
					// 只有在手动添加时才自动展开滴度下拉框
					if (dynamicValidateForm.domains[index] &&
						dynamicValidateForm.domains[index].manualAnnotationId == "" &&
						!dynamicValidateForm.domains[index].titer) {
						if (proxy.$refs[`titerDomains_${index}`] && proxy.$refs[`titerDomains_${index}`][0]) {
							proxy.$refs[`titerDomains_${index}`][0].focus();
						}
					}
				}, 50);
			}
		})
	}
	//复核下拉选择内容：选择好核型，关闭下拉，展开滴度
	function handleMarkReviewChange(index: any) {
		nextTick(() => {
			if (proxy.$refs[`treeMarkReview_${index}`] && proxy.$refs[`treeMarkReview_${index}`][0]) {
				proxy.$refs[`treeMarkReview_${index}`][0].blur();
				// 延迟一点再聚焦滴度下拉框，避免连续闪烁
				setTimeout(() => {
					// 只有在手动添加时才自动展开滴度下拉框
					if (dynamicValidateForm.markReviewDetails[index] &&
						dynamicValidateForm.markReviewDetails[index].markReviewId == "" &&
						!dynamicValidateForm.markReviewDetails[index].titer) {
						if (proxy.$refs[`titerMarkReview_${index}`] && proxy.$refs[`titerMarkReview_${index}`][0]) {
							proxy.$refs[`titerMarkReview_${index}`][0].focus();
						}
					}
				}, 50);
			}
		})
	}

	function handleMarkReviewChangeThree(index: any) {
		nextTick(() => {
			if (proxy.$refs[`treeMarkReviewThree_${index}`] && proxy.$refs[`treeMarkReviewThree_${index}`][0]) {
				proxy.$refs[`treeMarkReviewThree_${index}`][0].blur();
				// 延迟一点再聚焦滴度下拉框，避免连续闪烁
				setTimeout(() => {
					// 只有在手动添加时才自动展开滴度下拉框
					if (dynamicValidateForm.markReviewDetails[index] &&
						dynamicValidateForm.markReviewDetails[index].markReviewId == "" &&
						!dynamicValidateForm.markReviewDetails[index].titer) {
						if (proxy.$refs[`titerMarkReviewThree_${index}`] && proxy.$refs[`titerMarkReviewThree_${index}`][0]) {
							proxy.$refs[`titerMarkReviewThree_${index}`][0].focus();
						}
					}
				}, 50);
			}
		})
	}


	function resultJudgmentQuery(type: any, showSameOnly: boolean = false) {
		let resultParam = {
			fileId: fileId.value,
			type: "ONE",
			showSameOnly: showSameOnly // 添加参数控制是否只显示相同结果
		}
		switch (type) {
			case "one":
				break;
			case "two":
				resultParam.type = "TWO";
				break;
			case "three":
				resultParam.type = "THREE";
				break;
			default:
				break;
		}
		resultJudgmentFind(resultParam).then((res: any) => {
			if (res.code == 200) {
				resultShow.value = res.data;
			}
		});
	}

	//结果复制
	async function resultCopy(formEl: FormInstance | undefined, type: string) {
		if (fileId.value == undefined || fileId.value.length === 0) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}

		let data = {
			fileId: fileId.value,
			type: "ONE",
			manualAnnotationType: "",
			oneCheckType: "",
			twoCheckType: "",
			isTiter: isTiter.value,
		}

		try {
			// 保存当前分页参数和选中状态
			const currentPageNum = queryParams.value.pageNum;
			const currentPageSize = queryParams.value.pageSize;

			switch (type) {
				case "one":
					data.manualAnnotationType = aiForm.value.predictType?.toUpperCase();
					dynamicValidateForm.selectedType = aiForm.value.predictType?.toUpperCase();
					await copyApi(data, "认可AI结果");
					await getList1();
					// 恢复分页参数
					queryParams.value.pageNum = currentPageNum;
					queryParams.value.pageSize = currentPageSize;
					// 再次刷新列表以应用分页
					await getList1();
					// 执行查询和跳转
					await manualAnnotationQuery(fileId.value);
					await resultJudgmentQuery(type, true);
					oneQualitativeShow.value = true;//定性下拉
					isOnePositive.value = true;
					oneTitle.value = "修改结果";
					oneCopyShow.value = false;
					oneDisabled.value = true;
					break;

				case "two":
					data.type = "TWO";
					data.oneCheckType = aiForm.value.predictType?.toUpperCase();
					dynamicValidateForm.selectedOneType = aiForm.value.predictType?.toUpperCase();
					await copyApi(data, "认可人工标注结果");
					await getList1();
					// 恢复分页参数
					queryParams.value.pageNum = currentPageNum;
					queryParams.value.pageSize = currentPageSize;
					// 再次刷新列表以应用分页
					await getList1();
					await drawerDetails({ fileId: fileId.value, annotationMethod: "ONE_CHECK" });
					await resultJudgmentQuery(type);
					oneQualitativeShow.value = true;//定性下拉
					isOnePositive.value = true;
					oneTitle.value = "修改结果";
					oneCopyShow.value = false;
					oneDisabled.value = true;
					break;

				case "three":
					data.type = "THREE";
					data.twoCheckType = aiForm.value.predictType?.toUpperCase();
					dynamicValidateForm.selectedTwoType = aiForm.value.predictType?.toUpperCase();
					await copyApi(data, "认可一级复核结果");
					await getList1();
					// 恢复分页参数
					queryParams.value.pageNum = currentPageNum;
					queryParams.value.pageSize = currentPageSize;
					// 再次刷新列表以应用分页
					await getList1();
					await drawerDetails({ fileId: fileId.value, annotationMethod: "TWO_CHECK" });
					await resultJudgmentQuery(type);
					threeQualitativeShow.value = true;//定性下拉
					isThreePositive.value = true;
					threeTitle.value = "修改结果";
					oneCopyShow.value = false;
					threeDisabled.value = true;
					break;
			}

			// 检查是否有下一条数据
			// const currentIndex = fileList.value.findIndex((item: any) => item.fileId === fileId.value);
			// if (currentIndex === -1 || currentIndex === fileList.value.length - 1) {
			// 	// 如果没有下一条数据，重置状态
			// 	resetRowState();
			// } else {
			// 	// 有下一条数据，跳转
			// 	await selectNextRow();
			// }
			buttonShow.value = false;//确认、取消
			oneCopyShow.value = false;//认可AI结果

		} catch (error) {
			console.error('操作失败:', error);
			proxy.$modal.msgError("操作失败，请重试");
		}
	}

	/**
	 * 自动选择下一条记录
	 */
	const selectNextRow = async () => {
		if (!fileList.value || fileList.value.length === 0) {
			// 如果没有数据，重置状态
			resetRowState();
			return;
		}

		// 获取当前选中行的索引
		const currentIndex = fileList.value.findIndex((item: any) => item.fileId === fileId.value);
		if (currentIndex === -1) {
			resetRowState();
			return;
		}

		// 获取下一条记录
		const nextIndex = currentIndex + 1;
		if (nextIndex < fileList.value.length) {
			const nextRow = fileList.value[nextIndex];

			// 等待下一个 tick，确保数据已更新
			await nextTick();

			// 设置选中行
			selectedRow.value = nextRow;

			// 设置复选框选中状态
			proxy.setTableRowSelected(pageTableRef, nextRow, true);

			// 等待一段时间确保数据加载完成
			await new Promise(resolve => setTimeout(resolve, 100));

			// 使用 await 等待 rowClick 完成
			await rowClick(nextRow);

			// 再次等待确保所有状态更新完成
			await nextTick();
		} else {
			// 如果是最后一条数据，重置状态
			resetRowState();
		}
	};

	/**
	 * 重置行状态
	 */
	const resetRowState = () => {
		// 重置所有状态变量
		oneQualitativeShow.value = false;
		twoQualitativeShow.value = false;
		threeQualitativeShow.value = false;
		buttonShow.value = false;
		addShow.value = true;
		oneCopyShow.value = true;
		isOnePositive.value = false;
		isTwoPositive.value = false;
		isThreePositive.value = false;
		oneDisabled.value = false;
		twoDisabled.value = false;
		threeDisabled.value = false;
		oneTitle.value = "不认可AI结果";
		twoTitle.value = "不认可标注结果";
		threeTitle.value = "不认可复核结果";

		// 清空数据
		dynamicValidateForm.domains = [];
		dynamicValidateForm.markReviewDetails = [];
		dynamicValidateForm.selectedType = undefined;
		dynamicValidateForm.selectedOneType = undefined;
		dynamicValidateForm.selectedTwoType = undefined;

		// 清空图片相关状态
		url.value = "";
		image.value = null;
		scale.value = 1;
		brightness.value = 100;
		offsetX.value = 0;
		offsetY.value = 0;
		imageInfo.value = "";
		config.value = null;
		qrcodeShow.value = false;
	};

	function copyApi(data: any, message: string) {
		copy(data).then((res: any) => {
			if (res.code == 200) {
				proxy.$modal.msgSuccess(message);
			}
		})
	}

	//标注详情
	async function manualAnnotationQuery(fileId: any) {
		await manualAnnotationDetails(fileId).then((res: any) => {
			if (res.code == 200 && res.data != null && res.data.length > 0) {
				dynamicValidateForm.domains = [];
				dynamicValidateForm.remark = res.data[0] != null ? res.data[0].remark : "";
				res.data.forEach((manualAnnotationDetails: any) => {
					dynamicValidateForm.domains.push({
						manualAnnotationId: manualAnnotationDetails.manualAnnotationId,
						karyotype: manualAnnotationDetails.karyotype,
						fileId: manualAnnotationDetails.fileId,
						titer: manualAnnotationDetails.titer,
					});
				})
				oneQualitativeShow.value = true;
				dynamicValidateForm.selectedType = res.data[0] != null ? res.data[0].manualAnnotationType?.toUpperCase() : null;
			}
		})
	}

	// 处理上传请求
	const handleUploadRequest = async (callback: any) => {
		try {
			isUploading.value = true; // 开始上传时设置为true
			// 执行表单验证
			const isValid = await fileRef.value?.validate();
			callback(isValid); // 将验证结果传回子组件
		} catch (error) {
			console.error('表单验证失败:', error);
			callback(false);
		} finally {
			isUploading.value = false; // 上传完成时设置为false
		}
	};
	interface FileInfo {
		url: string;
		fileName: string;
	}

	const imageUrls = ref<FileInfo[]>([]);
	const loadingDownload = ref<any>(false);
	const downloadedCount = ref(0);
	const totalCount = ref(0);
	//图片下载
	const handleDownload = async () => {
		if (ids.value == undefined || !ids.value.length) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}
		// 初始化状态
		loadingDownload.value = true;
		downloadedCount.value = 0;
		//获取图片url
		await imagesDownload(ids.value).then((res: any) => {
			if (res.code == 200 && res.data != null) {
				imageUrls.value = res.data;
			}
		})
		totalCount.value = imageUrls.value.length;

		const zip = new JSZip();
		const MAX_CONCURRENT = 5; // 最大并发数

		try {
			// 分块处理并发下载
			const chunks = [];
			for (let i = 0; i < imageUrls.value.length; i += MAX_CONCURRENT) {
				chunks.push(imageUrls.value.slice(i, i + MAX_CONCURRENT));
			}
			for (const chunk of chunks) {
				await Promise.all(chunk.map(async (fileInfo) => {
					try {
						const response = await fetchWithRetry(fileInfo.url);
						zip.file(fileInfo.fileName, response);
						downloadedCount.value++;
					} catch (err: any) {
						console.error(`[失败] ${fileInfo.url}: ${err.message}`);
					}
				}));
			}

			// 生成ZIP文件
			const content = await zip.generateAsync({ type: 'blob' });
			FileSaver(content, `images_${Date.now()}.zip`);
		} catch (err) {
			proxy.$modal.msgError("下载失败");
		} finally {
			loadingDownload.value = false;
		}
	};

	// 带重试机制的下载函数
	const fetchWithRetry = async (url: string, retries = 3): Promise<Blob> => {
		for (let i = 0; i < retries; i++) {
			try {
				const response = await fetch(url);
				if (!response.ok) throw new Error(`HTTP ${response.status}`);
				return await response.blob();
			} catch (err) {
				if (i === retries - 1) throw err;
				await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
			}
		}
		throw new Error('Unreachable code'); // 确保所有路径都有返回值
	};

	// 生成唯一文件名
	const generateFilename = (url: any) => {
		const ext = url.split('.').pop().split(/[#?]/)[0] || 'bin';
		return `${Date.now()}_${Math.random().toString(36).slice(2)}.${ext}`;
	};

	// 添加排序处理方法
	const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
		console.log(prop, order);
		queryParams.value.orderByColumn = prop;
		queryParams.value.isAsc = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : undefined;
		getList();
	};
	//一级、二级复核copyAI
	const reviewCopyAI = async (formEl: FormInstance | undefined, type: string) => {
		if (fileId.value == undefined || fileId.value.length === 0) {
			proxy.$modal.msgError("请选择一张图片");
			return;
		}

		let data = {
			fileId: fileId.value,
			type: "",
			oneCheckType: "",
			twoCheckType: "",
			isTiter: isTiter.value,
		}

		try {
			// 保存当前分页参数和选中状态
			const currentPageNum = queryParams.value.pageNum;
			const currentPageSize = queryParams.value.pageSize;

			switch (type) {
				case "two":
					data.type = "TWO";
					data.oneCheckType = aiForm.value.predictType?.toUpperCase();
					dynamicValidateForm.selectedOneType = aiForm.value.predictType?.toUpperCase();
					await handlerCopyAI(data, "认可AI结果");
					await getList1();
					// 恢复分页参数
					queryParams.value.pageNum = currentPageNum;
					queryParams.value.pageSize = currentPageSize;
					// 再次刷新列表以应用分页
					await getList1();
					await drawerDetails({ fileId: fileId.value, annotationMethod: "ONE_CHECK" });
					await resultJudgmentQuery(type, true);
					oneQualitativeShow.value = true;//定性下拉
					isOnePositive.value = true;
					oneTitle.value = "修改结果";
					oneCopyShow.value = false;
					oneDisabled.value = true;
					break;

				case "three":
					data.type = "THREE";
					data.twoCheckType = aiForm.value.predictType?.toUpperCase();
					dynamicValidateForm.selectedTwoType = aiForm.value.predictType?.toUpperCase();
					await handlerCopyAI(data, "认可AI结果");
					await getList1();
					// 恢复分页参数
					queryParams.value.pageNum = currentPageNum;
					queryParams.value.pageSize = currentPageSize;
					// 再次刷新列表以应用分页
					await getList1();
					await drawerDetails({ fileId: fileId.value, annotationMethod: "TWO_CHECK" });
					await resultJudgmentQuery(type, true);
					threeQualitativeShow.value = true;//定性下拉
					isThreePositive.value = true;
					threeTitle.value = "修改结果";
					oneCopyShow.value = false;
					threeDisabled.value = true;
					break;
			}

			// 检查是否有下一条数据
			// const currentIndex = fileList.value.findIndex((item: any) => item.fileId === fileId.value);
			// if (currentIndex === -1 || currentIndex === fileList.value.length - 1) {
			// 	// 如果没有下一条数据，重置状态
			// 	resetRowState();
			// } else {
			// 	// 有下一条数据，跳转
			// 	await selectNextRow();
			// }
			buttonShow.value = false;//确认、取消
			oneCopyShow.value = false;//认可AI结果
		} catch (error) {
			console.error('操作失败:', error);
			proxy.$modal.msgError("操作失败，请重试");
		}
	}
	const handlerCopyAI = (data: any, message: string) => {
		copyAI(data).then((res: any) => {
			if (res.code == 200) {
				proxy.$modal.msgSuccess(message);
			}
		})
	}
	// 添加 newTime 变量用于强制刷新 canvas
	const newTime = ref(Date.now());

	// 包装原有的 rowClick 和 rowDblClick 函数
	const handleRowClick = (row: any) => {
		newTime.value = Date.now();
		rowClick(row);
	};

	const handleRowDblClick = (row: any) => {
		newTime.value = Date.now();
		rowDblClick(row);
	};

	//标注，大众文件状态：人工确认绿色，结果已修改蓝色，AI判读中浅黄色，AI判读失败红色，其它灰色
	const getFileStatus = (status: string) => {
		switch (status) {
			case "MANUAL_CONFIRM":
				return "#0fb90f";//人工确认绿色
			case "MANUAL_ANNOTATION":
			case "RESULT_MODIFIED":
				return "#409EFF";//结果已修改蓝色
			case "AI_INTERPRETATION":
				return "#a79402";//AI判读中浅黄色
			case "AI_INTERPRETATION_FAIL":
				return "#f56c6c";//AI判读失败红色
			default:
				return "#909399";
		}
	}


	// 定义常量
	const MAX_PAGE_SIZE = 10000;

	/**
	 * 处理分页大小变化
	 * @param size 分页大小
	 */
	const handleSizeChange = (size: any) => {
		if (isNaN(size)) { // 处理"All"的情况,此时size会是NaN
			// 选择全部时，设置日期范围为当天
			const today = new Date();
			const startOfDay = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 00:00:00`;
			const endOfDay = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 23:59:59`;

			// 设置开始和结束时间
			queryParams.value.startTime = startOfDay;
			queryParams.value.endTime = endOfDay;

			// 设置分页参数
			queryParams.value.pageNum = 1;
			queryParams.value.pageSize = MAX_PAGE_SIZE;

			// 启用虚拟滚动
			proxy.$nextTick(() => {
				const table = pageTableRef.value;
				if (table) {
					// 设置表格高度为视窗高度的80%
					const viewportHeight = window.innerHeight;
					table.height = `${viewportHeight * 0.8}px`;
				}
			});

			// 直接调用getList，不使用防抖
			getList();
		} else {
			// 禁用虚拟滚动
			proxy.$nextTick(() => {
				const table = pageTableRef.value;
				if (table) {
					table.height = 'auto';
				}
			});

			// 使用防抖优化getList调用
			debounceGetList();
		}
	};

	// 添加防抖的getList函数
	const debounceGetList = debounce(() => {
		getList();
	}, 300);

	// 添加窗口大小变化监听
	onMounted(() => {
		window.addEventListener('resize', handleResize);
	});

	onUnmounted(() => {
		window.removeEventListener('resize', handleResize);
	});

	// 处理窗口大小变化
	const handleResize = debounce(() => {
		if (isNaN(queryParams.value.pageSize)) {
			const table = pageTableRef.value;
			if (table) {
				const viewportHeight = window.innerHeight;
				table.height = `${viewportHeight * 0.8}px`;
			}
		}
	}, 200);
	/**
	 * 切换滴度模式
	 * 倍比滴度 ⇔ √10滴度
	 */
	const toggleTiterMode = async () => {
		// 先保存新的状态值，避免响应式更新时机问题
		const newTiterValue = !isTiter.value;
		isTiter.value = newTiterValue;

		// 等待下一个tick确保状态更新完成
		await nextTick();

		// 重新加载滴度下拉内容
		const titer = newTiterValue ? "TITER" : "TEN_TITER";
		proxy.getDicts(titer).then((response: any) => {
			if (response.code === 200) {
				titerStatusOptions.value = response.data;
				proxy.$modal.msgSuccess(`已切换至${newTiterValue ? '倍比滴度' : '√10滴度'}模式`);
			}
		});
	};

	/**
	 * 选择滴度模式的处理函数
	 * @param isMultiple 是否为倍比滴度模式
	 */
	const selectTiterMode = (isMultiple: boolean) => {
		// 关闭对话框
		const dialogElement = document.querySelector('.el-overlay.is-message-box');
		if (dialogElement) {
			dialogElement.remove();
		}

		loading.value = true;

		if (isMultiple) {
			// 选择倍比滴度
			isTiter.value = true;
			proxy.getDicts("TITER").then((response: any) => {
				if (response.code === 200) {
					titerStatusOptions.value = response.data;
					proxy.$modal.msgSuccess({
						message: "🎉 已选择倍比滴度模式，开始愉快地工作吧！",
						type: "success",
						duration: 3000,
						showClose: true
					});
				} else {
					proxy.$modal.msgError("滴度数据加载失败，请重试");
				}
			}).catch((error: any) => {
				console.error("加载倍比滴度数据失败:", error);
				proxy.$modal.msgError("加载滴度数据时出现错误");
			}).finally(() => {
				loading.value = false;
			});
		} else {
			// 选择√10滴度
			isTiter.value = false;
			proxy.getDicts("TEN_TITER").then((response: any) => {
				if (response.code === 200) {
					titerStatusOptions.value = response.data;
					proxy.$modal.msgSuccess({
						message: "🎉 已选择√10滴度模式，开始愉快地工作吧！",
						type: "success",
						duration: 3000,
						showClose: true
					});
				} else {
					proxy.$modal.msgError("滴度数据加载失败，请重试");
				}
			}).catch((error: any) => {
				console.error("加载√10滴度数据失败:", error);
				proxy.$modal.msgError("加载滴度数据时出现错误");
			}).finally(() => {
				loading.value = false;
			});
		}
	};

	/**
	 * 显示滴度选择对话框
	 * 每次进入菜单时让用户选择滴度模式
	 */
	const showTiterSelectionDialog = () => {
		// 检测是否为移动端
		const isMobileDevice = window.innerWidth <= 768;
		
		// 移动端优化的点击处理函数
		const handleOptionClick = (selectMultiple: boolean, e: Event) => {
			e.preventDefault();
			e.stopPropagation();
			
			const target = e.currentTarget as HTMLElement;
			
			// 添加点击反馈动画
			target.style.transform = 'scale(0.95)';
			target.style.transition = 'transform 0.1s ease';
			
			// 移动端使用触觉反馈（如果支持）
			if (isMobileDevice && 'vibrate' in navigator) {
				navigator.vibrate(50);
			}
			
			setTimeout(() => {
				target.style.transform = '';
				// 关闭当前对话框
				const dialogElement = document.querySelector('.el-overlay.is-message-box');
				if (dialogElement) {
					dialogElement.remove();
				}
				// 执行滴度选择
				selectTiterMode(selectMultiple);
			}, 100);
		};

		ElMessageBox.confirm(
			h('div', {
				class: `titer-selection-content ${isMobileDevice ? 'mobile' : 'desktop'}`
			}, [
				h('div', {
					class: 'titer-options'
				}, [
					h('div', {
						class: 'titer-option titer-option-primary',
						onClick: (e: Event) => handleOptionClick(true, e)
					}, [
						h('div', { class: 'titer-option-icon' }, '📊'),
						h('div', { class: 'titer-option-title' }, '倍比滴度'),
						h('div', { class: 'titer-option-desc' }, '标准滴度测量模式'),
						h('div', { class: 'titer-option-badge' }, '推荐')
					]),
					h('div', {
						class: 'titer-option titer-option-success',
						onClick: (e: Event) => handleOptionClick(false, e)
					}, [
						h('div', { class: 'titer-option-icon' }, '√'),
						h('div', { class: 'titer-option-title' }, '√10滴度'),
						h('div', { class: 'titer-option-desc' }, '根号10滴度测量模式'),
						h('div', { class: 'titer-option-feature' }, '高精度')
					])
				])
			]),
			'🧪 滴度选择',
			{
				type: 'info',
				showClose: false,
				closeOnClickModal: false,
				closeOnPressEscape: false,
				customClass: `titer-selection-dialog ${isMobileDevice ? 'mobile-dialog' : 'desktop-dialog'}`,
				showConfirmButton: false,
				showCancelButton: false
			}
		);
	};

	/**
	 * 检查某个日期是否有数据
	 */
	const hasDataOnDate = (date: string) => {
		// 确保输入的日期格式正确
		if (!date) {
			return false;
		}

		// 提取纯日期部分（YYYY-MM-DD格式）
		let checkDate = date;
		if (date.includes('T')) {
			checkDate = date.split('T')[0]; // 去掉时间部分
		}
		if (date.includes(' ')) {
			checkDate = date.split(' ')[0]; // 去掉时间部分
		}

		// 检查数组中是否包含这个日期
		const hasData = datesWithData.value.some(dataDate => {
			// 同样处理数据中的日期格式
			let compareDate = dataDate;
			if (dataDate.includes('T')) {
				compareDate = dataDate.split('T')[0];
			}
			if (dataDate.includes(' ')) {
				compareDate = dataDate.split(' ')[0];
			}
			const match = compareDate === checkDate;
			if (match) {
				console.log('✅ 匹配到有数据的日期:', checkDate);
			}
			return match;
		});

		return hasData;
	};

	/**
	 * 处理日历面板变化
	 */
	const handleCalendarChange = async (date: Date) => {
		console.log('📅 日历面板变化:', date);
		calendarValue.value = date;
		await loadDateStatistics(date);

		// 日历变化后重新应用高亮
		setTimeout(() => {
			applyHighlights();
		}, 600);

		// 再次尝试确保高亮显示
		setTimeout(() => {
			applyHighlights();
		}, 1200);
	};

	/**
	 * 加载指定日期的统计数据
	 */
	const loadDateStatistics = async (date?: Date) => {
		try {
			// 使用传入的日期或当前日历值
			const currentDate = date || calendarValue.value;
			const year = currentDate.getFullYear();
			const month = currentDate.getMonth();

			const startDate = `${year}-${String(month + 1).padStart(2, '0')}-01`;
			const lastDay = new Date(year, month + 1, 0);
			const endDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(lastDay.getDate()).padStart(2, '0')}`;

			console.log('📅 加载日期统计:', { year, month: month + 1, startDate, endDate });

			// 先清空数据，显示加载状态
			datesWithData.value = [];

			const response = await getDateStatistics(startDate, endDate);

			// 确保响应数据是数组格式
			let apiData = [];
			if (response && response.data) {
				if (Array.isArray(response.data)) {
					apiData = response.data;
				} else {
					console.warn('⚠️  API返回的数据不是数组格式:', response.data);
				}
			}

			console.log('🔍 原始API数据:', apiData);

			// 检查当前查询的月份是否为6月
			const isJune = (year === 2025 && month + 1 === 6);

			// 如果有API数据就使用，没有就根据月份决定是否使用测试数据
			if (apiData.length > 0) {
				datesWithData.value = apiData.map(dateStr => {
					// 确保日期格式为 YYYY-MM-DD
					if (dateStr.includes('T')) {
						return dateStr.split('T')[0];
					}
					if (dateStr.includes(' ')) {
						return dateStr.split(' ')[0];
					}
					return dateStr;
				});
				console.log('✅ 使用API日期数据:', datesWithData.value);
			} else if (isJune) {
				// 只在6月份时使用测试数据
				const testDates = [
					'2025-06-10',
					'2025-06-11',
					'2025-06-13'
				];
				datesWithData.value = testDates;
				console.log('🔧 6月份使用测试数据:', datesWithData.value);
			} else {
				// 其他月份没有数据
				datesWithData.value = [];
				console.log('📅 当前月份无数据');
			}
		} catch (error) {
			console.error('❌ 加载日期统计失败:', error);

			// 失败时也要根据月份决定
			const currentDate = date || calendarValue.value;
			const year = currentDate.getFullYear();
			const month = currentDate.getMonth();
			const isJune = (year === 2025 && month + 1 === 6);

			if (isJune) {
				// 只在6月份时使用测试数据
				const testDates = [
					'2025-06-10',
					'2025-06-11',
					'2025-06-13'
				];
				datesWithData.value = testDates;
				console.log('🔧 加载失败，6月份使用测试数据:', datesWithData.value);
			} else {
				// 其他月份清空数据
				datesWithData.value = [];
				console.log('🔧 加载失败，当前月份无数据');
			}
		}
	};

	/**
	 * 弹窗显示时加载数据
	 */
	const loadCalendarData = async () => {
		console.log('📅 日历弹窗打开，加载数据');
		console.log('📅 当前日历值:', calendarValue.value);
		await loadDateStatistics(calendarValue.value);

		// 强制触发界面更新
		nextTick(() => {
			console.log('📊 当前有数据的日期:', datesWithData.value);
			console.log('📊 数据类型:', datesWithData.value.length > 0 ? typeof datesWithData.value[0] : 'empty');
			// 强制触发响应式更新
			datesWithData.value = [...datesWithData.value];

			// 手动验证每个有数据的日期
			if (datesWithData.value.length > 0) {
				console.log('🔍 开始验证有数据的日期:');
				datesWithData.value.forEach(date => {
					console.log(`🎯 测试日期 ${date}:`, hasDataOnDate(date));
				});
			}

			// 应用强制高亮确保样式生效
			setTimeout(() => {
				applyHighlights();
				// 添加月份切换按钮的监听器
				addCalendarButtonListeners();
			}, 500);

			// 再次尝试确保高亮显示
			setTimeout(() => {
				applyHighlights();
			}, 1000);
		});
	};

	/**
	 * 添加日历按钮监听器
	 */
	const addCalendarButtonListeners = () => {
		console.log('🎯 添加日历按钮监听器');

		// 查找日历的上一个月和下一个月按钮
		const prevButton = document.querySelector('.el-calendar__button-group .el-button:first-child');
		const nextButton = document.querySelector('.el-calendar__button-group .el-button:last-child');

		if (prevButton) {
			prevButton.addEventListener('click', () => {
				console.log('📅 点击上一个月按钮');
				setTimeout(async () => {
					await loadDateStatistics(calendarValue.value);
					applyHighlights();
				}, 100);
			});
			console.log('✅ 上一个月按钮监听器已添加');
		}

		if (nextButton) {
			nextButton.addEventListener('click', () => {
				console.log('📅 点击下一个月按钮');
				setTimeout(async () => {
					await loadDateStatistics(calendarValue.value);
					applyHighlights();
				}, 100);
			});
			console.log('✅ 下一个月按钮监听器已添加');
		}

		if (!prevButton || !nextButton) {
			console.log('⚠️  未找到日历按钮，稍后重试');
			setTimeout(addCalendarButtonListeners, 500);
		}
	};

	/**
	 * 应用蓝色√标记 - 简单可靠的数据标识
	 */
	const applyHighlights = () => {
		console.log('🎨 开始应用蓝色√标记');

		// 首先清除所有现有的√标记
		const existingMarks = document.querySelectorAll('.data-check-mark');
		existingMarks.forEach(mark => mark.remove());
		console.log(`🧹 清除了 ${existingMarks.length} 个现有标记`);

		// 尝试多种选择器查找日历单元格
		const selectors = [
			'.compact-calendar-cell',
			'.el-calendar-day',
			'.el-calendar-table td',
			'[class*="calendar"]'
		];

		let cells: NodeListOf<Element> | null = null;
		let usedSelector = '';

		for (const selector of selectors) {
			const found = document.querySelectorAll(selector);
			if (found.length > 0) {
				cells = found;
				usedSelector = selector;
				break;
			}
		}

		if (!cells || cells.length === 0) {
			console.log('❌ 未找到日历单元格，重试中...');
			setTimeout(() => applyHighlights(), 500);
			return;
		}

		console.log(`📱 使用选择器 "${usedSelector}" 找到 ${cells.length} 个单元格`);

		const currentDate = calendarValue.value;
		const currentYear = currentDate.getFullYear();
		const currentMonth = currentDate.getMonth() + 1;

		let addedCount = 0;

		cells.forEach((cell: any, index) => {
			// 检查单元格是否属于当前月份（避免上个月和下个月的干扰）
			const hasCurrentClass = cell.classList.contains('current') ||
				cell.classList.contains('is-today') ||
				!cell.classList.contains('prev') &&
				!cell.classList.contains('next') &&
				!cell.classList.contains('is-prev-month') &&
				!cell.classList.contains('is-next-month');

			// 尝试多种方式获取日期文本
			let dayNumber = '';
			const dateText = cell.querySelector('.date-text');

			if (dateText) {
				dayNumber = dateText.textContent?.trim() || '';
			} else {
				// 如果没有 .date-text，尝试直接从单元格获取文本
				dayNumber = cell.textContent?.trim() || '';
				// 提取数字部分
				const match = dayNumber.match(/\d+/);
				if (match) {
					dayNumber = match[0];
				}
			}

			if (dayNumber && !isNaN(Number(dayNumber))) {
				const day = parseInt(dayNumber);

				// 智能判断日期所属月份
				let targetYear = currentYear;
				let targetMonth = currentMonth;

				// 如果是日历开头几个单元格的大数字（如27、28、29、30、31），可能是上个月
				if (index < 10 && day > 20) {
					targetMonth = currentMonth - 1;
					if (targetMonth === 0) {
						targetMonth = 12;
						targetYear = currentYear - 1;
					}
				}
				// 如果是日历末尾几个单元格的小数字（如1、2、3、4、5），可能是下个月
				else if (index > 25 && day < 10) {
					targetMonth = currentMonth + 1;
					if (targetMonth === 13) {
						targetMonth = 1;
						targetYear = currentYear + 1;
					}
				}

				// 只有当前月份的日期才添加标记
				if (targetMonth === currentMonth && targetYear === currentYear) {
					const fullDate = `${targetYear}-${String(targetMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

					// 检查是否有数据
					if (hasDataOnDate(fullDate)) {
						console.log(`🎯 为当前月日期 ${fullDate} (第${index + 1}个单元格) 添加蓝色√标记`);

						// 创建蓝色√标记
						const checkMark = document.createElement('div');
						checkMark.className = 'data-check-mark';
						checkMark.innerHTML = '✓';
						checkMark.style.cssText = `
							position: absolute;
							top: 2px;
							right: 2px;
							background: #409eff;
							color: white;
							width: 16px;
							height: 16px;
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							font-size: 10px;
							font-weight: bold;
							z-index: 100;
							box-shadow: 0 1px 3px rgba(0,0,0,0.3);
						`;

						// 确保单元格有相对定位
						cell.style.position = 'relative';

						// 添加标记到单元格
						cell.appendChild(checkMark);
						addedCount++;

						console.log(`✅ 已为当前月日期 ${fullDate} 添加蓝色√标记`);
					}
				} else {
					console.log(`⚠️  跳过非当前月日期: ${targetYear}-${String(targetMonth).padStart(2, '0')}-${String(day).padStart(2, '0')} (当前月: ${currentYear}-${String(currentMonth).padStart(2, '0')})`);
				}
			}
		});

		console.log(`✅ 蓝色√标记应用完成，共添加了 ${addedCount} 个标记`);
	};

	/**
	 * 强制应用高亮样式 - 直接操作DOM
	 */




	/**
	 * 处理日期点击事件 - 快速设置查询时间范围
	 */
	const handleDateClick = (date: string) => {
		// 设置开始时间为点击的日期
		queryParams.value.startTime = `${date} 00:00`;

		// 触发查询
		handleQuery();

		// 显示提示信息
		proxy.$modal.msgSuccess(`已设置查询开始时间为：${date}`);
	};

	// UI相关的响应式状态和方法
	// 添加控制示例图显示的状态
	const showProportionImage = ref(true);

	// 注意：brightness变量已在图片处理部分定义，这里重新使用

	// 存储定时器引用，用于清理
	const timers = ref<Set<number>>(new Set());

	// 在script setup部分添加
	const showTip = ref(false);

	// 是否显示快捷键引导
	const showGuide = ref(true);

	// 添加移动端检测
	const isMobile = computed(() => {
		return window.innerWidth <= 768;
	});

	// 添加上传状态
	const isUploadingFile = ref(false);

	/**
	 * 显微镜型号选项类型
	 */
	interface ImagingDevice {
		dictValue: string;
		dictLabel: string;
	}

	// 添加过滤后的显微镜型号选项
	const filteredImagingDevice = ref<ImagingDevice[]>([]);

	// 格式化数字，保留2位小数不四舍五入
	const formatNumber = (num: number) => {
		return Math.floor(num * 100) / 100;
	};

	// 切换示例图显示状态的方法
	const toggleProportionImage = () => {
		showProportionImage.value = !showProportionImage.value;
		resetImageWithBrightness();
	};

	// 计算亮度样式
	const canvasStyle = computed(() => {
		return {
			filter: `brightness(${brightness.value}%)`
		};
	});

	// 亮度调整方法
	const adjustBrightness = (increase: boolean) => {
		if (increase) {
			brightness.value = Math.min(brightness.value + 40, 500); // 增加亮度，最大500%
		} else {
			brightness.value = 60; // 还原到原始亮度
		}
		console.log('当前亮度:', brightness.value);
	};

	// 重置图片和亮度
	const resetImageWithBrightness = () => {
		brightness.value = 60; // 使用原有的默认值
		resetImage();
	};

	/**
	 * 计算表格高度，随搜索栏显示/隐藏自适应
	 * @returns {number} 表格高度（单位：px）
	 */
	const tableHeight = computed(() => {
		// 卡片总高度（可根据实际页面调整）
		// 收起搜索栏时，适当增加高度（如+60）
		const cardTotal = showSearch.value ? 605 : 720;
		// 搜索栏高度
		const searchHeight = showSearch.value ? 110 : 0;
		// 按钮组高度
		const buttonGroupHeight = 40;
		// 分页高度
		const paginationHeight = 50;
		// 卡片内padding
		const padding = 40;
		return cardTotal - searchHeight - buttonGroupHeight - paginationHeight - padding;
	});

	/**
	 * 处理试剂厂家变化事件
	 * @param {string} value - 选中的试剂厂家值
	 */
	const handleReagentChange = (value: string) => {
		// 清空显微镜型号选择
		uploadForm.imagingDevice = '';

		if (value) {
			// 根据试剂厂家过滤显微镜型号
			const selectedReagent = reagent_manufacturer.value.find((item: any) => item.dictValue === value);
			if (selectedReagent && !selectedReagent.dictLabel.includes('其他')) {
				filteredImagingDevice.value = imaging_device.value.filter((item: any) =>
					item.dictValue.includes(selectedReagent.dictLabel) || item.dictValue.includes('通用型显微镜')
				);
			}
		} else {
			// 如果没有选择试剂厂家，显示所有显微镜型号
			filteredImagingDevice.value = imaging_device.value.filter((item: any) =>
				item.dictValue.includes('通用型显微镜')
			);
		}
	};

	/**
	 * 高亮有/无子节点的一级核型目录
	 * @param {boolean} visible - 下拉是否显示
	 */
	const highlightFirstLevelWithChildren = (visible: boolean) => {
		if (!visible) return;
		let tryCount = 0;
		const tryAddClass = () => {
			nextTick(() => {
				const dropdowns = document.querySelectorAll('.el-select-dropdown .el-tree');
				dropdowns.forEach(tree => {
					const firstLevelNodes = tree.querySelectorAll(':scope > .el-tree-node');
					firstLevelNodes.forEach(node => {
						const content = node.querySelector('.el-tree-node__content');
						if (content) {
							content.classList.remove('has-children', 'no-children');
							if (node.querySelector('.el-tree-node__children')) {
								content.classList.add('has-children');
							} else {
								content.classList.add('no-children');
							}
						}
					});
				});
				if (dropdowns.length === 0 && tryCount < 5) {
					tryCount++;
					const retryTimer = setTimeout(tryAddClass, 60) as any;
					timers.value.add(retryTimer);
				}
			});
		};
		tryAddClass();
	};

	/**
	 * 关闭快捷键引导
	 */
	const closeGuide = () => {
		showGuide.value = false;
		localStorage.setItem('hasShownGuide', 'true');
	};

	/**
	 * 初始化组件时的设置
	 */
	const initializeComponent = (imageCanvasRef: any, appStore: any) => {
		// 设置菜单状态
		appStore.setSidebarByRoute(window.location.pathname);

		// 初始化canvas
		if (imageCanvasRef.value) {
			const canvas = imageCanvasRef.value;
			const ctx = canvas.getContext('2d');
			if (ctx) {
				// 设置canvas的初始背景为黑色
				ctx.fillStyle = '#000000';
				ctx.fillRect(0, 0, canvas.width, canvas.height);
			}
		}

		// 显示提示10秒
		showTip.value = true;
		const tipTimer = setTimeout(() => {
			showTip.value = false;
			timers.value.delete(tipTimer);
		}, 10000) as any;
		timers.value.add(tipTimer);

		// 检查是否已展示过快捷键引导
		const hasShownGuide = localStorage.getItem('hasShownGuide');
		if (hasShownGuide) {
			showGuide.value = false;
		}

		// 初始化时设置显微镜型号选项
		filteredImagingDevice.value = imaging_device.value;
	};

	/**
	 * 清理组件资源
	 */
	const cleanupComponent = () => {
		// 清理所有定时器
		timers.value.forEach(timer => {
			clearTimeout(timer);
		});
		timers.value.clear();

		// 重置响应式数据，避免在组件卸载后被访问
		showTip.value = false;
		showGuide.value = false;
		brightness.value = 60;
	};

	/**
	 * 包装行点击处理方法，添加亮度重置逻辑
	 */
	const handleRowClickWithBrightness = (row: any) => {
		brightness.value = 60;
		handleRowClick(row);
	};

	/**
	 * 包装行双击处理方法，添加亮度重置逻辑
	 */
	const handleRowDblClickWithBrightness = (row: any) => {
		brightness.value = 60;
		handleRowDblClick(row);
	};

	/**
	 * 重置图片和亮度的简化版本
	 */
	const handleResetImage = () => {
		resetImageWithBrightness();
	};

	// 监听试剂厂家变化
	const stopReagentWatch = watch(() => uploadForm.reagentManufacturer, (newVal) => {
		handleReagentChange(newVal);
	});

	// 在组件挂载后初始化
	onMounted(() => {
		initializeComponent(imageCanvasRef, appStore);
	});

	// 组件卸载时清理资源
	onBeforeUnmount(() => {
		// 停止watch监听器
		stopReagentWatch();

		// 清理canvas相关资源
		if (imageCanvasRef.value) {
			const ctx = imageCanvasRef.value.getContext('2d');
			if (ctx) {
				ctx.clearRect(0, 0, imageCanvasRef.value.width, imageCanvasRef.value.height);
			}
		}

		// 使用统一的清理方法
		cleanupComponent();
	});

	// prettier-ignore
	return {
		loading, single, multiple, open, showSearch, total, fileList, title, typeOptions, dateRange, queryParams, queryFormRef, form, formRef, rules,
		getList, getList1, reset, handleQuery, resetQuery, handleSelectionChange, handleDelete,
		handleExport, handleClearCache, pageTableRef, cleanSelect,
		handleUpload, fileStatusOptions, oneFileStatusOptions, twoFileStatusOptions, files, uploadSuccess, uploadEle, reagent_manufacturer, imaging_device, fileRef, uploadForm, uploadComponent,
		karyotypeOptions, aiFormRef, aiForm, manualAnnotationForm, rowClick, rowDblClick,
		isPrimary, isdanger, isOnePrimary, isOneDanger, isTwoPrimary, isTwoDanger, handleChange, handleOneChange, handleTwoChange, handleMarkReviewChangeThree, oneDisabled, twoDisabled, threeDisabled,
		handleRecognition,
		openView, formDetails,
		dropDownSelect,
		handleDistribution, openDistribution, formDistribution, downReviewersSelect, submitDistribution, distributionRef,
		titerStatusOptions,
		resetImage, imageCanvasRef, width,
		removeDomain, addDomain, submitForm, resetForm, dynamicValidateForm, formItemRef, switchValue, removeMarkReview,
		onChangeChecked, activeName, handleDomainsChange, handleMarkReviewChange,
		oneCheck, twoCheck, config, qrcodeShow, resultShow, resultCopy,
		imageInfo, confirm, handleUploadRequest, public_file_status, isPublic, handleDownload,
		loadingDownload, downloadedCount, totalCount, ids,
		selectedRow, handleSortChange,
		NEGATIVE,
		POSITIVE,
		SUOICIOUS,
		oneQualitativeShow, twoQualitativeShow, threeQualitativeShow, buttonShow, reviewCopyAI,
		addShow,
		oneCopyShow,
		oneTitle, twoTitle, threeTitle, newTime, handleRowClick, handleRowDblClick,
		isUploading,
		getFileStatus,
		handleSizeChange,
		objective, isTiter, toggleTiterMode, showTiterSelectionDialog, selectTiterMode,
		calendarValue, datesWithData, hasDataOnDate, handleCalendarChange, loadDateStatistics,
		loadCalendarData, handleDateClick, applyHighlights, addCalendarButtonListeners,
		// UI相关的新导出
		showProportionImage, brightness, timers, showTip, showGuide, isMobile, isUploadingFile,
		filteredImagingDevice, formatNumber, toggleProportionImage, canvasStyle, adjustBrightness,
		resetImageWithBrightness, tableHeight, handleReagentChange, highlightFirstLevelWithChildren,
		closeGuide, initializeComponent, cleanupComponent, handleRowClickWithBrightness, handleRowDblClickWithBrightness,
		// 新增的组件级别导出
		handleResetImage, appStore
	};
};

/**
 * 根据核型label递归查找karyotypeId
 * @param {string} label - AI预测的核型label
 * @param {Array<{ name: string; karyotypeId: string; child?: any[] }>} options - 核型树数据
 * @returns {string | null} 匹配到的karyotypeId
 */
function findKaryotypeIdByLabel(label: string, options: Array<{ name: string; karyotypeId: string; child?: any[] }>): string | null {
	for (const opt of options) {
		if (opt.name === label) return opt.karyotypeId;
		if (opt.child && Array.isArray(opt.child)) {
			const found: string | null = findKaryotypeIdByLabel(label, opt.child);
			if (found) return found;
		}
	}
	return null;
}

/**
 * 根据滴度label查找dictValue
 * @param {string} label - AI预测的滴度label
 * @param {Array<{ dictLabel: string; dictValue: string }>} options - 滴度字典
 * @returns {string} 匹配到的dictValue
 */
function findTiterValueByLabel(label: string, options: Array<{ dictLabel: string; dictValue: string }>): string {
	for (const opt of options) {
		if (opt.dictLabel === label) return opt.dictValue;
	}
	return "";
}
