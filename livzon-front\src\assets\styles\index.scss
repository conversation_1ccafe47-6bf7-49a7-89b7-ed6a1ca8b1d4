@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './livzon.scss';

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-weight: bold !important;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}


.table_link_btn {
    margin: 0 5px;
    padding: 0 1px 0 1px
}

.table_link_text {
    margin-left: 2px;
}

.el-link-spacing {
    margin-right: 10px;
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
	transition: all 0.5s;
}

.fade-enter-active,
.fade-leave-active {
	transition: all 0.5s;
}

.fade-enter,
.fade-leave-to {
	opacity: 0.1;
	transform: translateY(-20px);
}

.item-search .el-button--primary {
    margin-left: 22px;
}

/**
 * @fileoverview el-tree-select 弹层层级分明样式增强
 * 让每个节点所属的一级节点一目了然
 * 一级节点更突出，二级、三级节点缩进和颜色更明显
 */

/* 一级节点（根节点）样式 */
.el-select-dropdown .el-tree-node.level-1 > .el-tree-node__content {
  background: #d0eaff; /* 更加醒目的蓝色背景 */
  border-left: 6px solid #2196f3; /* 更粗更深的左边框 */
  font-weight: bold;
  color: #174a7c;
  padding-left: 12px !important;
  transition: background 0.2s;
}
.el-select-dropdown .el-tree-node.level-1 > .el-tree-node__content:hover,
.el-select-dropdown .el-tree-node.level-1.is-current > .el-tree-node__content {
  background: #b3dbff !important;
  color: #0d305a;
}

/* 二级节点样式 */
.el-select-dropdown .el-tree-node.level-2 > .el-tree-node__content {
  background: #f0f7ff;
  border-left: 4px solid #90caf9;
  color: #22577a;
  padding-left: 32px !important;
}
.el-select-dropdown .el-tree-node.level-2 > .el-tree-node__content:hover,
.el-select-dropdown .el-tree-node.level-2.is-current > .el-tree-node__content {
  background: #e0f0ff !important;
  color: #174a7c;
}

/* 三级及更深层节点样式 */
.el-select-dropdown .el-tree-node.level-3 > .el-tree-node__content,
.el-select-dropdown .el-tree-node.level-4 > .el-tree-node__content,
.el-select-dropdown .el-tree-node.level-5 > .el-tree-node__content {
  background: #fff;
  border-left: 4px solid #e3e3e3;
  color: #444;
  padding-left: 48px !important;
}
.el-select-dropdown .el-tree-node.level-3 > .el-tree-node__content:hover,
.el-select-dropdown .el-tree-node.level-3.is-current > .el-tree-node__content,
.el-select-dropdown .el-tree-node.level-4 > .el-tree-node__content:hover,
.el-select-dropdown .el-tree-node.level-4.is-current > .el-tree-node__content,
.el-select-dropdown .el-tree-node.level-5 > .el-tree-node__content:hover,
.el-select-dropdown .el-tree-node.level-5.is-current > .el-tree-node__content {
  background: #f5faff !important;
  color: #22577a;
}

// 兼容原有样式，保留原有选择器，便于渐进增强

/**
 * @description Element Plus 顶部导航下拉子菜单现代化风格
 */
.el-menu--popup {
  min-width: 200px !important;
  background: #fff !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  padding: 8px !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  backdrop-filter: blur(8px) !important;
}

.el-menu--popup .el-menu-item {
  /**
   * @description 优化下拉菜单字体为中文优先，提升跨平台美观性
   */
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, 'Helvetica Neue', sans-serif !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  padding: 0 16px !important;
  height: 40px !important;
  line-height: 40px !important;
  color: #1f2937 !important;
  border-radius: 8px !important;
  margin: 2px 4px !important;
  letter-spacing: 0.3px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  background: transparent !important;
  position: relative;
  overflow: hidden;

  .svg-icon {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: #1765d5;
    transform: scaleY(0);
    transition: transform 0.2s ease;
  }
}

.el-menu--popup .el-menu-item:hover {
  background: #f8fafc !important;
  color: #1765d5 !important;
  transform: translateX(2px);
  font-weight: 600 !important;
  
  &::before {
    transform: scaleY(1);
  }
}

.el-menu--popup .el-menu-item.is-active {
  background: #eef2ff !important;
  color: #1765d5 !important;
  font-weight: 700 !important;
  
  &::before {
    transform: scaleY(1);
  }
}

.el-menu--popup .el-menu-item.is-disabled {
  color: #9ca3af !important;
  background: #f9fafb !important;
  cursor: not-allowed !important;
  opacity: 0.8 !important;
}

/**
 * @description 全局菜单激活与悬停样式，确保文字可见
 */
.el-menu .el-menu-item.is-active,
.el-menu .el-menu-item:hover,
.el-menu--popup .el-menu-item.is-active,
.el-menu--popup .el-menu-item:hover {
  color: #1765d5 !important;
  font-weight: 700 !important;
  background: #eef2ff !important;
}