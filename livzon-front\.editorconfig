
# EditorConfig 有助于为不同IDE编辑器上处理同一项目的多个开发人员维护一致的编码风格
# 告诉EditorConfig插件，这是根文件，不用继续往上查找
root = true

# 匹配全部文件
[*]
# 设置字符集
charset = utf-8
# 缩进风格，可选space、tab
indent_style = tab
# 缩进的空格数
indent_size = 4
# 结尾换行符，可选lf、cr、crlf
end_of_line = lf
# 在文件结尾插入新行
insert_final_newline = true
# 去除行首的任意空白字符
trim_trailing_whitespace = true 
# 删除一行中的前后空格
trim_trailing_whitespace = true

# 匹配md结尾的文件
[*.md]
insert_final_newline = false
trim_trailing_whitespace = false
