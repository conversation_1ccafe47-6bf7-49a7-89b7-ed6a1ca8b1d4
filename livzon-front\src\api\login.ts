import request from "@/utils/request";

/**
 * 登录方法
 *
 * @param {object} loginParam 登录参数
 * @returns
 */
export const login = async (loginParam: any) => {
	return await request({
		url: "/login",
		method: "post",
		data: loginParam,
	});
};

/**
 * 角色切换
 *
 * @param {number} roleId 角色id
 * @returns {Promise} 返回切换角色的请求结果
 */
export const switchRole = async (roleId: number) => {
	return await request({
		url: "/system/role/switch",
		method: "post",
		params: { roleId },
	});
};

/**
 * 获取角色集
 *
 * @param {object} loginParam 登录参数
 * @returns
 */
export const roles = async (loginParam: any) => {
	return await request({
		url: "/roles",
		method: "post",
		data: loginParam,
	});
};


/**
 * 获取角色集
 *
 * @param {object} loginParam 登录参数
 * @returns
 */
export const findByRoles = async () => {
	return await request({
		url: "/findByRoles",
		method: "get",
	});
};

/**
 * 获取角色code
 */

export const findRoleCode = async () => {
	return await request({
		url: "/findRoleCode",
		method: "get"
	})
}

/**
 * 获取用户(角色与权限)详细信息
 *
 * @returns
 */
export const getInfo = async (param: any) => {
	return await request({
		url: "/getInfo",
		method: "get",
		params: param,
	});
};


/**
 * 退出
 *
 * @returns
 */
export const logout = async () => {
	return await request({
		url: "/logout",
		method: "post",
	});
};

/**
 * 获取验证码
 *
 * @returns
 */
export const getCodeImg = async () => {
	return await request({
		url: "/captchaImage",
		method: "get",
	});
};

// 注册方法
export const register = async (data: any) => {
	return await request({
		url: "/register",
		headers: {
			isToken: false,
		},
		method: "post",
		data: data,
	});
};

// 发送短信验证码
export const getSmsCode = async (mobile: any) => {

	return await request({
		url: '/sms/code',
		method: 'post',
		params: {
			phoneNumber: mobile
		}
	})
};

// 短信登录方法
export const smsLogin = async (mobile: any, smsCode: any, uuid: any) => {
	const data = {
		phoneNumber: mobile,
		smsCode,
		uuid
	}
	return await request({
		url: '/sms/login',
		method: 'post',
		data: data
	})
};
