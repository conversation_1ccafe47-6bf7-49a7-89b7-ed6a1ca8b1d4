import useUserStore from "@/store/modules/user";
import { useRouter } from "vue-router";
import { getCodeImg, roles } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { ref } from "vue";
import { ElForm } from "element-plus";
import { lodashFunc } from "@/utils/livzon";
//mport { ILoginForm } from "./module/loginForm";

export default () => {
	const loginFormRef = ref<InstanceType<typeof ElForm>>();
	const codeUrl = ref<string>("");
	const loginForm = ref<any>({
		username: "",
		password: "",
		rememberMe: false,
		code: "",
		uuid: "",
		roleId: "",
	});
	//弹窗开关
	const openRoleView = ref<any>(false);
	//角色下拉
	const roleSelect = ref<any>([]);
	//角色
	const formRole = ref<any>();
	//提交
	const submitRole = () => {

	};
	//取消
	const cancel = () => {

	};


	const loginRules = {
		username: [
			{ required: true, trigger: "blur", message: "用户名不能为空" },
		],
		password: [
			{ required: true, trigger: "blur", message: "密码不能为空" },
		],
		code: [
			{ required: true, trigger: "change", message: "验证码不能为空" },
		],
		roleId: [
			{ 
				required: true, 
				trigger: "change", 
				message: "角色不能为空",
				validator: (rule: any, value: any, callback: any) => {
					if (isRole.value && !value) {
						callback(new Error('请选择角色'));
					} else {
						callback();
					}
				}
			},
		],
	};
	// 注册开关
	const register = ref<boolean>(true);
	let loading = ref<boolean>(false);
	const redirect = ref<any>(undefined);

	const userStore = useUserStore();
	const router = useRouter();
	//关闭弹窗
	const cleanSelect = () => {
		formRole.value?.clearSelection();
	};
	const getCodeBase64 = () => {
		getCodeImg().then((res: any) => {
			if (res.code === 200) {
				const data = res.data;
				loginForm.value.uuid = data.uuid;
				const img = data.img;
				if (img.indexOf("data:image") > -1) {
					codeUrl.value = img;
				} else {
					codeUrl.value = "data:image/png;base64," + img;
				}
			}
		});
	};

	// 验证码防抖，设置每700毫秒才能点击一次
	const getCode = lodashFunc(getCodeBase64, 700);
	// 角色防抖，设置每700毫秒才能点击一次
	const findRole = lodashFunc(findByRole, 700);

	const getCookie = () => {
		const username = Cookies.get("username");
		const password = Cookies.get("password");
		const rememberMe = Cookies.get("rememberMe");
		// prettier-ignore
		loginForm.value.username = username === undefined ? loginForm.value.username : username;
		// prettier-ignore
		loginForm.value.password = password === undefined ? loginForm.value.password : decrypt(password) as string;
		// prettier-ignore
		loginForm.value.rememberMe = rememberMe === undefined ? false : Boolean(rememberMe);
	};

	const isRole = ref<boolean>(false);
	/**
	 * 根据用户名和密码查找角色
	 * @param {boolean} isLogin - 是否是登录时的调用
	 * @returns {Promise<void>}
	 */
	async function findByRole(isLogin: boolean = false) {
		// 如果没有记住密码，每次查角色前清空 roleId，防止残留
		if (!loginForm.value.rememberMe && !isLogin) {
			loginForm.value.roleId = "";
		}
		if (!loginForm.value.username || !loginForm.value.password) {
			return;
		}
		await roles(loginForm.value).then((res: any) => {
			if (res.code == 200) {
				roleSelect.value = res.data;
				if (res.data.length > 0) {
					// 只有当用户只有一个角色且是 public 时才隐藏内容
					const hasPublicRole = res.data.some((item: any) => item.roleKey === 'public');
					isRole.value = !(res.data.length === 1 && hasPublicRole);
					// 只在非登录时且用户没有选择角色时才自动选择第一个角色
					if (!isLogin && !loginForm.value.roleId) {
						loginForm.value.roleId = res.data[0].roleId;
					}
				} else {
					isRole.value = true;
				}
			}
		}).catch((error) => {
			console.log("获取角色权限失败", error);
			loading.value = false;
			getCode();
		});
	}
	const handleLogin = () => {
		// 先获取角色信息，传入 true 表示这是登录时的调用
		findByRole(true).then(() => {
			logOn();
		});
	};
	//登录
	function logOn() {
		loginFormRef.value?.validate(async (valid: boolean) => {
			if (valid) {
				loading.value = true;
				localStorage.setItem('username', loginForm.value.username);
				localStorage.setItem('password', encrypt(loginForm.value.password).toString());
				localStorage.setItem('rememberMe', loginForm.value.rememberMe.toString());
				localStorage.setItem('roleId', loginForm.value.roleId.toString());
				if (loginForm.value.rememberMe) {
					// prettier-ignore
					Cookies.set("username", loginForm.value.username, { expires: 30, });
					// prettier-ignore
					Cookies.set("password", encrypt(loginForm.value.password).toString(), { expires: 30, });
					// prettier-ignore
					Cookies.set("rememberMe", loginForm.value.rememberMe.toString(), { expires: 30, });
					Cookies.set("roleId", loginForm.value.roleId.toString(), { expires: 30, });
				} else {
					Cookies.remove("username");
					Cookies.remove("password");
					Cookies.remove("rememberMe");
					Cookies.remove("roleId");
				}
				// prettier-ignore
				await userStore.userLogin(loginForm.value)
					.then(() => {
						router.push({ path: redirect.value || "/" }).catch(() => { });

					})
					.catch((error) => {
						console.log("登录失败", error);
						loading.value = false;
						getCode();
					});
			}
		});
	}

	getCode();
	getCookie();
	// prettier-ignore
	return {
		loginFormRef, loginForm, loginRules, codeUrl, loading, getCode, handleLogin,
		cleanSelect, formRole, roleSelect, submitRole, cancel, openRoleView, findRole,
		register, isRole,
	};
};
