<template>
    <div class="wechat-comment-item" v-if="isMainComment">
        <!-- 主评论（只有 depth=0 时显示） -->
        <div class="main-comment">
            <el-avatar 
                class="avatar" 
                :size="32" 
                :src="comment.headImg" 
                :style="{ backgroundColor: extractColorByName(comment.name) }"
            >
                {{ comment.name ? comment.name.charAt(0) : '?' }}
            </el-avatar>
            <div class="comment-content">
                <div class="comment-header">
                    <span class="author-name">
                        <template v-if="comment.toName">
                            {{ comment.name }}回复{{ comment.toName }}：
                        </template>
                        <template v-else>
                            {{ comment.name }}：
                        </template>
                    </span>
                    <span class="comment-time">{{ formatTime(comment.time) }}</span>
                </div>
                <div class="comment-text" v-html="comment.comment" @click="handleContentClick" :class="{ 'clickable': !isCurrentUserComment }"></div>
            </div>
        </div>

        <!-- 回复列表（微信朋友圈风格：扁平化显示） -->
        <div v-if="hasReplies" class="replies-section">
            <!-- 展开/收起按钮（超过3条回复时显示） -->
            <div class="replies-toggle" v-if="comment.child.length > 3">
                <button class="toggle-btn" @click="toggleReplies">
                    <span v-if="!repliesExpanded">
                        展开{{ comment.child.length }}条回复
                        <i class="el-icon-arrow-down"></i>
                    </span>
                    <span v-else>
                        收起回复
                        <i class="el-icon-arrow-up"></i>
                    </span>
                </button>
            </div>
            
            <!-- 回复列表 -->
            <div class="replies-list">
                <div 
                    v-for="(reply, index) in visibleReplies" 
                    :key="'reply-' + reply.shareId"
                    class="reply-item"
                >
                    <span class="reply-text" @click="handleReplyContentClick(reply)" :class="{ 'clickable': !isReplyFromCurrentUser(reply) }">
                        <span class="reply-author">{{ reply.name.replace(':', '') }}</span>回复<span class="reply-target">{{ reply.toName }}</span>：<span class="reply-content" v-html="reply.comment"></span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
/**
 * @description 微信朋友圈风格评论组件
 * <AUTHOR>
 */
export default {
    name: 'CommentItem',
    props: {
        /**
         * 评论数据
         */
        comment: {
            type: Object,
            required: true
        },
        /**
         * 嵌套深度
         */
        depth: {
            type: Number,
            default: 0
        },
        /**
         * 根据名字生成颜色的函数
         */
        extractColorByName: {
            type: Function,
            required: true
        },
        /**
         * 当前用户名
         */
        currentUserName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            repliesExpanded: false, // 回复展开状态
        }
    },
    computed: {
        /**
         * 是否为主评论（只有 depth=0 的评论才显示完整结构）
         * @returns {boolean}
         */
        isMainComment() {
            return this.depth === 0;
        },
        /**
         * 判断是否为当前用户的评论
         * @returns {boolean} 是否为当前用户的评论
         */
        isCurrentUserComment() {
            return this.currentUserName && 
                   this.currentUserName.trim() !== '' && 
                   this.comment.name === this.currentUserName;
        },
        /**
         * 是否有回复
         * @returns {boolean}
         */
        hasReplies() {
            return this.comment.child && this.comment.child.length > 0;
        },
        /**
         * 可见的回复列表
         * @returns {Array}
         */
        visibleReplies() {
            if (!this.hasReplies) return [];
            
            // 如果回复数量 <= 3，直接显示全部
            if (this.comment.child.length <= 3) {
                return this.comment.child;
            }
            
            // 如果回复数量 > 3
            if (this.repliesExpanded) {
                // 展开状态：显示全部回复
                return this.comment.child;
            } else {
                // 收起状态：只显示前3条
                return this.comment.child.slice(0, 3);
            }
        }
    },
    mounted() {
        // 微信朋友圈风格：超过3条回复时默认收起，否则默认展开
        this.repliesExpanded = this.comment.child && this.comment.child.length <= 3;
    },
    methods: {
        /**
         * 处理主评论回复
         */
        handleReplyClick() {
            if (this.isCurrentUserComment) {
                this.$message({
                    showClose: true,
                    type: 'warning',
                    message: '不能回复自己的评论'
                });
                return;
            }
            
            this.$emit('send-reply', {
                toId: this.comment.shareId,
                toName: this.comment.name
            });
        },
        /**
         * 处理主评论内容点击
         */
        handleContentClick() {
            if (this.isCurrentUserComment) {
                return; // 自己的评论不响应点击
            }
            this.handleReplyClick();
        },
        /**
         * 处理回复中的回复
         * @param {Object} reply - 被回复的回复对象
         */
        handleReplyToReply(reply) {
            if (this.isReplyFromCurrentUser(reply)) {
                this.$message({
                    showClose: true,
                    type: 'warning',
                    message: '不能回复自己的评论'
                });
                return;
            }
            
            // 回复格式：回复的是子回复，但父ID还是主评论的ID
            this.$emit('send-reply', {
                toId: this.comment.shareId, // 父ID是主评论
                toName: reply.name // 被回复的人
            });
        },
        /**
         * 处理回复内容点击
         * @param {Object} reply - 被回复的回复对象
         */
        handleReplyContentClick(reply) {
            if (this.isReplyFromCurrentUser(reply)) {
                return; // 自己的回复不响应点击
            }
            this.handleReplyToReply(reply);
        },
        /**
         * 判断回复是否来自当前用户
         * @param {Object} reply - 回复对象
         * @returns {boolean}
         */
        isReplyFromCurrentUser(reply) {
            return this.currentUserName && 
                   this.currentUserName.trim() !== '' && 
                   reply.name === this.currentUserName;
        },
        /**
         * 切换回复展开/收起状态
         */
        toggleReplies() {
            this.repliesExpanded = !this.repliesExpanded;
        },
        /**
         * 格式化时间显示
         * @param {string} timeStr - 时间字符串
         * @returns {string} 格式化后的时间
         */
        formatTime(timeStr) {
            if (!timeStr) return '';
            
            try {
                const now = new Date();
                let time;
                
                // 尝试多种格式解析
                if (typeof timeStr === 'string') {
                    // 处理日期-时间格式 (2022-01-01 12:00:00)
                    if (/^\d{4}-\d{1,2}-\d{1,2}\s\d{1,2}:\d{1,2}:\d{1,2}$/.test(timeStr)) {
                        time = new Date(timeStr.replace(/\s/, 'T'));
                    } 
                    // 处理ISO格式 (2022-01-01T12:00:00.000Z)
                    else if (/^\d{4}-\d{1,2}-\d{1,2}T\d{1,2}:\d{1,2}:\d{1,2}/.test(timeStr)) {
                        time = new Date(timeStr);
                    }
                    // 处理日期格式 (2022-01-01)
                    else if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(timeStr)) {
                        time = new Date(timeStr);
                    }
                    // 处理时间戳(毫秒)
                    else if (/^\d{13}$/.test(timeStr)) {
                        time = new Date(parseInt(timeStr));
                    }
                    // 处理时间戳(秒)
                    else if (/^\d{10}$/.test(timeStr)) {
                        time = new Date(parseInt(timeStr) * 1000);
                    }
                    // 尝试标准解析
                    else {
                        time = new Date(timeStr);
                    }
                } else if (typeof timeStr === 'number') {
                    // 数字类型时间戳
                    time = timeStr > 10000000000 
                        ? new Date(timeStr) // 毫秒时间戳
                        : new Date(timeStr * 1000); // 秒时间戳
                } else {
                    // 其他情况
                    time = new Date(timeStr);
                }
                
                // 检查日期是否有效
                if (isNaN(time.getTime())) {
                    console.warn('无效的日期格式:', timeStr);
                    return '时间未知';
                }
                
                const diff = now.getTime() - time.getTime();
                
                // 1分钟内
                if (diff < 60 * 1000) {
                    return '刚刚';
                }
                
                // 1小时内
                if (diff < 60 * 60 * 1000) {
                    return Math.floor(diff / (60 * 1000)) + '分钟前';
                }
                
                // 24小时内
                if (diff < 24 * 60 * 60 * 1000) {
                    return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
                }
                
                // 超过24小时，显示日期
                const month = time.getMonth() + 1;
                const day = time.getDate();
                const hour = time.getHours();
                const minute = time.getMinutes();
                
                return `${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            } catch (error) {
                console.error('日期格式化错误:', error);
                return '时间未知';
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.wechat-comment-item {
    margin-bottom: 16px;
    font-size: 14px;
}

/* 主评论样式 */
.main-comment {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.avatar {
    flex-shrink: 0;
    margin-top: 2px;
}

.comment-content {
    flex: 1;
    min-width: 0;
}

.comment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    margin-bottom: 4px;
}

.author-name {
    font-weight: 600;
    color: #576b95;
    font-size: 13px;
}

.comment-time {
    font-size: 12px;
    color: #999;
}

.comment-text {
    color: #333;
    line-height: 1.5;
    margin-bottom: 6px;
    word-break: break-all;
    
    &.clickable {
        cursor: pointer;
        border-radius: 4px;
        padding: 2px 4px;
        margin: -2px -4px 4px -4px;
        transition: background-color 0.2s ease;
        
        &:hover {
            background-color: #f5f5f5;
        }
        
        &:active {
            background-color: #e8e8e8;
        }
    }
    
    :deep(a) {
        color: #576b95;
        text-decoration: none;
    }
    
    :deep(img) {
        max-width: 20px;
        height: 20px;
        vertical-align: middle;
    }
}



/* 回复区域样式 */
.replies-section {
    margin-left: 40px;
    background: #f7f7f7;
    border-radius: 4px;
    padding: 6px 8px;
}

.replies-toggle {
    margin-bottom: 8px;
}

.toggle-btn {
    background: none;
    border: none;
    color: #576b95;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0;
    
    &:hover {
        color: #485a7a;
    }
    
    i {
        font-size: 12px;
    }
}

.replies-list {
    /* 回复列表样式 */
}

.reply-item {
    margin-bottom: 4px;
    line-height: 1.4;
    font-size: 13px;
    
    &:last-child {
        margin-bottom: 0;
    }
}

.reply-text {
    flex: 1;
    word-break: break-all;
    color: #333;
    
    &.clickable {
        cursor: pointer;
        border-radius: 4px;
        padding: 2px 4px;
        margin: -2px -4px;
        transition: background-color 0.2s ease;
        
        &:hover {
            background-color: #e8e8e8;
        }
        
        &:active {
            background-color: #d8d8d8;
        }
    }
}

.reply-author {
    color: #576b95;
    font-weight: 500;
}

.reply-target {
    color: #576b95;
    font-weight: 500;
}

.reply-content {
    color: #333;
    
    :deep(a) {
        color: #576b95;
        text-decoration: none;
    }
    
    :deep(img) {
        max-width: 16px;
        height: 16px;
        vertical-align: middle;
        margin: 0 2px;
    }
}



/* 响应式调整 */
@media (max-width: 480px) {
    .replies-section {
        margin-left: 32px;
        padding: 6px 8px;
    }
    
    .comment-text {
        font-size: 13px;
    }
    
    .reply-item {
        font-size: 12px;
    }
}
</style> 