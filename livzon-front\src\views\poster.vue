<template>
    <div class="container">
        <div class="all-tool">
            <div style="width:100%;height:auto;" class="course-container">
                <div class="course" id="myImage">
                    <div class="img-box">
                        <img style="max-width:100%;height:auto;" :src="coverImg" />
                    </div>
                    <div>
                        <el-form ref="aiFormRef" label-width="70px" :inline="true" :model="aiForm" :rules="rules">
                            <el-row>
                                <el-col :span="14">
                                    <el-form-item class="fromItem" v-if="aiForm.predictType === 'negative'"
                                        label="AI判读:" prop="predictType">
                                        <div class="divItem" style="font-size: 15px;font-weight: bold;color: #67c23a;">
                                            {{ '阴性' }}
                                        </div>
                                    </el-form-item>
                                    <el-form-item class="fromItem" v-else-if="aiForm.predictType === 'positive'"
                                        label="AI判读:" prop="predictType">
                                        <div class="positive" style="font-size: 15px;font-weight: bold;color: #f56c6c;">
                                            {{ '阳性' }}
                                        </div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="10" style="display: flex; justify-content: flex-end;">
                                    <el-form-item class="fromItem">
                                        <vue-qr style="width:3rem;height:3rem;" :text="config.value" :size="170"
                                            :margin="5" class="vue-qr-img">
                                        </vue-qr>
                                    </el-form-item>
                                </el-col>
                                <template v-for="(items, index) in aiForm.aiPredictKaryotypeDetailsVOS">
                                    <el-col :span="14">
                                        <el-form-item class="fromItem" :label="`核型${index + 1 + ':'}`"
                                            prop="predictKaryotype">
                                            <div class="divItem">
                                                {{ items.predictKaryotype }}
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="10">
                                        <el-form-item class="fromItem" :label="`滴度${index + 1 + ':'}`"
                                            prop="predictTiters">
                                            <div class="divItem">
                                                {{ items.predictTiters }}
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                </template>
                            </el-row>
                        </el-form>
                    </div>
                </div>


            </div>
        </div>
        <div class="btn-all" type="button">
            <el-button size="mini" type="primary" @click="saveImage">下载分享图</el-button>
        </div>
        <a id="link"></a>
    </div>
</template>
<script>
import VueQr from 'vue-qr/src/packages/vue-qr.vue' //生成二维码
import html2canvas from "html2canvas";
//api
import { poster } from "@/api/request/admin/tagging";
const baseURL = import.meta.env.VITE_PC_BASE_PATH;
export default {
    components: {
        VueQr
    },
    data() {
        return {
            aiForm: {
                predictType: "",
                type: "",
                aiPredictKaryotypeDetailsVOS: []
            },
            config: {  //二维码参数
                value: baseURL + `/share?fileId=${this.$route.query.fileId}`,//显示的值、跳转的地址
            },
            coverImg: "",
        };
    },
    mounted() {
        this.initPoster(); // 在组件挂载后调用异步方法
    },
    methods: {
        //初始化分享图
        async initPoster() {
            await poster(this.$route.query.fileId).then(response => {
                if (response.code === 200) {
                    let data = response.data;
                    this.coverImg = data.filePath;
                    this.aiForm.predictType = data.predictType;
                    this.aiForm.type = data.type;
                    this.aiForm.aiPredictKaryotypeDetailsVOS = data.aiPredictKaryotypeDetailsVOS;
                }
            });
        },

        saveImage() {
            const isWeixin = /MicroMessenger/i.test(navigator.userAgent);

            html2canvas(document.querySelector("#myImage"), {
                useCORS: true,
                backgroundColor: null,
                scale: 1.5,
                height: 600,
            }).then((canvas) => {
                let num = 1;
                const machineType = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop'
                if (machineType === 'Mobile') {
                    num = 2;
                }

                const image = canvas.toDataURL("image/png", 1);

                if (isWeixin) {
                    // 微信浏览器中，引导用户长按保存
                    const img = new Image();
                    img.src = image;
                    img.style.maxWidth = '95%';
                    img.style.height = 'auto';
                    img.style.margin = '0 auto';
                    img.style.display = 'block';

                    // 创建遮罩层
                    const mask = document.createElement('div');
                    mask.style.position = 'fixed';
                    mask.style.top = '0';
                    mask.style.left = '0';
                    mask.style.right = '0';
                    mask.style.bottom = '0';
                    mask.style.width = '100%';
                    mask.style.height = '100%';
                    mask.style.backgroundColor = 'rgba(0,0,0,0.8)';
                    mask.style.display = 'flex';
                    mask.style.flexDirection = 'column';
                    mask.style.alignItems = 'center';
                    mask.style.justifyContent = 'center';
                    mask.style.zIndex = '9999';

                    // 创建内容容器
                    const container = document.createElement('div');
                    container.style.width = '100%';
                    container.style.display = 'flex';
                    container.style.flexDirection = 'column';
                    container.style.alignItems = 'center';
                    container.style.padding = '0 10px';
                    container.style.boxSizing = 'border-box';

                    // 添加提示文字
                    const tip = document.createElement('div');
                    tip.style.color = '#fff';
                    tip.style.marginTop = '20px';
                    tip.style.fontSize = '16px';
                    tip.style.textAlign = 'center';
                    tip.textContent = '请长按图片保存到相册';

                    container.appendChild(img);
                    container.appendChild(tip);
                    mask.appendChild(container);
                    document.body.appendChild(mask);

                    // 点击遮罩层关闭
                    mask.addEventListener('click', () => {
                        document.body.removeChild(mask);
                    });
                } else if (num === 2) {
                    const blob = this.base64ImgtoFile(image);
                    const blobUrl = window.URL.createObjectURL(blob);
                    const filename = blob.name;
                    const a = document.createElement('a');
                    a.href = blobUrl;
                    a.download = filename;
                    a.click();
                    window.URL.revokeObjectURL(blobUrl);
                } else {
                    var link = document.getElementById("link");
                    link.setAttribute("download", "分享图.png");
                    link.setAttribute("href", image);
                    link.click();
                }
            })
        },

        base64ImgtoFile(dataurl, filename = '分享图' + Date.now()) {
            // 将base64格式分割：['data:image/png;base64','XXXX']
            const arr = dataurl.split(',');
            // .*？ 表示匹配任意字符到下一个符合条件的字符 刚好匹配到：
            // image/png
            const mime = arr[0].match(/:(.*?);/)[1]; // image/png
            // [image,png] 获取图片类型后缀
            const suffix = mime.split('/')[1]; // png
            const bstr = atob(arr[1]); // atob() 方法用于解码使用 base-64 编码的字符串
            let n = bstr.length;
            const u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            return new File([u8arr], `${filename}.${suffix}`, {
                type: mime,
            });
        },

    },

};
</script>
<style lang="less" scoped>
.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    box-sizing: border-box;
    padding: 20px;
    background: #f5f7fa;
}

.all-tool {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    background: #fff;
    box-sizing: border-box;
    border-radius: 8px;
    width: 100%;
    max-width: 600px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    .course-container {
        width: 100%;
        height: auto;
        position: relative;
        padding: 15px;
    }

    .course {
        z-index: 1;
        padding: 15px;
        box-sizing: border-box;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        background: #fff;

        .img-box {
            max-width: 100%;
            height: 280px;
            border-radius: 8px;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}

.btn-all {
    text-align: center;
    margin: 20px auto 0;

    .el-button {
        padding: 10px 25px;
        font-size: 12px;
        border-radius: 4px;
    }
}

.negative,
.positive {
    font-size: 2vw;
    font-weight: bold;
    color: #67c23a;
    border-radius: 4px;
    display: inline-block;
}

.positive {
    color: #f56c6c;
}

.fromItem {
    font-size: 2vw;
    font-weight: bold;
    margin-bottom: 10px;

    :deep(.el-form-item__label) {
        font-weight: bold;
        color: #606266;
    }
}

.divItem {
    font-size: 2vw;
    font-weight: bold;
    color: #409EFF;
    border-radius: 4px;
    display: inline-block;
}

.vue-qr-img {
    border-radius: 4px;
}
</style>